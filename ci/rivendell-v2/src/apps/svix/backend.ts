import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import { Deployment } from "kubernetes-models/apps/v1";
import * as com from "../common";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const name = "svix";
  const commonLabels = { app: name };

  const deploy = new Deployment({
    metadata: {
      name: name,
    },
    spec: {
      replicas: 1,
      selector: {
        matchLabels: commonLabels,
      },
      template: {
        metadata: {
          labels: {
            ...commonLabels,
            "log.anduin.com/loki": "true",
            version: "v2",
          },
        },
        spec: {
          nodeSelector: com.node.nodeDatabase(ctx),
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [{
            name: "svix",
            image: ConfigUtils.getDockerPublicImage(ctx.configs, "svix"),
            env: [
              k8s.envFromValue("WAIT_FOR", "true"),
              k8s.envFromValue("SVIX_ENVIRONMENT", "prod"),
              k8s.envFromValue("SVIX_LOG_LEVEL", "info"),
              k8s.envFromValue("SVIX_LOG_FORMAT", "json"),
              k8s.envFromValue("SVIX_REDIS_DSN", "redis://svix-redis:6379"),
              k8s.envFromValue("SVIX_WHITELABEL_HEADERS", "true"),
              com.env.envFromGondorConfig(
                "SVIX_OPENTELEMETRY_ADDRESS",
                "STARGAZER_TRACING_FULL_COLLECTOR_ENDPOINT",
                true,
              ),
              com.env.envFromGondorConfig(
                "STARGAZER_SERVICES_DEPLOYMENT",
                "STARGAZER_SERVICES_DEPLOYMENT",
              ),
              k8s.envFromValue(
                "SVIX_OPENTELEMETRY_SERVICE_NAME",
                "svix-$(STARGAZER_SERVICES_DEPLOYMENT)",
              ),
              k8s.envFromValue(
                "OTEL_RESOURCE_ATTRIBUTES",
                "deployment.environment=$(STARGAZER_SERVICES_DEPLOYMENT)",
              ),
              com.env.envFromSharedEnvSecret(
                "SVIX_DB_DSN",
                "STARGAZER_SERVICES_SVIX_POSTGRES_URI",
              ),
              com.env.envFromSharedEnvSecret(
                "SVIX_MAIN_SECRET",
                "STARGAZER_SERVICES_SVIX_MAIN_SECRET",
              ),
              com.env.envFromSharedEnvSecret(
                "SVIX_JWT_SECRET",
                "STARGAZER_SERVICES_SVIX_JWT_SECRET",
              ),
            ],
            readinessProbe: {
              httpGet: {
                path: "/api/v1/health/",
                port: "http",
              },
              initialDelaySeconds: 5,
              timeoutSeconds: 30,
            },
            livenessProbe: {
              httpGet: {
                path: "/api/v1/health/",
                port: "http",
              },
              initialDelaySeconds: 120,
              timeoutSeconds: 30,
            },
            ports: [{
              name: "http",
              containerPort: 8071,
            }],
          }],
        },
      },
    },
  });

  const svc = k8s.serviceFromDeployment(deploy, {
    probeTcp: true,
    ports: [{
      name: "http",
      port: 8071,
      protocol: "TCP",
      targetPort: 8071,
    }],
  });

  return [
    deploy,
    svc,
  ];
}
