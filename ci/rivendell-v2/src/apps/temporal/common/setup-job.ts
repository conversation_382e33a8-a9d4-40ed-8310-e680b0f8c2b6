import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as com from "@app/common";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import { Job } from "kubernetes-models/batch/v1";
import * as path from "path";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const name = "temporal-schema-setup";
  const cmName = `${name}-config`;
  const containerName = "auto-setup";

  const cm = await k8s.createConfigMapFromPath(
    cmName,
    path.resolve(__dirname, `../resources/auto-setup.sh`),
  );

  const job = new Job({
    metadata: { name },
    spec: {
      backoffLimit: 3,
      ttlSecondsAfterFinished: 1800,
      template: {
        metadata: { name },
        spec: {
          restartPolicy: "Never",
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [
            {
              name: containerName,
              image: ConfigUtils.getDockerPublicImage(
                ctx.configs,
                "temporalAdminTools",
              ),
              command: ["sh"],
              args: [`/etc/temporal/scripts/auto-setup.sh`],
              env: [
                com.env.envFromContextConfig(
                  ctx,
                  "CASSANDRA_REPLICATION_FACTOR",
                  "temporalCassandraReplicationFactor",
                ),
                com.env.envFromContextConfig(
                  ctx,
                  "ES_ENABLED",
                  "temporalEsEnabled",
                ),
                com.env.envFromContextConfig(ctx, "ES_HOST", "temporalEsHost"),
                com.env.envFromSharedEnvSecret(
                  "POSTGRES_USER",
                  "STARGAZER_SERVICES_SHARED_POSTGRES_USER",
                ),
                com.env.envFromSharedEnvSecret(
                  "POSTGRES_PWD",
                  "STARGAZER_SERVICES_SHARED_POSTGRES_PASSWORD",
                ),
              ],
              resources: com.resources.getResources(
                ctx,
                com.resources.FargateResourceSpecs.S,
              ),
            },
          ],
        },
      },
    },
  });

  com.storage.cmAsVolume(ctx, job.spec!.template.spec!, {
    cmName: cm.metadata!.name!,
    volumeName: "setup-scripts",
    mounts: [
      {
        container: containerName,
        path: "/etc/temporal/scripts/auto-setup.sh",
        storageSubPath: "auto-setup.sh",
      },
    ],
  });

  return [cm, job];
}
