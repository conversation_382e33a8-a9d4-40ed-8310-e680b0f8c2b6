// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.api.serviceaccount.key

import anduin.brienne.portal.RemoveApiKeyParams
import anduin.portal.PortalClient
import design.anduin.components.button.Button
import design.anduin.components.modal.{ModalBody, ModalFooterWCancel}
import design.anduin.components.toast.Toast

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.anduin.stargazer.client.utils.ZIOUtils

private[api] final case class ConfirmRemoveKeyModalBody(keyHash: String, onClose: Boolean => Callback) {
  def apply(): VdomElement = ConfirmRemoveKeyModalBody.component(this)
}

private[api] object ConfirmRemoveKeyModalBody {
  private type Props = ConfirmRemoveKeyModalBody

  private final case class State(
    isRemoving: Boolean = false
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    private def onDelete(props: Props) = {
      val cb = ZIOUtils.toReactCallback(
        PortalClient
          .removeKey(
            RemoveApiKeyParams(props.keyHash)
          )
          .map(
            _.fold(
              ex => scope.modState(_.copy(isRemoving = false), Toast.errorCallback(s"Failed to remove key, ${ex}")),
              _ => scope.modState(_.copy(isRemoving = false), Toast.successCallback("Key removed") >> props.onClose(true))
            )
          )
      )
      scope.modState(
        _.copy(isRemoving = true),
        cb
      )
    }

    def render(props: Props, state: State): VdomElement = {
      React.Fragment(
        ModalBody(
        )(
          <.div(
            "Removing this API key?"
          )
        ),
        ModalFooterWCancel(props.onClose(false)) {
          Button(
            style = Button.Style.Full(
              color = Button.Color.Danger,
              isBusy = state.isRemoving
            ),
            onClick = onDelete(props)
          )("Remove")
        }
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
