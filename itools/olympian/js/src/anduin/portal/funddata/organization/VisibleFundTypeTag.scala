// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.funddata.organization

import com.raquo.laminar.api.L.*
import design.anduin.components.tag.Tag
import design.anduin.components.tag.laminar.TagL
import design.anduin.style.tw.*

import anduin.funddata.endpoint.firm.VisibleFundType

final case class VisibleFundTypeTag(
  visibleFundType: VisibleFundType
) {

  def apply(): HtmlElement = {
    visibleFundType match {
      case VisibleFundType.Production =>
        div(
          tw.mt2,
          maxWidth.px(240),
          TagL(
            label = Val(VisibleFundType.Production.name),
            color = Val(Tag.Bold.Primary)
          )()
        )
      case VisibleFundType.External =>
        div(
          tw.mt2,
          maxWidth.px(240),
          TagL(
            label = Val(VisibleFundType.External.name),
            color = Val(Tag.Light.Primary)
          )()
        )
      case VisibleFundType.Internal =>
        div(
          tw.mt2,
          maxWidth.px(240),
          Tag<PERSON>(
            label = Val(VisibleFundType.Internal.name)
          )()
        )
    }
  }

}
