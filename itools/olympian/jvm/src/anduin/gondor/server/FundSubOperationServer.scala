// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.gondor.server

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration

import sttp.tapir.server.armeria.zio.ArmeriaZioServerInterpreter

import anduin.fundsub.copy.FundSubCopyConfigService
import anduin.fundsub.dashboard.FundSubDashboardAdminService
import anduin.fundsub.duplicateconfig.FundSubDuplicateConfigService
import anduin.fundsub.endpoint.operation.FundSubOperationEndpoints.*
import anduin.fundsub.service.FundSubImportExportService
import anduin.tapir.server.AuthenticatedEndpointServer
import anduin.tapir.server.EndpointServer.TapirServerService
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.authorization.AuthorizationService
import com.anduin.stargazer.service.fundsub.operation.{
  FundSubOperationEntityService,
  FundSubOperationInvestorService,
  FundSubOperationService
}

final class FundSubOperationServer(
  protected val backendConfig: GondorBackendConfig,
  protected val authorizationService: AuthorizationService,
  fundSubOperationService: FundSubOperationService,
  fundSubOperationEntityService: FundSubOperationEntityService,
  fundSubOperationInvestorService: FundSubOperationInvestorService,
  fundSubImportExportService: FundSubImportExportService,
  fundSubDashboardAdminService: FundSubDashboardAdminService,
  fundSubDuplicateConfigService: FundSubDuplicateConfigService,
  copyConfigService: FundSubCopyConfigService,
  override protected val interpreter: ArmeriaZioServerInterpreter[Any]
) extends AuthenticatedEndpointServer {

  private val CheckAdvancedDashboardDataTimeoutSeconds = 300

  val services: List[TapirServerService] = List(
    authRouteForwardError(createFundSub) { (params, ctx) =>
      fundSubOperationService.createFundSub(
        params,
        ctx.actor.userId,
        Some(ctx)
      )
    },
    authRouteForwardError(editFundSub) { (params, ctx) =>
      fundSubOperationService.editFundSub(params, ctx.actor.userId)
    },
    authRouteForwardError(archiveFundSub, timeout = FiniteDuration(60, TimeUnit.SECONDS)) { (params, ctx) =>
      fundSubOperationService.archiveFundSub(params.fundSubId, ctx.actor.userId)
    },
    authRouteForwardError(editFundType) { (params, ctx) =>
      fundSubOperationService.editFundType(
        params.fundSubId,
        params.fundType,
        ctx.actor.userId
      )
    },
    authRouteForwardError(getFormForFundSetup) { (_, ctx) =>
      fundSubOperationService.getFormForFundSetup(ctx.actor.userId)
    },
    authRouteForwardError(getFormVersionForFundSetup) { (params, ctx) =>
      fundSubOperationService.getFormVersionForFundSetup(params.formId, ctx.actor.userId)
    },
    authRouteForwardError(getFormVersionDetail) { (params, ctx) =>
      fundSubOperationService.getFormVersionDetail(
        params.formVersionId,
        params.formFieldMode,
        ctx.actor.userId
      )
    },
    authRouteForwardError(getFormVersionForDashboardConfig) { (params, ctx) =>
      fundSubOperationService.getFormVersionForDashboardConfig(params.formVersionId, ctx.actor.userId)
    },
    authRouteForwardError(syncFormDataToDashboard) { (params, ctx) =>
      fundSubOperationService.syncFormDataToDashboard(
        params.lpId,
        ctx.actor.userId
      )
    },
    authRouteForwardError(
      getFundSubLpInfo,
      timeout = FiniteDuration(120, TimeUnit.SECONDS)
    ) { (params, ctx) =>
      fundSubOperationService.getFundSubLpInfo(
        params.fundSubId,
        ctx.actor.userId,
        params.includeImportIdInfo
      )
    },
    authRouteForwardError(getFundSubTemplateMappingInfo) { (params, ctx) =>
      fundSubOperationService.getFundSubTemplateMappingsInfo(
        params.fundSubId,
        ctx.actor.userId
      )
    },
    authRouteForwardError(updateFormVersionTemplateMappings) { (params, ctx) =>
      fundSubOperationService.updateFormVersionTemplateMappings(
        params.fundSubId,
        params.formVersionId,
        params.importMappingVersionIds,
        params.exportMappingVersionIds,
        ctx.actor.userId
      )
    },
    authRouteForwardError(getRemovedInvestors) { (params, ctx) =>
      fundSubOperationService.getRemovedInvestors(params.fundSubId, ctx.actor.userId)
    },
    authRouteForwardError(getInvestorDebugData) { (params, ctx) =>
      fundSubOperationService.getInvestorDebugData(params.lpId, ctx.actor.userId)
    },
    authRouteForwardError(compareInvestorFormWithNewVersion, timeout = FiniteDuration(150, TimeUnit.SECONDS)) {
      (params, ctx) =>
        fundSubOperationInvestorService.compareInvestorFormWithNewVersion(
          params.fundSubLpId,
          params.gaiaFormUpdateTypeOpt,
          None,
          ctx.actor.userId
        )
    },
    authRouteForwardError(batchCompareInvestorFormWithNewVersion, timeout = FiniteDuration(150, TimeUnit.SECONDS)) {
      (params, ctx) =>
        fundSubOperationInvestorService.batchCompareInvestorFormWithNewVersion(
          params.lpIds,
          params.gaiaFormUpdateTypeOpt,
          params.newGaiaFormVersionIdOpt,
          ctx.actor.userId
        )
    },
    authRouteForwardError(compareInvestorSupportingForm, timeout = FiniteDuration(120, TimeUnit.SECONDS)) {
      (params, ctx) =>
        fundSubOperationInvestorService.compareInvestorSupportingForm(
          params.lpId,
          params.supportingDocId,
          params.gaiaFormOptions,
          params.versionToUpdateOpt,
          ctx.actor.userId
        )
    },
    authRouteForwardError(
      updateFormForInvestor,
      timeout = FiniteDuration(150, TimeUnit.SECONDS)
    ) { (params, ctx) =>
      fundSubOperationInvestorService.updateFormForInvestor(
        params.lpId,
        ctx.actor.userId,
        params.gaiaFormOptions,
        params.newGaiaFormVersionIdOpt
      )
    },
    authRouteForwardError(getInvestorFormUpdateLogs) { (params, ctx) =>
      fundSubOperationService.getInvestorFormUpdateLogs(params.fundSubId, ctx.actor.userId)
    },
    authRouteForwardError(getInvestorFirstFormVersion) { (params, ctx) =>
      fundSubOperationService.getInvestorFirstFormVersion(params.lpId, ctx.actor.userId)
    },
    authRouteForwardError(getTaxFormConfig) { (_, ctx) =>
      fundSubOperationService.getTaxFormConfig(ctx.actor.userId)
    },
    authRouteForwardError(getGlobalTaxFormVersions) { (_, ctx) =>
      fundSubOperationService.getGlobalTaxFormVersions(ctx.actor.userId)
    },
    authRouteForwardError(updateDynamicTaxFormConfig) { (params, ctx) =>
      fundSubOperationService.updateTaxFormConfig(params.formIds, ctx.actor.userId)
    },
    authRouteForwardError(updateTaxFormVersionConfig) { (params, ctx) =>
      fundSubOperationService.updateTaxFormVersionConfig(params.formVersionIds, ctx.actor.userId)
    },
    authRouteForwardError(getFundSupportingForms) { (params, ctx) =>
      fundSubOperationService.getFundSupportingForms(params.fundSubId, ctx.actor.userId)
    },
    authRouteForwardError(anduinAdminPreviewLpFormOutputAndDocuments) { (params, ctx) =>
      fundSubOperationService.anduinAdminPreviewLpFormOutputAndDocuments(
        params.lpId,
        ctx.actor.userId,
        params.shouldGenerateNewFiles
      )
    },
    authRouteForwardError(anduinAdminUpdateLpFormOutputAndDocuments) { (params, ctx) =>
      fundSubOperationService.anduinAdminUpdateLpFormOutputAndDocuments(
        params.lpId,
        params.cleanInfoToUpdateOpt,
        params.signedInfoToUpdateOpt,
        params.countersignedInfoToUpdateOpt,
        ctx.actor.userId
      )
    },
    authRouteForwardError(parseImportSpreadsheet) { (params, ctx) =>
      fundSubImportExportService.parseImportSpreadsheet(params, ctx.actor.userId)
    },
    authRouteForwardError(parseImportCsv) { (params, ctx) =>
      fundSubImportExportService.parseImportCsv(params, ctx.actor.userId)
    },
    authRouteForwardError(getDefaultGaiaImportTemplate) { (params, ctx) =>
      fundSubImportExportService.generateDefaultImportTemplateForNewForm(params, ctx.actor.userId)
    },
    authRouteForwardError(getDefaultGaiaImportTemplateWithLpData) { (params, ctx) =>
      fundSubImportExportService.generateDefaultImportTemplateWithLpData(params, ctx.actor.userId)
    },
    authRouteForwardError(exportLpsTagData) { (params, ctx) =>
      fundSubOperationService.exportLpsTagData(params.fundSubId, ctx.actor.userId)
    },
    authRouteForwardError(getLpsTagData) { (params, ctx) =>
      fundSubOperationService.getLpsTagData(params.fundSubId, ctx.actor.userId)
    },
    authRouteForwardError(updateLpsTagData) { (params, ctx) =>
      fundSubOperationService.updateLpsTagData(
        params.fundSubId,
        params.lpsTagDataToUpdate,
        ctx.actor.userId
      )
    },
    authRouteForwardError(getFundsUsingTaxForm) { (params, ctx) =>
      fundSubOperationService.getFundsUsingTaxForm(params.formIdEither, ctx.actor.userId)
    },
    authRouteForwardError(updateTaxFormForFunds) { (params, ctx) =>
      fundSubOperationService.updateTaxFormForFunds(
        params.formVersionId,
        params.fundSubIds,
        ctx.actor.userId
      )
    },
    authRouteForwardError(getInvestorsUsingTaxForm) { (params, ctx) =>
      fundSubOperationService.getInvestorsUsingTaxForm(
        params.formIdEither,
        params.fundId,
        ctx.actor.userId
      )
    },
    authRouteForwardError(updateInvestorTaxFormVersionForOldForm) { (params, ctx) =>
      fundSubOperationInvestorService.updateInvestorTaxFormVersionForOldForm(
        params.lpId,
        params.lpFormId,
        ctx.actor.userId
      )
    },
    authRouteForwardError(updateInvestorSupportingFormVersion) { (params, ctx) =>
      fundSubOperationInvestorService.updateInvestorSupportingFormVersion(
        params.supportingDocId,
        params.newFormVersionId,
        params.gaiaFormOptions,
        ctx.actor.userId
      )
    },
    authRouteForwardError(
      updateGpSignatureFieldsForInvestor,
      timeout = FiniteDuration(120, TimeUnit.SECONDS)
    ) { (params, ctx) =>
      fundSubOperationInvestorService.updateGpSignatureFieldsForInvestor(params.lpId, ctx.actor.userId)
    },
    authRouteForwardError(getDashboardConfig) { (params, ctx) =>
      fundSubOperationService.getDashboardConfig(params, ctx.actor.userId)
    },
    authRouteForwardError(saveDashboardConfig) { (params, ctx) =>
      fundSubOperationService.saveDashboardConfig(params, ctx.actor.userId)
    },
    authRouteForwardError(
      checkAdvancedDashboardData,
      timeout = FiniteDuration(CheckAdvancedDashboardDataTimeoutSeconds, TimeUnit.SECONDS)
    ) { (params, ctx) =>
      fundSubDashboardAdminService.checkFundData(params, ctx.actor.userId)
    },
    authRouteForwardError(syncAdvancedDashboardOrder) { (params, ctx) =>
      fundSubDashboardAdminService.syncFundOrder(params, ctx.actor.userId)
    },
    authRouteForwardError(compareOrderInfo) { (lpId, ctx) =>
      fundSubDashboardAdminService.compareOrderInfo(lpId, ctx.actor.userId)
    },
    authRouteForwardError(syncAdvancedDashboardFundInfo) { (params, ctx) =>
      fundSubDashboardAdminService.syncFundInfo(params, ctx.actor.userId)
    },
    authRouteForwardError(compareFundInfo) { (fundId, ctx) =>
      fundSubDashboardAdminService.compareFundInfo(fundId, ctx.actor.userId)
    },
    authRouteForwardError(getStandardAliasSupportOfForm) { (formVersionId, ctx) =>
      fundSubOperationService.getStandardAliasSupportOfForm(formVersionId, ctx.actor.userId)
    },
    authRouteForwardError(setCloseFundSub) { (params, ctx) =>
      fundSubOperationService.setCloseFundSub(params, ctx.actor.userId)
    },
    authRouteForwardError(batchMarkAllPagesAsViewed) { (params, ctx) =>
      fundSubOperationService.batchMarkAllPagesAsViewed(params, ctx.actor.userId)
    },
    authRouteForwardError(removeFundSubEnvironment) { (params, ctx) =>
      fundSubOperationService.removeFundSubEnvironment(
        params.fundSubId,
        ctx.actor.userId
      )
    },
    authRouteForwardError(syncAllDashboards) { (_, ctx) =>
      fundSubOperationService.syncAllDashboards(ctx.actor.userId)
    },
    authRouteForwardError(bulkAddFundsToEnvironment) { (params, ctx) =>
      fundSubOperationService.bulkAddFundsToEnvironment(
        params.fundSubIds,
        params.environmentId,
        ctx.actor.userId
      )
    },
    authRouteForwardError(getEmailDefaultTemplates) { (params, ctx) =>
      fundSubOperationService.getEmailDefaultTemplates(params, ctx.actor.userId)
    },
    authRouteForwardError(getFundAdminsNotificationSettings) { (fundId, ctx) =>
      fundSubOperationService.getFundAdminsNotificationSettings(fundId, ctx.actor.userId)
    },
    authRouteForwardError(getFundSubPortalData) { (fundSubId, ctx) =>
      fundSubOperationService.getFundSubPortalData(fundSubId, ctx.actor.userId)
    },
    authRouteForwardError(getFundSubEmailConfig) { (fundSubId, ctx) =>
      fundSubOperationService.getFundSubEmailConfig(fundSubId, ctx.actor.userId)
    },
    authRouteForwardError(updateFundSubEmailConfig) { (params, ctx) =>
      fundSubOperationService.updateFundSubEmailConfig(params, ctx.actor.userId)
    },
    authRouteForwardError(getFundSubStorageIntegrationConfig) { (fundSubId, ctx) =>
      fundSubOperationService.getFundSubStorageIntegrationConfig(fundSubId, ctx.actor.userId)
    },
    authRouteForwardError(updateFundSubStorageIntegrationConfig) { (params, ctx) =>
      fundSubOperationService.updateFundSubStorageIntegrationConfig(params, ctx.actor.userId)
    },
    authRouteForwardError(getSupportingFormConfig) { (params, ctx) =>
      fundSubOperationService.getSupportingFormConfig(params, ctx.actor.userId)
    },
    authRouteForwardError(updateSupportingFormConfig) { (params, ctx) =>
      fundSubOperationService.updateSupportingFormConfig(params, ctx.actor.userId)
    },
    authRouteCatchError(updateInvestorImportId) { (params, ctx) =>
      fundSubOperationService.updateInvestorImportId(params, ctx.actor.userId)
    },
    authRouteCatchError(updateLpLockStatusInfo) { (params, ctx) =>
      fundSubOperationInvestorService.updateLpLockStatusInfo(params, ctx.actor.userId)
    },
    authRouteCatchError(updateLpNoteInfo) { (params, ctx) =>
      fundSubOperationInvestorService.updateLpNoteInfo(params, ctx.actor.userId)
    },
    authRouteForwardError(computeIaTemplateCoverageForForm) { (params, ctx) =>
      fundSubOperationService.computeIaTemplateCoverageForForm(ctx.actor.userId, params).map(_.value)
    },
    authRouteForwardError(getCopyConfigForAdmin) { (params, ctx) =>
      copyConfigService.getCopyConfigForAdmin(params, ctx.actor.userId)
    },
    authRouteForwardError(updateCopyConfig) { (params, ctx) =>
      copyConfigService.updateCopyConfig(
        fundSubId = params.fundSubId,
        additionalCopyConfigMap = params.additionalCopyConfigMap,
        userId = ctx.actor.userId
      )
    },
    authRouteForwardError(toggleCopyConfig) { (fundSubId, ctx) =>
      copyConfigService.toggleCopyConfig(
        fundSubId = fundSubId,
        userId = ctx.actor.userId
      )
    },
    authRouteForwardError(processImportCopyConfig) { (params, ctx) =>
      copyConfigService.processImportCopyConfig(
        params = params,
        actor = ctx.actor.userId
      )
    },
    authRouteForwardError(exportCopyConfig) { (params, ctx) =>
      copyConfigService.exportCopyConfig(
        params = params,
        userId = ctx.actor.userId
      )
    },
    authRouteForwardError(
      endpoint = exportFundComment,
      timeout = FiniteDuration(CheckAdvancedDashboardDataTimeoutSeconds, TimeUnit.SECONDS)
    ) { (params, ctx) =>
      fundSubOperationService.exportFundComment(
        params = params,
        actor = ctx.actor.userId
      )
    },
    authRouteForwardError(
      endpoint = syncCommentTiDbData,
      timeout = FiniteDuration(CheckAdvancedDashboardDataTimeoutSeconds, TimeUnit.SECONDS)
    ) { (fundId, ctx) =>
      fundSubOperationService.syncCommentTiDbData(
        fundId = fundId,
        actor = ctx.actor.userId
      )
    },
    authRouteForwardError(duplicateFundConfig, timeout = FiniteDuration(60, TimeUnit.SECONDS)) { (params, ctx) =>
      fundSubDuplicateConfigService.duplicateFundConfig(
        params = params,
        actor = ctx.actor.userId,
        httpContext = Some(ctx)
      )
    },
    authRouteForwardError(getFundDataToDuplicate) { (params, ctx) =>
      fundSubDuplicateConfigService.getFundDataToDuplicate(
        params = params,
        actor = ctx.actor.userId
      )
    },
    authRouteForwardError(getEntityFundUsers) { (params, ctx) =>
      fundSubOperationEntityService.getFundUsers(params, ctx.actor.userId)
    }
  )

}
