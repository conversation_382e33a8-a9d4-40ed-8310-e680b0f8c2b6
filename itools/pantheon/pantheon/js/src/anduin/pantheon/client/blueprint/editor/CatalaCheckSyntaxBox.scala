// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.pantheon.client.blueprint.editor

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.button.laminar.ButtonL.Color
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.icon.{Icon, IconGlyph}
import design.anduin.components.progress.laminar.BarIndicatorL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import zio.ZIO

import anduin.blueprint.BlueprintInfo
import anduin.blueprint.endpoint.CheckCatalaSyntaxParams
import anduin.blueprint.utils.CatalaUtils.ErrorMessageParser
import anduin.facades.monaco.monacoEditor.mod.ISelection
import anduin.facades.monaco.monacoEditor.mod.editor.IStandaloneCodeEditor
import anduin.forms.client.BlueprintEndpointClient
import anduin.forms.model.blueprint.BlueprintModels.{BaseDocument, BlueprintVersionModel}
import anduin.frontend.AirStreamUtils
import anduin.pantheon.client.blueprint.BlueprintData.CatalaLogicContent
import anduin.pantheon.client.blueprint.editor.CatalaCheckSyntaxBox.CheckSyntaxResponse

case class CatalaCheckSyntaxBox(
  checkSyntaxEventStream: EventStream[Unit],
  catalaFormLogicSignal: Signal[CatalaLogicContent],
  selectedVersionOptSignal: Signal[Option[BlueprintVersionModel]],
  checkSyntaxBusySignal: Signal[Boolean],
  blueprintInfoSignal: Signal[BlueprintInfo],
  onCheckSyntaxBusy: Observer[Boolean],
  withEditorFnObserver: Observer[IStandaloneCodeEditor => Unit]
) {
  private val checkSyntaxResultVar = Var(CheckSyntaxResponse.Empty)
  private val showCheckSyntaxResultVar = Var(false)

  def apply(): HtmlElement = {
    div(
      tw.flexFill,
      child.maybe <-- showCheckSyntaxResultVar.signal.combineWith(checkSyntaxBusySignal).map {
        (showCheckSyntaxResult, checkSyntaxBusy) =>
          Option.when(showCheckSyntaxResult) {
            if (checkSyntaxBusy) {
              div(
                tw.wPc100.textGray6,
                BarIndicatorL()()
              )
            } else {
              div(
                children <-- checkSyntaxResultVar.signal.distinct.map {
                  case CheckSyntaxResponse.Empty => Seq(emptyNode)
                  case result: CheckSyntaxResponse.Result =>
                    Seq(
                      div(
                        tw.flex.itemsCenter.flexNone.justifyBetween.px8,
                        div(
                          tw.flex.flexFill,
                          div(
                            tw.flex.justifyStart.flexFill.px12,
                            div(
                              tw.flex.pt12,
                              result.textColor,
                              IconL(name = Val(result.icon))()
                            )
                          ),
                          div(
                            tw.flex.justifyEnd.flexFill.px12.pt2,
                            div(
                              tw.flex,
                              if (result.isFailed && !result.isDefaultPosition) {
                                ButtonL(
                                  style = ButtonL.Style.Minimal(
                                    color = Color.Primary
                                  ),
                                  onClick = withEditorFnObserver.contramap { _ =>
                                    jumpToLine(result.selection)
                                  }
                                )(
                                  span("Jump to line"),
                                  IconL(name = Val(Icon.Glyph.ChevronRight))()
                                )
                                  .amend(tw.ml4)
                              } else {
                                emptyMod
                              },
                              ButtonL(
                                style = ButtonL.Style.Minimal(
                                  icon = Some(Icon.Glyph.Cross)
                                ),
                                onClick = showCheckSyntaxResultVar.writer.contramap(_ => false)
                              )().amend(tw.pr4)
                            )
                          )
                        )
                      ),
                      div(
                        tw.flex.itemsCenter.flexNone.justifyBetween.px8.overflowYAuto,
                        height := "calc(100% - 40px)",
                        div(
                          tw.flex.pt12,
                          tw.text13.ml4,
                          maxHeight := "200px",
                          result.textColor,
                          pre(result.message)
                        )
                      )
                    )
                }
              )
            }
          }
      },
      checkSyntaxEventStream
        .sample(
          catalaFormLogicSignal,
          selectedVersionOptSignal.map(_.map(_.baseDocuments)),
          blueprintInfoSignal
        )
        .collect { case (formLogic, Some(baseDocuments), blueprintInfo) =>
          (formLogic.content, baseDocuments, blueprintInfo)
        }
        .flatMapSwitch((formLogicContent, baseDocuments, blueprintInfo) =>
          checkSyntax(formLogicContent, baseDocuments, blueprintInfo)
        ) --> Observer.empty
    )
  }

  private def checkSyntax(
    formLogic: String,
    baseDocuments: Seq[BaseDocument],
    blueprintInfo: BlueprintInfo
  ): EventStream[Unit] = {
    AirStreamUtils.taskToStream {
      ZIO.ifZIO(ZIO.succeed {
        formLogic.isEmpty || formLogic
          .replace("```catala", "")
          .replace("```", "")
          .trim
          .isEmpty
      })(
        onTrue = ZIO.attempt(Toast.error(s"The blueprint is empty, could not check syntax!")),
        onFalse = (ZIO.attempt {
          showCheckSyntaxResultVar.set(true)
          onCheckSyntaxBusy.onNext(true)
        } *>
          BlueprintEndpointClient
            .checkCatalaSyntax(CheckCatalaSyntaxParams(formLogic, baseDocuments, blueprintInfo))
            .flatMap(ZIO.fromEither)
            .tapError { err =>
              ZIO.attempt {
                Toast.error(s"Could not check blueprint syntax. Error: $err")
              }
            }
            .fold(
              _ => showCheckSyntaxResultVar.set(false),
              resp => {
                handleCheckSyntaxResponse(resp.error, resp.exitCode)
              }
            )).onExit(_ => ZIO.attempt(onCheckSyntaxBusy.onNext(false)).ignore)
      )
    }
  }

  private def handleCheckSyntaxResponse(responseMessage: String, exitCode: Int) = {
    val response = if (exitCode == 0) {
      CheckSyntaxResponse.Result(
        isFailed = false,
        icon = IconGlyph.Check,
        textColor = tw.textSuccess4,
        message = CheckSyntaxResponse.getSuccessMessage
      )
    } else {
      val error = ErrorMessageParser.parseErrorMessage(responseMessage)
      error match {
        case Some(err) =>
          CheckSyntaxResponse.Result(
            message = err.message,
            selection = ISelection(
              err.position.endColumn,
              err.position.endLine,
              err.position.startColumn,
              err.position.startLine
            ),
            isDefaultPosition = err.position.isDefault
          )
        case None =>
          CheckSyntaxResponse.Result(
            message = CheckSyntaxResponse.getFailedMessage(responseMessage)
          )
      }
    }
    checkSyntaxResultVar.set(response)
  }

  private def jumpToLine(selection: ISelection): IStandaloneCodeEditor => Unit = editor => {
    editor.revealLinesInCenter(selection.positionLineNumber, selection.selectionStartLineNumber)
    editor.setSelection(selection)
    editor.focus()
  }

}

object CatalaCheckSyntaxBox {

  enum CheckSyntaxResponse {

    case Result(
      message: String,
      isFailed: Boolean = true,
      icon: IconGlyph = IconGlyph.Error,
      textColor: HtmlMod = tw.textDanger4,
      selection: ISelection = ISelection(0, 0, 0, 0),
      isDefaultPosition: Boolean = true
    ) extends CheckSyntaxResponse

    case Empty extends CheckSyntaxResponse

  }

  object CheckSyntaxResponse {
    def getSuccessMessage = s"Congrats, blueprint syntax is valid"

    def getFailedMessage(rawMessage: String) =
      s"""Could not parse the error message:
         |$rawMessage
         |""".stripMargin

  }

}
