// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow

import com.anduin.stargazer.utils.DeploymentUtils
import zio.temporal.worker.ZWorkerOptions

enum WorkflowQueue(val workerOptions: ZWorkerOptions = WorkerOptions.default) {

  def entryName: String =
    productPrefix + "$_workflow" + DeploymentUtils.tenantSuffix

  def queueName: String = entryName

  override def toString: String = entryName

  case FundSubInvitation extends WorkflowQueue(WorkerOptions.FundSub.multipleInvitationWorkerOptions)
  case FundSubBillingExport extends WorkflowQueue()
  case AuditLogExport extends WorkflowQueue()
  case EmailExport extends WorkflowQueue()
  case CommentAnalyticExport extends WorkflowQueue()
  case FundFormFieldSourceAnalyticExport extends WorkflowQueue()
//  case EmailExportForFunds extends ZWorkflowQueue()
  case DataRoomInsightExport extends WorkflowQueue()
  case FundSubIAConfigExport extends WorkflowQueue()
  case InvestorAccessAnalyticExport extends WorkflowQueue()
  //  case ActionEventExport extends ZWorkflowQueue()
  case FormSimilarityAnalytic extends WorkflowQueue()
  case FormAsaAnalytic extends WorkflowQueue()
  case InvestorDataExport extends WorkflowQueue()
  case PrefillAnalytic extends WorkflowQueue()
  case UserDataExport extends WorkflowQueue()
  case FundSubDataImport extends WorkflowQueue(WorkerOptions.FundSub.multipleDataImportWorkerOptions)
  case FundSubDataExport extends WorkflowQueue()
  case FundSubCountersign extends WorkflowQueue(WorkerOptions.FundSub.counterSignWorkerOptions)
  case FundSubBatchAction extends WorkflowQueue(WorkerOptions.FundSub.batchActionWorkerOptions)
  case FundSubUpdateSubscriptionDocReview extends WorkflowQueue()
  case FundSubFormCommentNotification extends WorkflowQueue()
  case FundSubSyncDashboardData extends WorkflowQueue()
  case FundSubSendBaitUploadSupportingDocEvent extends WorkflowQueue()
  case FundSubSendBaitSupportingDocReviewReadyEvent extends WorkflowQueue()
  case SandboxSetupTimeExport extends WorkflowQueue()
  case FundSubDailyFundActivitiesEmail extends WorkflowQueue()
  case FundSubWeeklyFundActivitiesEmail extends WorkflowQueue()
  case FundSubSimulator extends WorkflowQueue()
  case FundSubNewInvestorDailyReportEmail extends WorkflowQueue()
  case FundSubLpSubmissionExport extends WorkflowQueue()
  case FundSubCommentExportWorkflow extends WorkflowQueue()
  case FundSubAutoSaveSubscriptionData extends WorkflowQueue()
  case FundSubDataExtractRequestNotificationEmail extends WorkflowQueue()
  case FundSubPrepareDummyDataForDataExtraction extends WorkflowQueue()
  case FundSubBulkAddEnvironment extends WorkflowQueue()
  case FundSubRemoveEnvironment extends WorkflowQueue()
  case FundSubInviteFundManager extends WorkflowQueue()
  case FundSubMoveFundManager extends WorkflowQueue()
  case FundSubRemoveFundManager extends WorkflowQueue()
  case FundSubAssignInvestor extends WorkflowQueue()
  case FundSubUnassignInvestor extends WorkflowQueue()

  // Public APIs
  case GetApiFileDownload extends WorkflowQueue()
  case GetRequestStatus extends WorkflowQueue()
  case BulkCreateOrders extends WorkflowQueue(WorkerOptions.PublicApi.bulkCreateOrders)
  case ActivateOfflineOrders extends WorkflowQueue()
  case GetFundInvitationLink extends WorkflowQueue()
  case GetOrdersFormData extends WorkflowQueue()
  case CreateWebhookEndpoint extends WorkflowQueue()
  case GetWebhookEndpoint extends WorkflowQueue()
  case GetAllFundWebhooksEndpoint extends WorkflowQueue()
  case RemoveWebhook extends WorkflowQueue()
  case UpdateWebhook extends WorkflowQueue()
  case GetStandardFormFields extends WorkflowQueue()
  case GetStandardFormField extends WorkflowQueue()
  case GenericJsonApi extends WorkflowQueue()
  case TestApi extends WorkflowQueue()
  case DataRoomPublicApi extends WorkflowQueue()

  case InvestorAccessDocumentExpirationNotification extends WorkflowQueue()
  case InvestorAccessUpdateProfileTemplate extends WorkflowQueue()

  // Dms
  case DmsSearchIndex extends WorkflowQueue()
  // Rag
  case RagIndexDocument extends WorkflowQueue()
  case RagUpdateIndex extends WorkflowQueue()

  // Data protection
  case DataProtectionRequest extends WorkflowQueue()

  // Data room
  case DataRoomPostUpload extends WorkflowQueue()
  case DataRoomNewUploadNotification extends WorkflowQueue()
  case DataRoomSimulator extends WorkflowQueue()

  case DataRoomAsyncPublicApi extends WorkflowQueue()

  // Fund data
  case FundDataImportInvestorsFromSubscription extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case FundDataImportInvestorsBySpreadsheet extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case FundDataImportInvestmentEntitiesBySpreadsheet extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case FundDataExportInvestmentEntitiesToSpreadsheet extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case FundDataImportContactsBySpreadsheet extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case FundDataImportRiskAssessmentsBySpreadsheet extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case FundDataComputeProfilesFromSpreadsheet extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case FundDataImportDocumentsBySpreadsheet extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)

  case FundDataImportProfilesFromSpreadsheet extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case FundDataBatchDocumentRequest extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case FundDataMergeInvestors extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case FundDataAssignInvestorsToClientGroup extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case FundDataDeleteClientGroup extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case FundDataInviteMembers extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)

  case FundDataRemoveGuests extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case FundDataInviteGuests extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case FundDataNotifyGuests extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case FundDataModifyGuestsAccessToOpportunityPages extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)

  case FundDataDocumentExpirationNotification extends WorkflowQueue()
  case FundDataAssessmentDueDateNotification extends WorkflowQueue()
  case FundDataFundSubProfileConflictSyncNotification extends WorkflowQueue()
  case FundDataFundSubSync extends WorkflowQueue()
  case FundDataDocumentExpirationWebhook extends WorkflowQueue()

  case FundDataCreateOfflineSubscriptions extends WorkflowQueue()
  case FundDataCreateOfflineTransactions extends WorkflowQueue()
  case FundDataImportOrdersToTransactions extends WorkflowQueue()

  case FundDataExtractInvestmentEntityInfoFromFiles extends WorkflowQueue()

  // Data Extract
  case DataExtractGetUserDocumentsMappingResult extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case DataExtractGenerateDebugReport extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)
  case DataExtractGenerateAnalyticsReport extends WorkflowQueue(WorkerOptions.BatchAction.workerOptions)

  // Data pipeline
  case GreylinCorrection extends WorkflowQueue()

  // Ontology
  case InitOntologySpace extends WorkflowQueue()

  // Autofill
  case ComputeFormMatching extends WorkflowQueue()

  // Annotations / PDF Tool
  case AnalyzePdfAnnotations extends WorkflowQueue()

  // Account
  case AccountToken extends WorkflowQueue()

  case DataIntegration extends WorkflowQueue(WorkerOptions.DataIntegration.workerOptions)

  // Async API
  case AsyncApiFast extends WorkflowQueue(WorkerOptions.fast)
  case AsyncApiMedium extends WorkflowQueue(WorkerOptions.medium)
  case AsyncApiHeavy extends WorkflowQueue(WorkerOptions.heavy)
  case AsyncApiCron extends WorkflowQueue()
  case AsyncApiCronV2 extends WorkflowQueue()

  // Platform
  case TemporalLock extends WorkflowQueue(WorkerOptions.fast)
  case BatchUpload extends WorkflowQueue(ZWorkerOptions.default)

  // Investor Portal
  case InvestorPortal extends WorkflowQueue(WorkerOptions.medium)
  case FundDataDocDistribution extends WorkflowQueue(WorkerOptions.medium)

}
