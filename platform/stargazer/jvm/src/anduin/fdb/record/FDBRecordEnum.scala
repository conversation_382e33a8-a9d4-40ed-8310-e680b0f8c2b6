// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record

enum FDBRecordEnum {

  def entryName = toString + '$' // to compatible with old entryName = this.getClass.getSimpleName

  case UserModel
  case Notification
  case NotificationSpace

  case DataRoomEvent
  case DataRoomState
  case DataRoomModel
  case DataRoomIntegration
  case DataRoomWhiteLabel
  case DataRoomAuthenticationWhiteLabel
  case DataRoomCustomization
  case DataRoomNotificationSettings
  case DataRoomEmail
  case DataRoomHomePage
  case LongRunningTask
  case DataRoomParticipant
  case DataRoomGroupEvent
  case DataRoomGroupState
  case DataRoomSimulator
  case DataRoomEnvironmentPolicyStore

  case DmsSearchEntry

  case DmsChannelModel
  case FileEvent
  case FileState
  case FileVersion
  case PublicFileModel
  case FolderEvent
  case FolderState
  case ShortcutState
  case ShortcutEvent
  case DmsStorageTracking
  case DirectFileUpload
  case PdfFileStorage
  @deprecated case OcrFileStorage
  case WatermarkFileStorageV2
  case FilePageCountV2
  case ThumbnailFileStorage
  case TextractState
  case TextractEvent
  case MergeTextract
  case Inbox
  @deprecated case SafeAnalyzer
  case DataExportJob
  @deprecated case CronJob // Deprecated
  case AnalyticCronJob
  case FundsubLPDashboard
  case FundSubFilterPresetInfo
  case FundsubLpActivityLog
  case FundSubLpTag
  case FundSubAdvisorTagModelStore
  case FundSubNewLpReportLog
  case FSContactGroup
  case FundSubWhiteLabel
  case FundSubExportTemplate
  case FundSubLpFileSetting
  case FundSubAdminNotificationSetting
  case FundSubSupportingDoc
  case ReviewPackage
  case ReviewPackageEmailLog

  case FundSubModelStore
  case FundSubLpModelStore
  case FundSubAdminModelStore

  case FundSubSubmissionVersionState
  case FundSubSubmissionVersionEvent

  case FundSubSignature

  case FundSubBatchInvitation
  case FundSubDataImport
  case FundSubDataExport
  case FundSubBatchAction
  case FundSubRiaGroup

  @deprecated case FundSubAuditLog

  case FundSubAuditLogEvent

  case FundSubGroup
  case PortalFundSubTeam
  case FundSubOpenFgaStoreMapping

  case FundSubInvestorGroup
  case FundSubInvestorGroupRelation

  case FundSubView

  case FundSubMigrationData

  case FundSubCloseData
  case NoteColumn
  case LpNote
  case CustomDataColumn
  case LpCustomData
  case FundSubCopyConfigData
  case FundSubSimulator
  case UserFundSub
  case FundSubSelfServiceExport
  case FundSubEmailTemplateCollection

  case CustomDisclaimerConfigRecord
  case CustomDisclaimerSettingRecordNew
  case CustomDisclaimerUserConsentRecord

  case FundSubEnvironmentPolicyStore
  case FundSubSideLetterVersionRecord
  case FundSubSideLetterWorkflowRecord

  @deprecated case DataExtractRequest
  @deprecated case FundSubDataExtract
  case FundSubDataExtractTestProfile
  case FundSubDataExtractLogRecord
  case FundSubDataExtractRequest
  case FundSubDataExtractMetadata
  case FundSubDataExtractFormData

  // Deprecated
  @deprecated case DisclaimerConfigRecord
  @deprecated case FundSubDisclaimerSettingRecord
  @deprecated case FundSubDisclaimerUserConsentRecord

  case LpFormDataWithNamespaceStore
  case LpFormDataStore
  case SubscriptionSchemaData
  case FundInfoSchemaData
  case EntityLpProfileTemplateStore
  case LpProfileModelStore
  case LpProfileDocumentStore
  case AnduinStandardAliasStore
  case InvestorFormUpdateLog

  case InvestmentEntityModelStore
  case InvestmentEntityUserModelStore
  case InvestmentEntitySubscriptionLinkingStore
  case SubscriptionInvestmentEntityLinkingStore

  case AsaStore

  case ComputeFormMatchingResultStore

  case OntologyAsaStore
  case SharedOntologyAsaStore
  case OntologyAsaMappingStore
  case OntologySpaceMetadataStore

  // Standard Alias Profile (SaProfile)
  case SaProfileStore
  case SaProfileActivityStore
  case MappingDestinationStore
  case SaProfileMappingStore
  case SaProfileMappingActivityStore
  case SaDataTemplateStore
  case SaDataTemplateActivityStore

  case InvestmentEntityAuditLogStore
  case LpProfileAuditLogStore
  case DocumentAuditLogStore
  case InvestmentEntityAuditLogTrackingStore
  case LpProfileAuditLogTrackingStore
  @deprecated case DocumentAuditLogTrackingStore // Deprecated
  case DocumentAuditLogTrackingStoreV2

  case DocumentLogKeyStore
  case InvestorProfileUserAuditLogTrackingStore
  case FormMetaStore
  case FormMappingStore
  case StandardAliasMappingStore
  case FormModelStore
  case FormVersion
  case FormActivity
  case FormTestSuite
  case FormTestScriptModel
  case FormTestScriptContent
  case FormVersionData
  case FormLock
  case AssetLock
  case DataTemplate
  case DataTemplateVersion
  case FormTemplateMappingModel
  case FormTemplateMappingVersion
  case FormTemplateMappingContent
  case FormFolderModelStore
  case FormToolConfigModel
  case AnnotationDocumentModel
  case AnnotationDocumentVersion
  case TextractAnnotationState
  case DigitizationFolderModelStore
  case DigitizationFileModelStore
  case DigitizationTagModelStore
  case DigitizationFileActivityModelStore
  case DigitizationSearchEntry
  case DigitizationUserActivityModelStore
  case FormIntegration
  case BlueprintModel
  case BlueprintVersion

  case ActivityLog

  case Contact
  case ContactGroup
  case ContactMetadataDataTable

  case ProtectedLink
  case OneTimeLink

  case OrgBilling
  case ChangeDataRoomPlanLog
  case ChangeSignaturePlanLog

  case TeamFlowEvent
  case TeamFlowState
  case TeamMemberFlowEvent
  case TeamMemberFlowState

  case IssueTrackerIssue
  case IssueTrackerIssueList
  case IssueTrackerModule
  case IssueTrackerUserModule
  case IssueTrackerChannel
  case IssueTrackerUser
  case IssueTrackerCollabGroup
  case IssueTrackerOfferingAccess
  case IssueTrackerOfferingResource
  case IssueTrackerMatterMapping

  case FormCommentDigest

  case EntityModel
  case EntityWhiteLabel

  case Oauth2Integration

  case Zaps

  case WhitelistDomain

  case Enterprise

  case LinkRestriction
  case OTPAuthentication
  case AuthenticationWhitelabel
  case AccountEnforcement
  case AccountPolicy
  case AccountToken

  // Integ test
  case SampleTracking
  case SampleDataTable
  case SampleDms

  // FormDiff
  case FormDiffJobV1
  case FormDiffJobV2

  // DocRequest
  case DocRequest
  case FormSubmission
  case DocSubmission

  // Advanced dashboard
  case Dashboard

  // Signature app
  case UserSignatureApp

  // User MFA
  case UserMFA

  // Async job
  @deprecated case AsyncJob

  // Email
  case InternalEmail
  case EmailModel
  case UserEmail
  case SesMessage
  case EmailProvider

  // Public API
  case ApiKey
  case ServiceAccount
  @deprecated case DeveloperProfile
  case WebhookEndpoint

  // Portal user
  case PortalUser

  // Review
  case ReviewConfig
  case ReviewStepConfig
  case ReviewFlow
  case ReviewStepEvent
  case SupportingDocReviewConfig
  @deprecated case SupportingDocReviewMapping
  case SupportingDocReviewStatus
  case SubscriptionDocReviewConfig
  case SubscriptionDocReviewMapping

  // Custom domain
  case CustomDomain

  // Environment
  case Environment
  case EnvironmentWhitelabel
  case EnvironmentSSOBinding
  case EnvironmentPolicy

  // Multi region
  case MultiRegion

  // Fund Data
  case FundDataFirm
  case FundDataOpenFgaMapping
  case FundDataRole
  case FundDataGroup
  case FundDataFirmProfile
  case FundDataFirmOrganization
  @deprecated case FundDataFund
  case FundDataFundV2
  case FundDataDataRoom
  case FundDataFundGroup
  case FundDataFundSubscription
  @deprecated case FundDataFundLinkSubscription
  @deprecated case FundDataFundLinkDataRoom
  case TaskRequest
  case TaskRequestItem
  case FundDataWhiteLabel
  case FundDataEmail
  case FundDataEmailTemplate
  case FundDataEnvironmentPolicyStore

  case FundDataClientGroup
  case FundDataInvestor
  case FundDataInvestmentEntity
  case FundDataInvestmentEntitySubscription
  case FundDataInvestmentEntityContact
  case FundDataInvestmentEntityProfile
  case FundDataInvestmentEntityProfileHistory
  case FundDataInvestmentEntityDocumentV2
  case FundDataInvestmentEntityDocumentTextract
  case FundDataInvestmentEntityAssessment
  case FundDataAuditLogEvent
  @deprecated case FundDataTag
  case FundDataFirmDefaultSetting
  @deprecated case FundDataInvestmentEntityDocumentsRequest
  case FundDataRequest
  case FundDataRequestConfig
  case FundDataFundSubSyncConfig
  case FundDataFundSubProfileSyncConflict
  case FundDataProfileConflict
  case FundDataFundSubSyncEvent
  case FundDataFeatureSwitch
  case FundDataExpirationDateConfig
  case FundDataLlmConfig
  @deprecated case FundDataFirmPortalUser
  case FundDataGuest
  case FundDataFirmGuest
  case FundDataNote
  case FundDataIdConfig

  // Contact
  case FundDataFirmCommunication
  case FundDataInvestmentEntityCommunication
  case FundDataFundLegalEntityCommunication
  case FundDataContactCommunication
  case FundDataFirmContact
  case FundDataInvestmentEntityContactV2
  case FundDataInvestorContact
  case FundLegalEntityCommunicationMapping

  // Deprecated
  @deprecated case FundDataInvestmentEntityDocument
  @deprecated case FundDataTransaction

  // Landing page
  case FundDataPortalHomePage
  case FundDataOpportunityPage
  case FundDataLandingPageConfig
  case FundDataVehiclePage
  case FundDataLandingPageLink
  case FundDataPortalLpDocumentPage

  // Fund
  case FundLegalEntity
  case FundFamily
  case FundShareClass
  case FundInvestment
  case FundTransaction
  case FundTransactionDocument

  case FundDataPortal

  // Comment
  case CommentThreadIndex
  case CommentMentionIndexModel
  case CommentAssignmentIndexModel
  case CommentExportTask

  // Asa analytic caching store
  @deprecated case AsaAnalyticCache
  @deprecated case FormSheetData

  case Amendment

  // FundSub events for digest email
  case OrderActivity
  case FundEmailLog

  // User tracking
  case FundSubUserTracking
  case DataRoomUserTracking

  // User Signature
  case UserSignature
  case UserESignature

  // User onboarding
  case FundSubUserOnboarding

  // Single user invitation
  case FundSubSingleUserInvitationLink

  // Batch Action
  case BatchAction

  // Data Extract
  case DataExtractProjectModel
  case DataExtractUserDocument
  case DataExtractProjectItem
  case DataExtractExtractedFormData

  // Data Pipeline
  case GreylinDataCorrectionEvent

  // Tag
  case TagItem
  case TagList
  case ObjectTags

  // Web content
  case WebContent

  // Integration Platform
  case PrismaticCustomer
  case IntegrationModel
  case IntegPlatformEntity
  case IntegPlatformIntegrationCredentials
  case IntegPlatformInstance

  // aml check
  case AmlCheck

  // ria
  case RiaEntity
  case RiaFundGroup
  case RiaEntityEmailDomainRelation
  case RiaEntityLinkedFund
  case RiaEntityUserRelation
  case RiaAdvisorOrderRelation
  case RiaEntityOrderRelation
  case FundAdvisor
  case RiaIndividualAdvisor
  case RiaEntityOpenFgaMapping

  case LpFormFieldSourceAnalyticsCache

  // Id Mapping
  case IdMapping

  // Cue Module
  case CueModuleModel
  case CueModuleVersion
  case CueTableData
  case CueTableMetadata

  // Async API
  case AsyncApiState
  case AsyncApiData
  case AsyncApiEvent
  case AsyncApiExecution

  // Async API v2
  case AsyncApiStateV2
  case AsyncApiEventV2

  // InvestorPortal
  case InvestorPortalState
  case PortalInstance
  case InvestorPortalFile
  case PortalDocDistributionState
  case PortalDocDistributionEvent

  case RevertOTPAuthentication
}
