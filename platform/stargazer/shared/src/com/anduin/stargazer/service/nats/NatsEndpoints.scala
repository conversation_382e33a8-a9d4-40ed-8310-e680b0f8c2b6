// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service.nats

import anduin.tapir.AuthenticatedEndpoints
import anduin.tapir.AuthenticatedEndpoints.BaseAuthenticatedEndpoint

import sttp.tapir.*

object NatsEndpoints extends AuthenticatedEndpoints {
  private val NatsPaths = "nats"

  val sessions: BaseAuthenticatedEndpoint[Unit, GeneralNatsApiError, NatsSession] =
    authEndpoint[Unit, GeneralNatsApiError, NatsSession](
      NatsPaths / "sessions"
    )

}
