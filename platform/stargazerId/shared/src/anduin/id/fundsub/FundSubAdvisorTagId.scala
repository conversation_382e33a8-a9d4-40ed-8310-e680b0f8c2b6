// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.id.fundsub

import anduin.id.RadixChildRefinedId
import anduin.id.common.IdPrefix
import anduin.model.DefaultValue
import anduin.model.id.RadixIdDefaultValue
import anduin.radix.RadixChildRefinedCompanion
import anduin.refined.Refined
import scalapb.TypeMapper

final case class FundSubAdvisorTagId(
  parent: FundSubId,
  value: Refined[FundSubAdvisorTagId]
) extends RadixChildRefinedId[FundSubAdvisorTagId, FundSubId]

object FundSubAdvisorTagId extends RadixChildRefinedCompanion[FundSubAdvisorTagId, FundSubId] {
  override val valueLength = 7
  override val valuePrefix: IdPrefix = IdPrefix.FundSubAdvisorTagIdPrefixValue

  override val buildNode: (FundSubId, Refined[FundSubAdvisorTagId]) => FundSubAdvisorTagId = { (parent, value) =>
    FundSubAdvisorTagId(parent, value)
  }

  override given defaultValue: DefaultValue[FundSubAdvisorTagId] = DefaultValue.instance {
    buildNode(FundSubId.defaultValue.get, RadixIdDefaultValue.unsafeDefaultValue)
  }

  given fundSubAdvisorTagIdTypeMapper: TypeMapper[String, FundSubAdvisorTagId] = radixIdMapperImpl(defaultValue.get)
  given fundSubAdvisorTagIdOptTypeMapper: TypeMapper[String, Option[FundSubAdvisorTagId]] = radixIdOptMapperImpl
}
