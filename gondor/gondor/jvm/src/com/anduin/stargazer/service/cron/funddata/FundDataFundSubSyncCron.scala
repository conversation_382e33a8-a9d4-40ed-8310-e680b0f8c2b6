// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service.cron.funddata

import zio.temporal.schedules.ZScheduleSpec

import anduin.funddata.sync.FundDataFundSubSyncAllFirmsWorkflowImpl
import anduin.protobuf.funddata.sync.event.ProcessFundSubSyncAllFirmsParams
import anduin.workflow.cron.TemporalScheduleUtils

final case class FundDataFundSubSyncCron(
  temporalScheduleUtils: TemporalScheduleUtils
) {

  def start: zio.Task[Unit] = {
    temporalScheduleUtils
      .startSchedule(
        "funddata-fundsub-sync-all-firms",
        ZScheduleSpec.cronExpressions("0 4 * * *"), // 04:00 UTC = 00:00 New York
        FundDataFundSubSyncAllFirmsWorkflowImpl.instance,
        workflowRunTimeout = FundDataFundSubSyncAllFirmsWorkflowImpl.workflowRunTimeout
      )(_.processFundSubSyncAllFirms(ProcessFundSubSyncAllFirmsParams()))
      .unit
  }

}
