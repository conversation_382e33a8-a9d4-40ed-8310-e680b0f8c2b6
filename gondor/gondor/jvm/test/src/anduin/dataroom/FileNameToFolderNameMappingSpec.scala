package anduin.dataroom

import anduin.dataroom.service.FileNameToFolderNameMappingUtils
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{FolderId, FolderIdFactory}
import anduin.stargazer.service.dataroom.MappedFolderResult
import cats.data.NonEmptyList
import com.anduin.stargazer.UnitSpec

final class FileNameToFolderNameMappingSpec extends UnitSpec {
  val dataRoomId: DataRoomWorkflowId = DataRoomWorkflowId.defaultValue.get

  val folderId1: FolderId = FolderId.buildNode(dataRoomId, NonEmptyList(FolderIdFactory.generateSuffix, Nil))

  val folderId2: FolderId = FolderId.buildNode(dataRoomId, NonEmptyList(FolderIdFactory.generateSuffix, Nil))

  val folderId3: FolderId = FolderId.buildNode(dataRoomId, NonEmptyList(FolderIdFactory.generateSuffix, Nil))

  val folderNames: Seq[(FolderId, String)] = Seq(
    (folderId1, "<PERSON> Doe"),
    (folderId2, "<PERSON>"),
    (folderId3, "john_doe")
  )

  "getNormalizedFolderNameFromFileName" - {
    "Folder name should be empty when there is no separator characters detected" in {
      FileNameToFolderNameMappingUtils.getNormalizedFolderNameFromFileName(
        "Eric Miller-September Transactions.pdf"
      ) shouldBe empty
    }
    "Folder name should be case-insensitive" in {
      FileNameToFolderNameMappingUtils
        .getNormalizedFolderNameFromFileName("JohnDoe_Annual_Report_2024.pdf")
        .value shouldBe "johndoe"
    }
    "Folder name should ignore spacing" in {
      FileNameToFolderNameMappingUtils
        .getNormalizedFolderNameFromFileName("Jean-Michel Basquiat _ Q3-Report.pdf")
        .value shouldBe "jeanmichelbasquiat"
    }
    "Folder name should work with diacritical marks - 1" in {
      FileNameToFolderNameMappingUtils
        .getNormalizedFolderNameFromFileName("Sơn Tùng M-TP_Contact Information.csv")
        .value shouldBe "sơntùngmtp"
    }
    "Folder name should work with diacritical marks - 2" in {
      FileNameToFolderNameMappingUtils
        .getNormalizedFolderNameFromFileName("François-Elaloïs-Sébastien_Presentation_May_2024.pptx")
        .value shouldBe "françoiselaloïssébastien"
    }
    "Folder name should ignore some common special characters - 1" in {
      FileNameToFolderNameMappingUtils
        .getNormalizedFolderNameFromFileName("William S. Harris Jr._FinancialReport.pdf")
        .value shouldBe "williamsharrisjr"
    }
    "Folder name should ignore some common special characters - 2" in {
      FileNameToFolderNameMappingUtils
        .getNormalizedFolderNameFromFileName("Roderick O'Flaherty_Agreement.pdf")
        .value shouldBe "roderickoflaherty"
    }
    "Folder name should ignore some common special characters - 3" in {
      FileNameToFolderNameMappingUtils
        .getNormalizedFolderNameFromFileName(
          "John Brown, Mary Smith, and Susan Tanaka_Annual-Report.csv"
        )
        .value shouldBe "johnbrownmarysmithandsusantanaka"
    }
  }

  "getMappedFolderOfFile" - {
    "Should be able to detect mapped folder" in {
      FileNameToFolderNameMappingUtils.getMappedFolderOfFile(
        "Eric Miller _ Tax form.pdf",
        folderNames
      ) shouldBe MappedFolderResult.MatchedFolder(folderId2)
    }
    "Should be able to detect duplicated folders" in {
      FileNameToFolderNameMappingUtils.getMappedFolderOfFile(
        "John Doe_W9-2018.pdf",
        folderNames
      ) shouldBe MappedFolderResult.DuplicatedFolders(Seq(folderId1, folderId3))
    }
    "Should be able to detect no matched folders" in {
      FileNameToFolderNameMappingUtils.getMappedFolderOfFile(
        "Hary Williams_Subscription_agreement.pdf",
        folderNames
      ) shouldBe MappedFolderResult.NoMatchedFolders
    }
  }
}
