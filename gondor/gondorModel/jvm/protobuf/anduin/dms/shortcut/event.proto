syntax = "proto3";

package anduin.dms.shortcut.event;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "anduin/dms/id.proto";
import "anduin/dms/shortcut/state.proto";
import "date_time.proto";
import "flow/file/permission_map.proto";

option (scalapb.options) = {
	flat_package: true
	single_file: true
	lenses: false
	import: "java.time.Instant"
	import: "anduin.dms.DmsFlowEvent"
	import: "anduin.dms.FileFolderIdTypeMapper.given"
	import: "anduin.model.common.user.UserId"
	import: "anduin.model.id.ShortcutId"
	preamble: "private[dms] sealed trait ShortcutFlowEvent extends DmsFlowEvent {"
	preamble: "  def shortcutId: ShortcutId"
	preamble: "}"
};

message CreateShortcut {
	option (scalapb.message).annotations = "private[dms]";
	option (scalapb.message).companion_annotations = "private[dms]";
	option (scalapb.message).extends = "ShortcutFlowEvent";
	ShortcutIdMessage shortcut_id = 1 [(scalapb.field).type = "ShortcutId", (scalapb.field).no_box = true];
	int64 index = 2;
	InstantMessage timestamp = 3 [(scalapb.field).type = "Instant"];
	string actor = 4 [(scalapb.field).type = "UserId"];
	state.ShortcutDestination destination = 5;
	string name = 6;
	flow.file.FileFolderPermissionMap permission_map = 7;
}

message DeleteShortcut {
	option (scalapb.message).annotations = "private[dms]";
	option (scalapb.message).companion_annotations = "private[dms]";
	option (scalapb.message).extends = "ShortcutFlowEvent";
	ShortcutIdMessage shortcut_id = 1 [(scalapb.field).type = "ShortcutId", (scalapb.field).no_box = true];
	int64 index = 2;
	InstantMessage timestamp = 3 [(scalapb.field).type = "Instant"];
	string actor = 4 [(scalapb.field).type = "UserId"];
}

message RestoreShortcut {
	option (scalapb.message).annotations = "private[dms]";
	option (scalapb.message).companion_annotations = "private[dms]";
	option (scalapb.message).extends = "ShortcutFlowEvent";
	ShortcutIdMessage shortcut_id = 1 [(scalapb.field).type = "ShortcutId", (scalapb.field).no_box = true];
	int64 index = 2;
	InstantMessage timestamp = 3 [(scalapb.field).type = "Instant"];
	string actor = 4 [(scalapb.field).type = "UserId"];
}

message ModifyDestination {
	option (scalapb.message).annotations = "private[dms]";
	option (scalapb.message).companion_annotations = "private[dms]";
	option (scalapb.message).extends = "ShortcutFlowEvent";
	ShortcutIdMessage shortcut_id = 1 [(scalapb.field).type = "ShortcutId", (scalapb.field).no_box = true];
	int64 index = 2;
	InstantMessage timestamp = 3 [(scalapb.field).type = "Instant"];
	string actor = 4 [(scalapb.field).type = "UserId"];
	string name = 5;
	state.ShortcutDestination destination = 6;
}

message ModifyPermissions {
	option (scalapb.message).annotations = "private[dms]";
	option (scalapb.message).companion_annotations = "private[dms]";
	option (scalapb.message).extends = "ShortcutFlowEvent";
	ShortcutIdMessage shortcut_id = 1 [(scalapb.field).type = "ShortcutId", (scalapb.field).no_box = true];
	int64 index = 2;
	InstantMessage timestamp = 3 [(scalapb.field).type = "Instant"];
	string actor = 4 [(scalapb.field).type = "UserId"];
	flow.file.FileFolderPermissionMap updated_permissions = 5;
	flow.file.RevokedPermissionSet revoked_permissions = 6;
}

message RecordTypeUnion {
	CreateShortcut _CreateShortcut = 1;
	DeleteShortcut _DeleteShortcut = 2;
	ModifyPermissions _ModifyPermissions = 3;
	ModifyDestination _ModifyDestination = 4;
	RestoreShortcut _RestoreShortcut = 5;
}