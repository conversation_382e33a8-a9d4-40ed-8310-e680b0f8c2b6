// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.team.flow.memberflow

import anduin.fdb.record.model.FDBTupleConverter
import anduin.fdb.record.model.common.{RadixIdTupleConverter, UserIdTupleConverter}
import anduin.fdb.record.{FDBRecordEnum, FDBRecordKeySpace, FDBRecordStoreProvider}
import anduin.flow.fdb.EventStoreProviderCompanion
import anduin.model.id.TeamId
import com.apple.foundationdb.record.RecordMetaDataBuilder
import com.apple.foundationdb.record.metadata.Key
import com.apple.foundationdb.record.metadata.expressions.KeyExpression
import com.google.protobuf.Message

import anduin.team.teammemberflow.event.*

final case class TeamMemberEventStoreProvider(
  override protected val keySpace: FDBRecordKeySpace
) extends FDBRecordStoreProvider[FDBRecordEnum.TeamMemberFlowEvent.type](
      FDBRecordEnum.TeamMemberFlowEvent,
      TeamMemberFlowEventProto
    ) {

  protected def recordBuilderFn(builder: RecordMetaDataBuilder): Unit = {
    TeamMemberEventStoreProvider.allEventUnionModel.typeNames.foreach { typeName =>
      builder
        .getRecordType(typeName)
        .setPrimaryKey(TeamMemberEventStoreProvider.primaryKeyKeyExpression)
    }
  }

  protected def indexes: Seq[IndexMappingWithVersion] = {
    Seq(TeamMemberEventStoreProvider.Indexes.ByFlowKey.maxEver -> 1)
  }

}

object TeamMemberEventStoreProvider
    extends EventStoreProviderCompanion[
      FDBRecordEnum.TeamMemberFlowEvent.type,
      TeamMemberFlowPrimaryKey,
      TeamMemberFlowEvent,
      TeamMemberFlowEvent
    ] {

  protected val flowKeyExpression: KeyExpression = Key.Expressions.concatenateFields("team_id", "member_id")

  override given flowKeyTupleConverter: FDBTupleConverter[TeamMemberFlowPrimaryKey] = {
    RadixIdTupleConverter
      .instance[TeamId]
      .concat(UserIdTupleConverter.userIdTupleConverter)
      .biMap { case (teamId, userId) =>
        TeamMemberFlowPrimaryKey(teamId, userId)
      } { primaryKey =>
        (primaryKey.teamId, primaryKey.memberId)
      }
  }

  given caseInviteUser: Mapping[PrimaryKey, InviteUser] = mappingInstance
  given caseAcceptInvite: Mapping[PrimaryKey, AcceptInvite] = mappingInstance
  given caseRemindInvitedUser: Mapping[PrimaryKey, RemindInvitedUser] = mappingInstance
  given caseBounceInvitation: Mapping[PrimaryKey, BounceInvitation] = mappingInstance
  given caseDeclineInvite: Mapping[PrimaryKey, DeclineInvite] = mappingInstance
  given caseCancelInvite: Mapping[PrimaryKey, CancelInvite] = mappingInstance
  given caseAddMember: Mapping[PrimaryKey, AddMember] = mappingInstance
  given caseRemoveMember: Mapping[PrimaryKey, RemoveMember] = mappingInstance
  given caseRequestJoin: Mapping[PrimaryKey, RequestJoin] = mappingInstance
  given caseAcceptRequest: Mapping[PrimaryKey, AcceptRequest] = mappingInstance
  given caseDeclineRequest: Mapping[PrimaryKey, DeclineRequest] = mappingInstance
  given caseCancelRequest: Mapping[PrimaryKey, CancelRequest] = mappingInstance

  protected val allCases: List[Mapping[PrimaryKey, ? <: TeamMemberFlowEvent]] = List(
    caseInviteUser,
    caseAcceptInvite,
    caseRemindInvitedUser,
    caseBounceInvitation,
    caseDeclineInvite,
    caseCancelInvite,
    caseAddMember,
    caseRemoveMember,
    caseRequestJoin,
    caseAcceptRequest,
    caseDeclineRequest,
    caseCancelRequest
  )

  protected val toJavaMessage: TeamMemberFlowEvent => Message = {
    case e: InviteUser        => caseInviteUser.recordModel.toJavaMessage(e)
    case e: AcceptInvite      => caseAcceptInvite.recordModel.toJavaMessage(e)
    case e: RemindInvitedUser => caseRemindInvitedUser.recordModel.toJavaMessage(e)
    case e: BounceInvitation  => caseBounceInvitation.recordModel.toJavaMessage(e)
    case e: DeclineInvite     => caseDeclineInvite.recordModel.toJavaMessage(e)
    case e: CancelInvite      => caseCancelInvite.recordModel.toJavaMessage(e)
    case e: AddMember         => caseAddMember.recordModel.toJavaMessage(e)
    case e: RemoveMember      => caseRemoveMember.recordModel.toJavaMessage(e)
    case e: RequestJoin       => caseRequestJoin.recordModel.toJavaMessage(e)
    case e: AcceptRequest     => caseAcceptRequest.recordModel.toJavaMessage(e)
    case e: DeclineRequest    => caseDeclineRequest.recordModel.toJavaMessage(e)
    case e: CancelRequest     => caseCancelRequest.recordModel.toJavaMessage(e)
  }

  val firstEventMapping: Mapping[PrimaryKey, TeamMemberFlowEvent] = mappingInstance

  object Indexes extends BaseIndexes
}
