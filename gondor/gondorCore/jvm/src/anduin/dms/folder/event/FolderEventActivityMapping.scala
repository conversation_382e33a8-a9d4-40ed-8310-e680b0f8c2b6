// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dms.folder.event

import io.github.arainko.ducktape.Transformer

import anduin.dms.activity.*
import anduin.dms.folder.event.FolderEventStoreProvider.*

sealed private[dms] trait FolderEventActivityMapping[A <: FolderFlowActivity] {
  type Event <: FolderFlowEvent

  def index: IndexMapping[Event]

  def toActivity(event: Event): A

  def getActivityType: FileActivityType

}

object FolderEventActivityMapping {

  private def instance[A <: FolderFlowActivity, E <: FolderFlowEvent](
    indexMapping: IndexMapping[E],
    activityType: FileActivityType
  )(
    using transformer: Transformer.Derived[E, A]
  ): FolderEventActivityMapping[A] = {
    new FolderEventActivityMapping[A] {
      type Event = E

      override def index: IndexMapping[Event] = indexMapping

      override def toActivity(event: Event): A = transformer.transform(event)

      override def getActivityType: FileActivityType = activityType
    }
  }

  given modifyFolderMapping: FolderEventActivityMapping[FolderFlowActivity.ModifyPermissionsActivity] =
    instance[FolderFlowActivity.ModifyPermissionsActivity, ModifyPermissions](
      Indexes.ByEventType.modifyFolderPermissionIndex,
      FileActivityType.ModifyPermissionsActivity
    )

}
