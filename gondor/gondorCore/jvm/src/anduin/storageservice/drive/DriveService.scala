// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.storageservice.drive

import java.io.ByteArrayInputStream
import java.nio.charset.StandardCharsets
import java.util.Collections
import scala.annotation.unused
import scala.util.Try
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport
import com.google.api.client.json.gson.GsonFactory
import com.google.api.services.drive.model.File
import com.google.api.services.drive.{Drive, DriveScopes}
import com.google.auth.http.HttpCredentialsAdapter
import com.google.auth.oauth2.ServiceAccountCredentials
import io.circe.Json
import anduin.serverless.common.ServerlessModels.rclone.{
  GoogleDriveConfig,
  RcloneCopyItem,
  RcloneCopyRequest,
  ResourceConfig
}
import anduin.serverless.functions.RcloneServerless
import anduin.serverless.utils.ServerlessUtils
import anduin.storageservice.common.BaseStorageService
import anduin.storageservice.common.BaseStorageService.UploadFileItem
import anduin.storageservice.s3.S3Service
import com.anduin.stargazer.service.GondorConfig
import zio.{Task, ZIO}

import scala.jdk.CollectionConverters.*

import io.circe.parser.*

final case class DriveService(
  gondorConfig: GondorConfig,
  s3Service: S3Service,
  rcloneServerless: RcloneServerless
) extends BaseStorageService {

  private val driveKey = gondorConfig.backendConfig.driveConfig.key
  private val httpTransport = GoogleNetHttpTransport.newTrustedTransport()
  private val jsonFactory = new GsonFactory()

  private val credentials = ServiceAccountCredentials
    .fromStream(new ByteArrayInputStream(driveKey.getBytes(StandardCharsets.UTF_8)))
    .createScoped(Collections.singleton(DriveScopes.DRIVE))

  private val requestInitializer = new HttpCredentialsAdapter(credentials)

  private val driveService =
    new Drive.Builder(
      httpTransport,
      jsonFactory,
      requestInitializer
    ).setApplicationName("FundSub").build()

  override def listRootSharedFolders(): Task[Seq[BaseStorageService.FolderInfo]] = {
    val queryShareWithMe = getSharedWithMeQuery(sharedWithMe = true)
    ZIO.logInfo(s"DriveService: list shared folder under root") *>
      ZIO.attempt {
        val fileList = driveService
          .files()
          .list()
          .setIncludeItemsFromAllDrives(true)
          .setSupportsAllDrives(true)
          .setQ(s"(mimeType = 'application/vnd.google-apps.folder') and ($queryShareWithMe)")
          .setFields("files(id, name, owners, sharingUser)")
          .execute()
        fileList.getFiles.asScala.toSeq.map(file =>
          BaseStorageService.FolderInfo(
            id = file.getId,
            name = file.getName,
            owners = getFileOwners(file)
          )
        )
      }
  }

  override def sendFilesToFolder(
    rootFolderId: String,
    files: Seq[UploadFileItem],
    @unused configOpt: Option[Json]
  ): Task[Unit] = {
    val bucket = s3Service.s3Config.bucket
    val copyItems = files.map { fileItem =>
      RcloneCopyItem(
        source = List(bucket) ++ fileItem.storageId.id.split("/").toList,
        dest = (fileItem.destPath :+ fileItem.name).toList
      )
    }
    for {
      _ <- ZIO.logInfo(s"[DriveService]: upload files to root folder $rootFolderId, files: $files")
      _ <-
        rcloneServerless.doCopy(
          RcloneCopyRequest(
            files = copyItems.toList,
            source = ResourceConfig(
              s3Config = Some(ServerlessUtils.rcloneS3Config)
            ),
            dest = ResourceConfig(
              driveConfig = Some(GoogleDriveConfig(driveKey, rootFolderId = rootFolderId))
            )
          )
        )
    } yield ()
  }

  def getServiceAccountEmail: Task[String] = {
    ZIO.attempt {
      parse(driveKey).toOption.flatMap(_.hcursor.get[String]("client_email").toOption).getOrElse("")
    }
  }

  private def getSharedWithMeQuery(sharedWithMe: Boolean): String = {
    // Solution for sharedWithMe = false, see https://github.com/googleapis/google-api-python-client/issues/595
    if (sharedWithMe) "sharedWithMe = true" else "'me' in owners"
  }

  private def getFileOwners(file: File): Seq[String] = {
    Try {
      val owners = if (file.getOwners != null) { // scalafix:ok DisableSyntax.null
        file.getOwners.asScala.toSeq
      } else {
        Seq.empty
      }

      val sharingUser = if (file.getSharingUser != null) { // scalafix:ok DisableSyntax.null
        Seq(file.getSharingUser)
      } else {
        Seq.empty
      }

      (owners ++ sharingUser).distinctBy(_.getEmailAddress).map { user =>
        s"${user.getDisplayName}<${user.getEmailAddress}>"
      }
    }.getOrElse(Seq.empty)
  }

}
