// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.documentcontent.pdf

import anduin.model.document.DocumentStorageId
import anduin.serverless.common.ServerlessModels.S3Access
import anduin.serverless.common.ServerlessModels.muPdf.{InsertPdfRequest, InsertPdfResponse}
import anduin.serverless.functions.MuPDFServerless
import zio.Task

object PDFInserter {

  def insertPdf(
    muPdfServerless: MuPDFServerless,
    s3Access: S3Access,
    originalPdfStorageId: DocumentStorageId,
    pdfToInsertStorageId: DocumentStorageId,
    insertBeforePageIndex: Int,
    outputStorageId: DocumentStorageId
  ): Task[InsertPdfResponse] = {
    muPdfServerless.insertPdf(
      InsertPdfRequest(
        s3Access = s3Access,
        originalPdfStorageId = originalPdfStorageId.id,
        pdfToInsertStorageId = pdfToInsertStorageId.id,
        insertBeforePageIndex = insertBeforePageIndex,
        outputStorageId = outputStorageId.id
      )
    )
  }

}
