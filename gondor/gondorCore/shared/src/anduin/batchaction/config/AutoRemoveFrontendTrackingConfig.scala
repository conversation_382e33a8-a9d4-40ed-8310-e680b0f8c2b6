// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.batchaction.config

import anduin.batchaction.BatchActionType
import anduin.batchaction.endpoint.BatchActionInfo

import java.time.{Instant, Period}

/** Configuration to auto remove the batch action frontend tracking if the batch action was completed after a certain
  * period
  */
private[batchaction] object AutoRemoveFrontendTrackingConfig {

  private val PeriodToRemoveAfterCompleted: Period = Period.ofDays(2)

  private def shouldAutoRemoveType(batchActionType: BatchActionType): Boolean = {
    batchActionType match {
      // fund data
      case BatchActionType.FundDataImportInvestorFromSubscription        => false
      case BatchActionType.FundDataImportInvestorsBySpreadsheet          => false
      case BatchActionType.FundDataImportInvestmentEntitiesBySpreadsheet => false
      case BatchActionType.FundDataExportInvestmentEntitiesToSpreadsheet => false
      case BatchActionType.FundDataImportContactsBySpreadsheet           => false
      case BatchActionType.FundDataImportRiskAssessmentsBySpreadsheet    => false
      case BatchActionType.FundDataBatchDocumentRequest                  => false
      case BatchActionType.FundDataComputeProfilesFromSpreadsheet        => true
      case BatchActionType.FundDataImportDocumentBySpreadsheet           => false
      case BatchActionType.FundDataImportProfilesFromSpreadsheet         => false
      case BatchActionType.FundDataMergeInvestors                        => false
      case BatchActionType.FundDataAssignInvestorsToClientGroup          => false
      case BatchActionType.FundDataDeleteClientGroup                     => false
      case BatchActionType.FundDataInviteMembers                         => false
      case BatchActionType.FundDataRemoveGuests                          => false
      case BatchActionType.FundDataInviteGuests                          => false
      case BatchActionType.FundDataNotifyGuests                          => false
      case BatchActionType.FundDataCreateOfflineSubscriptions            => false
      case BatchActionType.FundDataModifyGuestsAccessToOpportunityPages  => false
      case BatchActionType.FundDataCreateOfflineTransactions             => false
      case BatchActionType.FundDataImportOrdersToTransactions            => true
      case BatchActionType.FundDataExtractInvestmentEntityInfoFromFiles  => false
      // data extract
      case BatchActionType.DataExtractGetUserDocumentsMappingResult => true
      case BatchActionType.DataExtractGenerateAnalyticsReport       => true
      case BatchActionType.DataExtractGenerateDebugReport           => true
      // fundsub
      case BatchActionType.FundSubOperationBulkAddEnvironment => true
      case BatchActionType.FundSubOperationRemoveEnvironment  => true
      case BatchActionType.FundSubInviteLp                    => false
      case BatchActionType.FundSubInviteFundManager           => false
      case BatchActionType.FundSubMoveFundManager             => false
      case BatchActionType.FundSubRemoveFundManager           => false
      case BatchActionType.FundSubDeleteFundManagerGroup      => false
      case BatchActionType.FundSubAssignInvestor              => false
      case BatchActionType.FundSubUnassignInvestor            => false
      case BatchActionType.FundSubDeleteInvestorGroup         => false
      // ria
      case BatchActionType.RiaCreateOrder => false
      // Annotations / PDF Tool
      case BatchActionType.PdfAnalyzePdfAnnotations => true

      case BatchActionType.Unrecognized(_) => false
    }
  }

  def shouldAutoRemove(batchActionInfo: BatchActionInfo): Boolean = {
    val checkType = shouldAutoRemoveType(batchActionInfo.actionType)
    val checkStatus = batchActionInfo.isCompleted
    val checkTime = batchActionInfo.latestStatusUpdate.exists(
      _.plus(PeriodToRemoveAfterCompleted).isBefore(Instant.now)
    )
    checkType && checkStatus && checkTime
  }

}
