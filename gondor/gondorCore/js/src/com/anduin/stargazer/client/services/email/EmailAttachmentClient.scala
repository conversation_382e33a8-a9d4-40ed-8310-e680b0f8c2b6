// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.client.services.email

import anduin.tapir.client.PublicEndpointClient
import com.anduin.stargazer.endpoints.{
  EmailAttachmentPublicEndpoints,
  GetEmailAttachmentDownloadAllParams,
  GetEmailAttachmentDownloadAllResponse,
  GetEmailAttachmentDownloadParams,
  GetEmailAttachmentDownloadResponse,
  GetEmailAttachmentParams,
  GetEmailAttachmentPdfUrlParams,
  GetEmailAttachmentPdfUrlResponse,
  GetEmailAttachmentResponse
}
import zio.Task

import anduin.service.GeneralServiceException

object EmailAttachmentClient extends PublicEndpointClient {

  val getEmailAttachment
    : GetEmailAttachmentParams => Task[Either[GeneralServiceException, GetEmailAttachmentResponse]] =
    toClient(
      EmailAttachmentPublicEndpoints.getEmailAttachment
    )

  val getEmailAttachmentDownloadUrl
    : GetEmailAttachmentDownloadParams => Task[Either[GeneralServiceException, GetEmailAttachmentDownloadResponse]] =
    toClient(
      EmailAttachmentPublicEndpoints.getEmailAttachmentDownloadUrl
    )

  val getEmailAttachmentDownloadAllUrl: GetEmailAttachmentDownloadAllParams => Task[
    Either[GeneralServiceException, GetEmailAttachmentDownloadAllResponse]
  ] = toClient(
    EmailAttachmentPublicEndpoints.getEmailAttachmentDownloadAllUrl
  )

  val getEmailAttachmentPdfUrl
    : GetEmailAttachmentPdfUrlParams => Task[Either[GeneralServiceException, GetEmailAttachmentPdfUrlResponse]] =
    toClient(
      EmailAttachmentPublicEndpoints.getEmailAttachmentPdfUrl
    )

}
