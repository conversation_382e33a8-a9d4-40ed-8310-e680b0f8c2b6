// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.account.enterprise

import zio.Task
import anduin.account.protocol.BifrostAuthenticationProtocol
import anduin.account.protocol.BifrostAuthenticationProtocol.EnterpriseLoginUserLinkedConfigInfo
import anduin.environment.{EnvironmentPolicyStoreOperations, EnvironmentPolicyStoreProvider}
import anduin.fdb.record.FDBRecordDatabase
import anduin.fdb.record.model.RecordIO
import anduin.id.account.EnterpriseLoginConfigId
import anduin.model.common.user.UserId
import anduin.model.id.EnterpriseLoginUserLinkIdFactory
import anduin.protobuf.account.EnterpriseLoginUserLink

final case class LocalEnterpriseUserLinkService() extends EnterpriseUserLinkService {

  override def createUserLink(userId: UserId, configId: EnterpriseLoginConfigId): Task[Boolean] = {
    val linkId = EnterpriseLoginUserLinkIdFactory.unsafeRandomId
    FDBRecordDatabase
      .transact(EnterpriseStoreOperations.Production) { ops =>
        for {
          links <- ops.getUserLinksByUserId(userId)
          shouldCreate = !links.exists(_.configId == configId)
          _ <- RecordIO.when(shouldCreate) {
            ops.createEnterpriseLoginUserLink(
              EnterpriseLoginUserLink(
                linkId = linkId,
                userId = userId,
                configId = configId
              )
            )
          }
        } yield shouldCreate
      }
  }

  override def deleteUserLink(userId: UserId, configId: EnterpriseLoginConfigId): Task[Unit] = {
    FDBRecordDatabase
      .transact(EnterpriseStoreOperations.Production) { ops =>
        for {
          links <- ops.getUserLinksByUserId(userId)
          linkOpt = links.find(_.configId == configId)
          _ <- RecordIO.traverseOptionUnit(linkOpt) { link =>
            ops.deleteEnterpriseLoginUserLink(link.linkId)
          }
        } yield ()
      }
  }

  override def getLinkedConfigsForUser(userId: UserId): Task[Seq[EnterpriseLoginUserLink]] = {
    FDBRecordDatabase
      .transact(EnterpriseStoreOperations.Production) { ops =>
        ops.getUserLinksByUserId(userId)
      }
  }

  override def getLinkedConfigInfosForUser(userId: UserId, filterOutEnvironment: Boolean)
    : Task[Seq[BifrostAuthenticationProtocol.EnterpriseLoginUserLinkedConfigInfo]] = {
    FDBRecordDatabase
      .transact(EnterpriseStoreProvider.Production, EnvironmentPolicyStoreProvider.Production) {
        case (enterpriseStore, environmentPolicyStore) =>
          val enterpriseStoreOperations = EnterpriseStoreOperations(enterpriseStore)
          val environmentPolicyStoreOperations = EnvironmentPolicyStoreOperations(environmentPolicyStore)
          for {
            links <- enterpriseStoreOperations.getUserLinksByUserId(userId)
            configInfos <- RecordIO.traverse(links) { link =>
              for {
                configOpt <- enterpriseStoreOperations.getOptEnterpriseLoginConfig(link.configId)
                boundedEnvironments <- environmentPolicyStoreOperations
                  .getEnvironmentReauthenticationPoliciesForSSOConfig(link.configId)
              } yield configOpt.flatMap { config =>
                if (filterOutEnvironment && boundedEnvironments.nonEmpty) {
                  None
                } else {
                  Some(
                    EnterpriseLoginUserLinkedConfigInfo(
                      configId = config.id,
                      providerName = config.providerName,
                      providerLogoUrl = config.providerLogoUrl
                    )
                  )
                }
              }
            }
          } yield configInfos.flatten
      }
  }

}
