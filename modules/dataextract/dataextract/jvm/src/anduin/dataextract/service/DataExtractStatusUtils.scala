// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataextract.service

import zio.ZIO

import anduin.dataextract.database.{
  DataExtractProjectItemStoreOperations,
  DataExtractProjectModelOperations,
  DataExtractUserDocumentStoreOperations
}
import anduin.fdb.record.{FDBOperations, FDBRecordDatabase}
import anduin.id.dataextract.{DataExtractProjectItemId, DataExtractUserDocumentId}
import anduin.model.id.FileId
import anduin.protobuf.dataextract.TemplateMessage
import anduin.protobuf.dataextract.projectitem.{DataExtractProjectItemMessage, DataExtractProjectItemStatusMessage}
import anduin.protobuf.dataextract.userdocument.{DataExtractUserDocumentMessage, DataExtractUserDocumentStatusMessage}

object DataExtractStatusUtils {

  private[service] def recalculateProjectItemStatus(projectItemId: DataExtractProjectItemId) = {
    for {
      (project, projectItem, userDocuments) <- FDBRecordDatabase.transact(
        FDBOperations[
          (
            DataExtractProjectModelOperations,
            DataExtractProjectItemStoreOperations,
            DataExtractUserDocumentStoreOperations
          )
        ].Production
      ) { case (projectOps, projectItemOps, userDocumentOps) =>
        for {
          project <- projectOps.get(projectItemId.parent)
          projectItem <- projectItemOps.get(projectItemId)
          userDocuments <- userDocumentOps.getByProjectItem(projectItemId)
        } yield (project, projectItem, userDocuments)
      }
      applicableTemplates = userDocuments.flatMap(_.applicableTemplates).toSet
      templates = project.templates.filter { template =>
        applicableTemplates.contains(template.id)
      }
      curStatus = projectItem.status
      newStatus <-
        if (curStatus.isDone) {
          ZIO.succeed(curStatus)
        } else {
          val calculatedStatus = calculateProjectItemStatus(projectItem, userDocuments, templates)
          FDBRecordDatabase
            .transact(DataExtractProjectItemStoreOperations.Production)(
              _.update(projectItemId)(_.copy(status = calculatedStatus))
            )
            .map { _ => calculatedStatus }
        }
    } yield newStatus != curStatus
  }

  private[service] def recalculateUserDocumentStatus(
    userDocumentId: DataExtractUserDocumentId,
    ignoreRejected: Boolean
  ) = {
    for {
      (project, userDocument) <- FDBRecordDatabase.transact(
        FDBOperations[
          (
            DataExtractProjectModelOperations,
            DataExtractUserDocumentStoreOperations
          )
        ].Production
      ) { case (projectOps, userDocumentOps) =>
        for {
          project <- projectOps.get(userDocumentId.parent.parent)
          userDocument <- userDocumentOps.get(userDocumentId)
        } yield (project, userDocument)
      }
      templates = project.templates.filter { template =>
        userDocument.applicableTemplates.contains(template.id)
      }
      curStatus = userDocument.status
      newStatus <-
        if (ignoreRejected && curStatus.isRejected) {
          ZIO.succeed(curStatus)
        } else {
          val calculatedStatus = calculateUserDocumentStatus(userDocument, templates)
          FDBRecordDatabase
            .transact(DataExtractUserDocumentStoreOperations.Production)(
              _.update(userDocumentId)(_.copy(status = calculatedStatus))
            )
            .map { _ => calculatedStatus }
        }
    } yield newStatus != curStatus
  }

  private def calculateProjectItemStatus(
    projectItem: DataExtractProjectItemMessage,
    userDocuments: Seq[DataExtractUserDocumentMessage],
    templates: Seq[TemplateMessage]
  ) = {
    lazy val reviewedFieldCounts = projectItem.reviewedDataCache.fold(Map.empty[FileId, Int])(_.reviewedFieldCounts)
    val isImportedToForm = projectItem.extractedFormVersionIdOpt.nonEmpty
    lazy val isTodo = userDocuments.exists(_.status.isTodo) || userDocuments.forall(_.status.isRejected)
    lazy val isReviewed = templates.nonEmpty && templates.forall { template =>
      template.templateDataCache.exists(_.fieldCount == reviewedFieldCounts.getOrElse(template.id, 0))
    }
    lazy val isReviewing = templates.exists { template => reviewedFieldCounts.get(template.id).exists(_ > 0) }
    if (isImportedToForm) {
      DataExtractProjectItemStatusMessage.ImportedToForm
    } else if (isTodo) {
      DataExtractProjectItemStatusMessage.Todo
    } else if (isReviewed) {
      DataExtractProjectItemStatusMessage.Reviewed
    } else if (isReviewing) {
      DataExtractProjectItemStatusMessage.Reviewing
    } else {
      DataExtractProjectItemStatusMessage.OcrProcessed
    }
  }

  private def calculateUserDocumentStatus(
    userDocument: DataExtractUserDocumentMessage,
    templates: Seq[TemplateMessage]
  ) = {
    lazy val processedTemplateFileIds =
      userDocument.userFileCache.flatMap(_.pageMatchingCache).fold(Set.empty[FileId])(_.templateFileIds)
    val isOcrProcessed =
      templates.nonEmpty && processedTemplateFileIds == templates.map(_.id).toSet && userDocument.userFileCache
        .flatMap(_.pageMatchingCache)
        .exists(_.doneSecondTextract)
    if (!isOcrProcessed) {
      DataExtractUserDocumentStatusMessage.Todo
    } else {
      DataExtractUserDocumentStatusMessage.OcrProcessed
    }
  }

}
