// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investmententity.service

import zio.Task

import anduin.id.funddata.FundDataFirmId
import anduin.id.fundsub.FundSubId
import anduin.id.investmententity.InvestmentEntityId
import anduin.model.common.user.UserId
import anduin.service.AuthenticatedRequestContext

trait InvestmentEntitySimulatorService {

  def createDemoInvestmentEntityForFundData(
    userId: UserId,
    firmId: FundDataFirmId,
    fundSubId: FundSubId,
    httpContextOpt: Option[AuthenticatedRequestContext]
  ): Task[Unit]

  def createDemoInvestmentEntityForFundSub(
    userId: UserId,
    fundSubId: FundSubId
  ): Task[Unit]

  def initDemoData(
    actor: UserId,
    investmentEntityId: InvestmentEntityId,
    httpContextOpt: Option[AuthenticatedRequestContext]
  ): Task[Unit]

}
