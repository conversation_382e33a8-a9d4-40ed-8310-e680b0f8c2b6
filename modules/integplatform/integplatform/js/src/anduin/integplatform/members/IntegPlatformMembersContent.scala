// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.integplatform.members

import anduin.id.entity.EntityId
import com.raquo.laminar.api.L.*
import design.anduin.style.tw.*

private[integplatform] final case class IntegPlatformMembersContent(entityId: EntityId) {

  private val TableWidth = 960

  def apply(): HtmlElement = {
    div(
      tw.flex.flexCol.itemsCenter.pb48.wPc100.hPc100,
      renderTitle,
      div(
        tw.mt48.flex.flexCol.flexFill.wPc100,
        maxWidth.px := TableWidth,
        ManageMembersTable(entityId)(),
        InviteHubMembersBtn(entityId)().amend(tw.mt16.flexNone)
      )
    )
  }

  private def renderTitle: HtmlElement = {
    div(
      tw.textCenter,
      div(
        tw.fontBold.text30.mb16.leading40,
        "Members"
      )
    )
  }

}
