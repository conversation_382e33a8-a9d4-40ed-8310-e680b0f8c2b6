// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.ontology.client

import java.util.UUID

import anduin.service.GeneralServiceException
import anduin.tapir.client.AuthenticatedEndpointClient
import zio.Task

import anduin.ontology.endpoints.*

object OntologyEndpointClient extends AuthenticatedEndpointClient {

  val processAsaParseResultsFromCsv: ProcessAsaParseResultsFromCsvParams => Task[
    Either[GeneralServiceException, ProcessAsaParseResultsFromCsvResponse]
  ] = {
    toClientThrowDecodeAndSecurityFailures(OntologyEndpoints.processAsaParseResultsFromCsv)
  }

  val queryJsonElements: String => Task[Either[GeneralServiceException, QueryJsonElementsResponse]] = {
    toClientThrowDecodeAndSecurityFailures(OntologyEndpoints.queryJsonElements)
  }

  val queryJsonElementsSeqInput
    : QueryJsonElementsSeqInputParams => Task[Either[GeneralServiceException, QueryJsonElementsResponse]] = {
    toClientThrowDecodeAndSecurityFailures(OntologyEndpoints.queryJsonElementsSeqInput)
  }

  val queryNodeById: UUID => Task[Either[GeneralServiceException, AsaNodeOptionResponse]] = {
    toClientThrowDecodeAndSecurityFailures(OntologyEndpoints.queryNodeById)
  }

  val queryNodesByUniqueRefInfo
    : QueryNodesByUniqueRefInfoParams => Task[Either[GeneralServiceException, AsaNodeListResponse]] = {
    toClientThrowDecodeAndSecurityFailures(OntologyEndpoints.queryNodesByUniqueRefInfo)
  }

  val fullTextSearch: FullTextSearchParams => Task[Either[GeneralServiceException, FullTextSearchListResponse]] = {
    toClientThrowDecodeAndSecurityFailures(OntologyEndpoints.fullTextSearch)
  }

  val searchNodesByAliasSubTexts
    : SearchNodesByAliasSubTextsParams => Task[Either[GeneralServiceException, AsaNodeListResponse]] = {
    toClientThrowDecodeAndSecurityFailures(OntologyEndpoints.searchNodesByAliasSubTexts)
  }

  val deleteNodeById: UUID => Task[Either[GeneralServiceException, Unit]] = {
    toClientThrowDecodeAndSecurityFailures(OntologyEndpoints.deleteNodeById)
  }

  val updateNode: UpdateNodeParams => Task[Either[GeneralServiceException, Unit]] = {
    toClientThrowDecodeAndSecurityFailures(OntologyEndpoints.updateNode)
  }

}
