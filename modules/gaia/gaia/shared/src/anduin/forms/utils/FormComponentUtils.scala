// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.utils

import anduin.forms.Form
import anduin.forms.endpoint.{LibraryComponent, LibraryComponentPage}
import anduin.forms.logic.extractor.LogicExtractor

object FormComponentUtils {

  // Extract all the direct children of the pages as the current convention.
  // Related rule is empty for now, but should be supported in the future
  def extractLibraryComponents(form: Form): Seq[LibraryComponentPage] = {
    val allValidRules = LogicExtractor
      .batchExtract(
        form.rules,
        form.libs,
        form.cueFieldSchemas
      )
      .zip(form.rules)
      .flatMap { case (resultEither, rule) =>
        resultEither.toOption.map(rule -> _)
      }
    FormDataUtils.findAllPage(form).map { case (pageName, pageObj) =>
      LibraryComponentPage(
        pageTitle = pageObj.title.getOrElse(pageName),
        components = pageObj.fields.map { field =>
          val allKeys = FormDataUtils.findAllKeys(field.name, field.tpe).toSet
          val relatedRules = allValidRules.filter(_._2.allKeys.map(_.key).intersect(allKeys).nonEmpty)
          LibraryComponent(
            name = field.name,
            schema = field.tpe,
            widgets = form.defaultUiSchema.view.filterKeys(allKeys.contains).toMap,
            relatedRules = relatedRules
          )
        }
      )
    }
  }

}
