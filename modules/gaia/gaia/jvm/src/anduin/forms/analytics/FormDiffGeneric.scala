// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.analytics

import anduin.algorithm.LCS
import anduin.algorithm.LCS.Diffable
import anduin.forms.Form
import anduin.forms.model.Schema.obj.Field
import anduin.forms.model.formdiff.{Matching, RuleMatching}
import anduin.forms.ui.{UIKey, Widget, WidgetType}
import anduin.forms.utils.FormJsonDiff
import anduin.forms.utils.FormJsonDiff.Section

object FormDiffGeneric {

  def diff(
    oldForm: Form,
    newForm: Form,
    blacklistWidgets: Set[WidgetType],
    showMatchingDataFn: Section => String = (s: Section) => s.field.name
  )(
    using
    sectionDiffable: Diffable[Section]
  ): (Seq[Matching], Seq[RuleMatching]) = {
    val filter: (Field, Widget) => Boolean = (_, widget) => {
      !widget.uiOptions.getOrElse(UIKey.invisible, false)
    }
    val oldSections = FormJsonDiff
      .flatten(oldForm, filter)
      .filterNot(section => blacklistWidgets.contains(section.widget.widgetType))
      .toVector
    val newSections =
      FormJsonDiff
        .flatten(newForm, filter)
        .filterNot(section => blacklistWidgets.contains(section.widget.widgetType))
        .toVector
    LCS.computeMatching(oldSections, newSections).map { matching =>
      Matching(
        matching.lhs.map(showMatchingDataFn),
        matching.rhs.map(showMatchingDataFn),
        matching.isSame
      )
    } -> Nil
  }

}
