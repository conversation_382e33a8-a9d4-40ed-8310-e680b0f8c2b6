// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.builder.modal

import com.raquo.laminar.nodes.ReactiveHtmlElement
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.progress.BarIndicator
import design.anduin.components.progress.laminar.{BarIndicatorL, BlockIndicatorL}
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.style.tw.*
import org.scalajs.dom.html
import zio.ZIO

import anduin.forms.builder.modal.PdfFormCutSharedModal.CutPdfOutput
import anduin.forms.builder.modal.UpdateMultiplePdfCutModal.{AllFilesStatus, FileStatus, InputRowData}
import anduin.forms.builder.toc.EmbeddedPdfWarning.SectionToReview
import anduin.forms.client.FormEndpointClient
import anduin.forms.endpoint.{CutFileParams, GetFileInfoToCutParams}
import anduin.forms.ui.types.FileId
import anduin.forms.utils.FormDataConverters
import anduin.forms.version.FormVersionMetadataModel.PdfCutInfo
import anduin.frontend.AirStreamUtils
import anduin.id.form.FormId
import anduin.util.FilenameUtils

import com.raquo.laminar.api.L.*

final case class UpdateMultiplePdfCutModal(
  formId: FormId,
  sectionsToReviewSignal: Signal[Seq[SectionToReview]],
  close: Observer[Unit],
  observer: Observer[Map[String, CutPdfOutput]],
  allFileNamesSignal: Signal[Set[String]]
) {

  private val inputVar = Var(Map.empty[String, InputRowData])
  private val inputSignal = inputVar.signal.distinct
  private val fileStatusVar = Var(Map.empty[String, FileStatus])
  private val fileStatusSignal = fileStatusVar.signal.distinct

  private val allFilesStatusSignal = fileStatusSignal
    .map[AllFilesStatus] { fileStatusMap =>
      val allStatus = fileStatusMap.values.toSet
      if (allStatus.contains(FileStatus.Error)) {
        AllFilesStatus.Error
      } else if (allStatus.contains(FileStatus.Loading)) {
        AllFilesStatus.Loading
      } else {
        AllFilesStatus.Loaded
      }
    }
    .distinct

  private val invalidPageRangeKeysSignal = inputSignal
    .combineWith(fileStatusSignal)
    .map { case input -> fileStatusMap =>
      input.filter { case oldFileName -> InputRowData(_, startOpt, endOpt) =>
        val pageCountOpt = fileStatusMap.get(oldFileName).flatMap(_.getPageCountOpt)
        (for {
          start <- startOpt
          end <- endOpt
          pageCount <- pageCountOpt
        } yield start < 1 || start > end || end > pageCount).getOrElse(true)
      }.keySet
    }
    .distinct

  private val invalidFileNameKeysSignal = inputSignal
    .map(_.filter { case _ -> row => !FilenameUtils.isValidFileName(row.name) }.keySet)
    .distinct

  private val duplicatedFileNameKeysSignal = inputSignal
    .combineWith(allFileNamesSignal)
    .map { case input -> allFileNames =>
      val remainingFileNames = allFileNames -- input.keySet.map(_.toLowerCase)
      val duplicatedFileNameKeys =
        input.groupMap(_._2.name.toLowerCase)(_._1).values.filter(_.size > 1).flatten.toSet
      val sameWithOuterFileNameKeys = input.filter { case _ -> row =>
        remainingFileNames.contains(FilenameUtils.getPdfFilename(row.name).toLowerCase)
      }.keySet
      duplicatedFileNameKeys ++ sameWithOuterFileNameKeys
    }
    .distinct

  private val canUpdateSignal =
    (invalidPageRangeKeysSignal.map(_.isEmpty) &&
      invalidFileNameKeysSignal.map(_.isEmpty) &&
      duplicatedFileNameKeysSignal.map(_.isEmpty)).distinct

  private def loadPdf(fileId: FileId, fileStatusObserver: Observer[FileStatus]) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        _ <- ZIO.attempt(fileStatusObserver.onNext(FileStatus.Loading))
        fid <- ZIO.attempt(FormDataConverters.fileIdTypeToFileIdThrowError(fileId))
        resp <- FormEndpointClient.getFileInfoToCut(GetFileInfoToCutParams(fid, formId))
      } yield resp.fold(
        _ => fileStatusObserver.onNext(FileStatus.Error),
        resp => fileStatusObserver.onNext(FileStatus.Loaded(resp.pageCount))
      )
    }
  }

  private def onUpdate(sectionsToReview: Seq[SectionToReview], input: Map[String, InputRowData]) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        output <- ZIO
          .foreachPar(sectionsToReview) { section =>
            val cutParamsOpt = for {
              row <- input.get(section.embeddedPdfFileName)
              start <- row.startOpt
              end <- row.endOpt
              fileName = FilenameUtils.getPdfFilename(row.name)
            } yield (start, end, fileName)
            ZIO
              .foreach(cutParamsOpt) { case (start, end, fileName) =>
                for {
                  fid <- ZIO.attempt(FormDataConverters.fileIdTypeToFileIdThrowError(section.uploadedPdfFileId))
                  respEither <- FormEndpointClient.cutFile(
                    CutFileParams(
                      fid,
                      formId,
                      start,
                      end,
                      fileName
                    )
                  )
                } yield respEither.toOption.map { resp =>
                  section.embeddedPdfFileName -> CutPdfOutput(
                    FileId(resp.fileId.idString),
                    fileName,
                    PdfCutInfo(
                      section.uploadedPdfFileName,
                      start,
                      end
                    )
                  )
                }
              }
              .map(_.flatten)
          }
          .map(_.flatten.toMap)
        _ <- ZIO.attempt(observer.onNext(output))
        _ <- ZIO.attempt(close.onNext(()))
      } yield ()
    }
  }

  private val actions = {
    val onClickEventBus = new EventBus[Unit]
    div(
      tw.flex.justifyEnd,
      ButtonL(onClick = close.contramap(_ => ()))("Cancel"),
      div(
        tw.ml8,
        ModalL(
          renderTarget = open =>
            ButtonL(
              style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
              onClick = Observer { _ =>
                onClickEventBus.emit(())
                open.onNext(())
              },
              isDisabled = !canUpdateSignal
            )("Update"),
          renderContent = _ =>
            div(
              tw.p40,
              div(
                width := "400px",
                tw.textPrimary4.m24.mb16,
                BarIndicatorL(height = BarIndicator.Height.Large)()
              ),
              div(tw.flex.justifyCenter, "Generating files...")
            ),
          isClosable = None,
          size = ModalL.Size(ModalL.Width.Content)
        )()
      ),
      onClickEventBus.events.sample(sectionsToReviewSignal, inputSignal).flatMapSwitch { case sections -> input =>
        onUpdate(sections, input)
      } --> close
    )
  }

  private val content = div(
    div(
      tw.mt20.mb16,
      "The below embedded files will be updated using the new subscription document."
    ),
    div(
      tw.borderGray3.borderBottom.flex.fontSemiBold.textGray7.py8.px12,
      div(tw.flexFill, "Embedded file"),
      div(width := "180px", "Page range")
    ),
    children <-- sectionsToReviewSignal.split(_.embeddedPdfFileName) { case (embeddedPdfFileName, _, sectionSignal) =>
      val fileStatusObserver = fileStatusVar.updater[FileStatus] { case fileStatusMap -> fileStatus =>
        fileStatusMap + (embeddedPdfFileName -> fileStatus)
      }
      val rowOptSignal = inputSignal.map(_.get(embeddedPdfFileName))
      val nameOptSignal = rowOptSignal.map(_.map(_.name)).distinct
      val startOptSignal = rowOptSignal.map(_.flatMap(_.startOpt)).distinct
      val endOptSignal = rowOptSignal.map(_.flatMap(_.endOpt)).distinct
      val rowObserver = inputVar.updater[InputRowData] { case input -> row => input + (embeddedPdfFileName -> row) }
      val nameObserver = inputVar.updater[String] { case input -> name =>
        input.updatedWith(embeddedPdfFileName)(_.map(_.copy(name = name)))
      }
      val startOptObserver = inputVar.updater[Option[Int]] { case input -> startOpt =>
        input.updatedWith(embeddedPdfFileName)(_.map(_.copy(startOpt = startOpt)))
      }
      val endOptObserver = inputVar.updater[Option[Int]] { case input -> endOpt =>
        input.updatedWith(embeddedPdfFileName)(_.map(_.copy(endOpt = endOpt)))
      }
      div(
        tw.borderGray3.borderBottom.p12,
        div(
          tw.flex.itemsCenter,
          div(
            tw.flexFill.flex.itemsCenter.mr24,
            div(IconL(Val(Icon.Glyph.FileEye))()),
            TextBoxL(
              value = nameOptSignal.map(_.getOrElse("")),
              onBlur = nameObserver.contramap(FilenameUtils.autoSanitizeName)
            )().amend(tw.ml8),
            span(tw.ml4.textGray7, s".${FilenameUtils.Extension.Pdf}")
          ),
          div(
            tw.flex.itemsCenter,
            div(
              maxWidth := "80px",
              TextBoxL(
                value = startOptSignal.map(_.map(_.toString).getOrElse("")),
                onBlur = startOptObserver.contramap(_.toIntOption),
                tpe = TextBoxL.Tpe.NumberInt()
              )()
            ),
            div(tw.wPx20.textCenter, "-"),
            div(
              maxWidth := "80px",
              TextBoxL(
                value = endOptSignal.map(_.map(_.toString).getOrElse("")),
                onBlur = endOptObserver.contramap(_.toIntOption),
                tpe = TextBoxL.Tpe.NumberInt()
              )()
            )
          )
        ),
        div(
          tw.ml24.mt4.text11,
          span(
            tw.textGray7,
            "Cut from: "
          ),
          span(child.text <-- sectionSignal.map(_.uploadedPdfFileName).distinct)
        ),
        div(
          tw.mt8.textDanger5.text11.leading16.flex.itemsCenter,
          invalidPageRangeKeysSignal.map(_.contains(embeddedPdfFileName)).distinct.not.cls(tw.hidden),
          IconL(Val(Icon.Glyph.Error), Icon.Size.Custom(12))(),
          div(tw.ml4, "Invalid page range!")
        ),
        div(
          tw.mt8.textDanger5.text11.leading16.flex.itemsCenter,
          invalidFileNameKeysSignal.map(_.contains(embeddedPdfFileName)).distinct.not.cls(tw.hidden),
          IconL(Val(Icon.Glyph.Error), Icon.Size.Custom(12))(),
          div(tw.ml4, "Invalid file name!")
        ),
        div(
          tw.mt8.textDanger5.text11.leading16.flex.itemsCenter,
          duplicatedFileNameKeysSignal.map(_.contains(embeddedPdfFileName)).distinct.not.cls(tw.hidden),
          IconL(Val(Icon.Glyph.Error), Icon.Size.Custom(12))(),
          div(tw.ml4, "File name duplicated with another file!")
        ),
        sectionSignal.map(_.uploadedPdfFileId).distinct.flatMapSwitch(loadPdf(_, fileStatusObserver)) --> Observer.empty,
        sectionSignal --> rowObserver
          .contramap[SectionToReview] { section =>
            InputRowData(
              FilenameUtils.getName(section.embeddedPdfFileName),
              Some(section.start),
              Some(section.end)
            )
          }
      )
    },
    div(tw.mt16.mb24, "Are you sure you want to continue?")
  )

  def apply(): ReactiveHtmlElement[html.Div] = div(
    tw.p24.pt20,
    div(
      tw.flex.itemsCenter.justifyBetween,
      tw.fontSemiBold.text20.leading32,
      div("Update embedded files"),
      ButtonL(
        style = ButtonL.Style.Minimal(icon = Some(Icon.Glyph.Cross)),
        onClick = close.contramap(_ => ())
      )()
    ),
    child <-- allFilesStatusSignal.map {
      case AllFilesStatus.Error => div(tw.py32.textDanger5, "Unable to load file info.")
      case AllFilesStatus.Loading =>
        div(tw.py32.hPx128, BlockIndicatorL(Val(Some("Loading file info")), isFullHeight = true)())
      case AllFilesStatus.Loaded => content
    },
    actions
  )

}

object UpdateMultiplePdfCutModal {
  private final case class InputRowData(name: String, startOpt: Option[Int], endOpt: Option[Int])

  private sealed trait FileStatus derives CanEqual {

    def getPageCountOpt: Option[Int] = {
      this match {
        case FileStatus.Loaded(pageCount) => Some(pageCount)
        case _                            => None
      }
    }

  }

  private object FileStatus {
    case object Error extends FileStatus
    case object Loading extends FileStatus
    final case class Loaded(pageCount: Int) extends FileStatus
  }

  private sealed trait AllFilesStatus derives CanEqual

  private object AllFilesStatus {
    case object Error extends AllFilesStatus
    case object Loading extends AllFilesStatus
    case object Loaded extends AllFilesStatus
  }

}
