// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.builder.modal

import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.toast.Toast

import anduin.forms.builder.modal.PdfFormCutSharedModal.CutPdfOutput
import anduin.forms.ui.types.FileId
import anduin.forms.ui.{UIKey, Widget}
import anduin.id.form.FormId
import anduin.util.FilenameUtils

import com.raquo.laminar.api.L.*

final case class PdfCutModal(
  formId: FormId,
  uploadedPdfFileIdSignal: Signal[FileId],
  uploadedPdfFileNameSignal: Signal[String],
  observer: Observer[CutPdfOutput],
  close: Observer[Unit],
  keyOptSignal: Signal[Option[String]],
  formWidgetMapSignal: Signal[Map[String, Widget]]
) {

  private val autoNameOptVar = Var(Option.empty[String])
  private val autoNameOptSignal = autoNameOptVar.signal.distinct
  private val openFileReplaceWarningModalVar = Var(false)
  private val finishEventBus = new EventBus[Unit]

  private val sameNameFileFieldsSignal =
    autoNameOptSignal
      .combineWith(formWidgetMapSignal)
      .map { case nameOpt -> widgetMap =>
        nameOpt.toSeq.flatMap { name =>
          val lowerFileName = FilenameUtils.getPdfFilename(name).toLowerCase
          widgetMap.filter(_._2.uiOptions.get(UIKey.fileName).exists(_.toLowerCase == lowerFileName)).keys.toSeq
        }
      }
      .distinct

  private val sameNameEmbeddedFieldsSignal =
    autoNameOptSignal
      .combineWith(formWidgetMapSignal)
      .combineWith(keyOptSignal)
      .map { case (nameOpt, widgetMap, keyOpt) =>
        nameOpt.toSeq.flatMap { name =>
          val lowerFileName = FilenameUtils.getPdfFilename(name).toLowerCase
          widgetMap
            .filter(_._2.uiOptions.get(UIKey.embeddedPdf).exists(_.toLowerCase == lowerFileName))
            .keys
            .toSeq
            .filterNot(keyOpt.contains)
        }
      }
      .distinct

  private val fileNameErrorOptSignal = sameNameFileFieldsSignal.map { sameNameFileFields =>
    Option.when(sameNameFileFields.nonEmpty)("File name duplicated with a subscription file!")
  }.distinct

  private val fileReplaceWarningModal = {
    ModalL(
      renderTitle = _ => "Embedded file replace warning",
      renderContent = close =>
        FileReplaceWarningModal(
          uploadedFileNameSignal = autoNameOptSignal.map(_.getOrElse("")).map(FilenameUtils.getPdfFilename).distinct,
          affectedFieldNamesSignal = sameNameEmbeddedFieldsSignal,
          cancelObserver = close,
          confirmObserver = Observer { _ =>
            finishEventBus.emit(())
            close.onNext(())
          }
        )(),
      isOpened = Some(openFileReplaceWarningModalVar.signal.distinct),
      afterUserClose = openFileReplaceWarningModalVar.writer.contramap { _ => false }
    )()
  }

  def apply(): Node = {
    val pipe = new EventBus[CutPdfOutput]
    val confirmCutEventBus = new EventBus[Unit]
    PdfFormCutSharedModal(
      formId = formId,
      uploadedPdfFileIdSignal = uploadedPdfFileIdSignal,
      uploadedPdfFileNameSignal = uploadedPdfFileNameSignal,
      observer = pipe.writer,
      close = close,
      autoNameOptObserver = autoNameOptVar.writer,
      customFileNameErrorOptSignal = fileNameErrorOptSignal,
      onConfirmCut = confirmCutEventBus.writer,
      finishEvents = finishEventBus.events
    )().amend(
      fileReplaceWarningModal,
      pipe.events --> observer,
      confirmCutEventBus.events
        .sample(fileNameErrorOptSignal, sameNameEmbeddedFieldsSignal) --> Observer[(Option[String], Seq[String])] {
        case fileNameErrorOpt -> sameNameEmbeddedFields =>
          fileNameErrorOpt.fold {
            if (sameNameEmbeddedFields.nonEmpty) {
              openFileReplaceWarningModalVar.set(true)
            } else {
              finishEventBus.emit(())
            }
          }(Toast.error(_))
      }
    )
  }

}
