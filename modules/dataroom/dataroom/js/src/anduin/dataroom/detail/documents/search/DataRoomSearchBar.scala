// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.documents.search

import java.time.Instant
import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration

import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.{PortalPosition, PortalWrapper}
import design.anduin.components.progress.react.BarIndicatorR
import design.anduin.components.textbox.TextBox
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*
import org.scalajs.dom.KeyCode

import anduin.dataroom.detail.documents.search.DataRoomSearchPanel.SearchType
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.stargazer.service.dataroom.QuickSearchDataRoomParams
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.endpoints.{DmsSearchResult, FileSearchResult, FolderSearchResult}
import stargazer.model.routing.Page

final case class DataRoomSearchBar(
  router: RouterCtl[Page],
  dataRoomWorkflowId: DataRoomWorkflowId,
  showSemanticSearch: Boolean = false,
  placeholder: String = "",
  width: Int,
  onChange: String => Callback,
  onSearch: (String, SearchType) => Callback
) {
  def apply(): VdomElement = DataRoomSearchBar.component(this)
}

object DataRoomSearchBar {

  private type Props = DataRoomSearchBar

  private val searchDelayInMilliseconds: Long = 500

  private val searchDelayDuration = FiniteDuration(searchDelayInMilliseconds, TimeUnit.MILLISECONDS)

  private final case class State(
    currentQuery: String = "",
    lastQuery: String = "",
    results: List[DmsSearchResult] = List.empty,
    isSearching: Boolean = false,
    scheduledSearchTime: Option[Instant] = None
  )

  private class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomNode = {
      <.div(
        tw.wPc100,
        PopoverR(
          renderTarget = renderSearchBox(props, state),
          renderContent = renderResults(props, state),
          position = PortalPosition.BottomCenter,
          targetWrapper = PortalWrapper.Block,
          isAutoFocus = false
        )()
      )
    }

    private def hideResult(toggleResult: Callback, isShowingResult: Boolean) =
      Callback.when(isShowingResult)(toggleResult)

    private def chooseSuitableSearchType(props: Props, state: State) = {
      state.results match {
        case Nil => Option.when(props.showSemanticSearch)(SearchType.Content).getOrElse(SearchType.Document)
        case results =>
          Option
            .when(results.forall {
              case _: FileSearchResult   => false
              case _: FolderSearchResult => true
            })(SearchType.Folder)
            .getOrElse(SearchType.Document)
      }
    }

    private def renderSearchBox(props: Props, state: State)(toggleResult: Callback, isShowingResult: Boolean) = {
      <.div(
        ComponentUtils.testId(DataRoomSearchBar, "SearchInput"),
        ^.onClick --> {
          Callback.when(!isShowingResult && state.currentQuery.trim.nonEmpty)(onSearch >> toggleResult)
        },
        TextBox(
          value = state.currentQuery,
          onChange = onQueryChange(
            props,
            toggleResult,
            isShowingResult
          ),
          icon = Some(Icon.Glyph.Search),
          placeholder = props.placeholder,
          onClear = Some(onQueryClear(props, toggleResult)),
          onKeyDown = { (e: ReactKeyboardEventFromInput) =>
            val keyCode = e.keyCode
            Callback.when(keyCode == KeyCode.Enter) {
              props.onSearch(state.currentQuery, chooseSuitableSearchType(props, state)) >> hideResult(
                toggleResult,
                isShowingResult
              )
            }
          }
        )()
      )
    }

    private def onQueryChange(props: Props, toggleResult: Callback, isShowingResult: Boolean)(newQuery: String) = {
      scope.modState(
        _.copy(
          currentQuery = newQuery,
          scheduledSearchTime = Some(Instant.now().plusMillis(searchDelayInMilliseconds))
        ),
        // showing result + empty query => hide result
        // hidden result + non-empty query => show result
        props.onChange(newQuery) >> Callback.when(isShowingResult == newQuery.isEmpty)(toggleResult)
      )
    }

    private def onQueryClear(props: Props, toggleResult: Callback) = {
      scope.modState(
        _.copy(
          currentQuery = "",
          scheduledSearchTime = None
        ),
        props.onChange("") >> toggleResult
      )
    }

    private def renderResults(props: Props, state: State)(closeResult: Callback) = {
      <.div(
        ^.width := s"${props.width - 16}px",
        if (state.isSearching || state.scheduledSearchTime.exists(_.isAfter(Instant.now()))) {
          <.div(
            tw.textPrimary4.p12,
            BarIndicatorR()()
          )
        } else {
          if (state.results.isEmpty) {
            DataRoomSearchResult(
              props.router,
              props.dataRoomWorkflowId,
              None,
              Callback.empty
            )()
          } else {
            <.div(
              tw.flex.flexCol,
              state.results.map { result =>
                <.div(
                  ^.key := result.itemId.toString,
                  DataRoomSearchResult(
                    props.router,
                    props.dataRoomWorkflowId,
                    Some(result),
                    closeResult
                  )()
                )
              }.toVdomArray,
              renderDivider,
              renderViewAllResults(props, state)(closeResult)
            )
          }
        }
      )
    }

    private def renderViewAllResults(props: Props, state: State)(closeResult: Callback): VdomNode = {
      <.button(
        tw.flex.itemsCenter.hPx48.p12,
        tw.rounded4.hover(tw.bgGray4),
        IconR(Icon.Glyph.Eye)(),
        ^.onClick --> {
          props.onSearch(state.currentQuery, chooseSuitableSearchType(props, state)) >> closeResult
        },
        <.div(
          tw.ml12.truncate,
          "View all results"
        )
      )
    }

    private def renderDivider = {
      <.div(tw.wPc100.hPx1.bgGray3.my8)
    }

    private def clearScheduledSearchTime(scheduledSearchTime: Option[Instant], state: State) = {
      if (state.scheduledSearchTime != scheduledSearchTime) state.scheduledSearchTime else None
    }

    def onSearch: Callback = {
      val searchCallback: Callback = for {
        props <- scope.props
        state <- scope.state
        scheduledSearchTime = state.scheduledSearchTime
        _ <-
          if (state.currentQuery.trim.nonEmpty) {
            Callback.when(!state.isSearching && scheduledSearchTime.exists(_.isBefore(Instant.now))) {
              scope.modState(
                _.copy(isSearching = true, results = List.empty),
                ZIOUtils.toReactCallback {
                  DataRoomEndpointClient
                    .quickSearch(
                      QuickSearchDataRoomParams(
                        dataRoomWorkflowId = props.dataRoomWorkflowId,
                        query = state.currentQuery.trim
                      )
                    )
                    .map { r =>
                      scope.modState(
                        state =>
                          state.copy(
                            isSearching = false,
                            scheduledSearchTime = clearScheduledSearchTime(scheduledSearchTime, state)
                          ),
                        r.fold(
                          _ => Callback.empty,
                          resp => scope.modState(_.copy(results = resp.results, lastQuery = state.currentQuery))
                        )
                      )
                    }
                }
              )
            }
          } else {
            scope.modState(state =>
              state.copy(
                results = List.empty,
                lastQuery = state.currentQuery,
                isSearching = false,
                scheduledSearchTime = clearScheduledSearchTime(scheduledSearchTime, state)
              )
            )
          }
      } yield ()
      searchCallback.debounce(searchDelayDuration)
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidUpdate { scope =>
      val isDataRoomChanged = scope.prevProps.dataRoomWorkflowId != scope.currentProps.dataRoomWorkflowId
      val isQueryChanged = (scope.prevState.currentQuery != scope.currentState.currentQuery)
      val shouldRefresh =
        (scope.prevState.isSearching && !scope.currentState.isSearching && scope.currentState.currentQuery != scope.currentState.lastQuery)
      Callback.when(isDataRoomChanged || isQueryChanged || shouldRefresh) {
        scope.backend.onSearch
      }
    }
    .build

}
