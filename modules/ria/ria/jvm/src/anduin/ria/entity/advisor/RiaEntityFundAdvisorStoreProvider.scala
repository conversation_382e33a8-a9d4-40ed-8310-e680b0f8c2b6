// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.ria.entity.advisor

import com.apple.foundationdb.record.RecordMetaDataBuilder
import com.apple.foundationdb.record.metadata.{Index, Key}

import anduin.fdb.record
import anduin.fdb.record.model.FDBTupleConverter
import anduin.fdb.record.model.common.RadixIdTuple.StringPartType
import anduin.fdb.record.{FDBRecordEnum, FDBRecordKeySpace, FDBRecordStoreProvider, FDBStoreProviderCompanion}
import anduin.id.ria.{RiaEntityId, RiaFundGroupId}
import anduin.model.common.user.UserId
import anduin.protobuf.ria.advisor.entity.{RiaEntityFundAdvisorModel, RiaEntityFundAdvisorProto}
import anduin.fdb.record.model.common.{RadixIdTupleConverter, RadixIdTuple}
import anduin.protobuf.ria.RiaEntityFundAdvisorRole
import com.apple.foundationdb.tuple.Tuple

final case class RiaEntityFundAdvisorStoreProvider(
  override protected val keySpace: FDBRecordKeySpace
) extends FDBRecordStoreProvider[FDBRecordEnum.FundAdvisor.type](
      FDBRecordEnum.FundAdvisor,
      RiaEntityFundAdvisorProto
    ) {

  override protected def indexes: Seq[IndexMappingWithVersion] = Seq(
    RiaEntityFundAdvisorStoreProvider.roleAndAdvisorIndexMapping -> 1,
    RiaEntityFundAdvisorStoreProvider.userAndRiaEntityIndexMapping -> 2,
    RiaEntityFundAdvisorStoreProvider.riaFundGroupIdIndexMapping -> 3
  )

  override protected def recordBuilderFn(builder: RecordMetaDataBuilder): Unit =
    builder
      .getRecordType(RiaEntityFundAdvisorModel.scalaDescriptor.name)
      .setPrimaryKey(RiaEntityFundAdvisorStoreProvider.primaryKeyExpression)

}

object RiaEntityFundAdvisorStoreProvider extends FDBStoreProviderCompanion[FDBRecordEnum.FundAdvisor.type] {

  final case class RiaEntityFundAdvisorPrimaryKey(
    riaFundGroupId: RiaFundGroupId,
    advisor: UserId
  )

  private val riaEntityIdKeyExpression = Key.Expressions.field("ria_fund_group_id").nest("parent")
  given riaEntityIdTupleConverter: FDBTupleConverter[RiaEntityId] = RadixIdTupleConverter.instance[RiaEntityId]

  private val riaFundGroupIdKeyExpression = Key.Expressions
    .field("ria_fund_group_id")
    .nest(
      Key.Expressions.field("parent"),
      Key.Expressions.field("value")
    )

  given riaFundGroupIdTupleConverter: FDBTupleConverter[RiaFundGroupId] = FDBTupleConverter(
    size = 2,
    fromTuple = (tuple, index) =>
      RadixIdTuple.getIdFromTuple(
        tuple,
        startAt = index
      )(
        StringPartType,
        StringPartType
      ),
    toTuple = { fundGroupId =>
      Tuple.from(fundGroupId.parent.value.value, fundGroupId.value.value)
    }
  )

  private val riaEntityFundAdvisorRoleKeyExpression = Key.Expressions.field("role")

  given riaEntityFundAdvisorRoleTupleConverter: FDBTupleConverter[RiaEntityFundAdvisorRole] =
    FDBTupleConverter.generatedEnum[RiaEntityFundAdvisorRole]

  private val userIdKeyExpression = Key.Expressions.field("advisor")
  given userIdTupleConverter: FDBTupleConverter[UserId] = FDBTupleConverter.userId

  private val primaryKeyExpression = Key.Expressions.concat(
    riaFundGroupIdKeyExpression,
    userIdKeyExpression
  )

  given primaryKeyTupleConverter: FDBTupleConverter[RiaEntityFundAdvisorPrimaryKey] =
    riaFundGroupIdTupleConverter
      .concat(userIdTupleConverter)
      .biMap[RiaEntityFundAdvisorPrimaryKey] { case (riaFundGroupId, advisor) =>
        RiaEntityFundAdvisorPrimaryKey(
          riaFundGroupId = riaFundGroupId,
          advisor = advisor
        )
      } { key =>
        (key.riaFundGroupId, key.advisor)
      }

  given riaEntityFundAdvisorRoleAndUserIdTupleConverter: FDBTupleConverter[(RiaEntityFundAdvisorRole, UserId)] =
    riaEntityFundAdvisorRoleTupleConverter.concat(
      userIdTupleConverter
    )

  given userIdRiaFundGroupIdTupleConverter: FDBTupleConverter[(UserId, RiaFundGroupId)] =
    userIdTupleConverter.concat(
      riaFundGroupIdTupleConverter
    )

  given userIdRiaEntityIdTupleConverter: FDBTupleConverter[(UserId, RiaEntityId)] =
    userIdTupleConverter.concat(
      riaEntityIdTupleConverter
    )

  val roleAndAdvisorIndexMapping: IndexMapping[RiaEntityFundAdvisorModel] = mappingIndexInstance(
    new Index(
      "role_and_advisor",
      Key.Expressions.concat(riaEntityFundAdvisorRoleKeyExpression, userIdKeyExpression)
    )
  )

  val userAndRiaEntityIndexMapping: IndexMapping[RiaEntityFundAdvisorModel] = mappingIndexInstance(
    new Index(
      "user_and_ria_entity",
      Key.Expressions.concat(userIdKeyExpression, riaEntityIdKeyExpression)
    )
  )

  val riaFundGroupIdIndexMapping: IndexMapping[RiaEntityFundAdvisorModel] = mappingIndexInstance(
    new Index(
      "ria_fund_group_id",
      riaFundGroupIdKeyExpression
    )
  )

  given riaEntityAdvisorModelPrimaryKeyMapping: Mapping[RiaEntityFundAdvisorPrimaryKey, RiaEntityFundAdvisorModel] =
    mappingInstance

}
