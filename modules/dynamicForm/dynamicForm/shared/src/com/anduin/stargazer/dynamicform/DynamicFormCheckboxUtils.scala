// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.dynamicform

import io.circe.Json
import io.circe.syntax.*

object DynamicFormCheckboxUtils {
  val CheckBoxTrue = "true"
  val CheckBoxFalse = "false"
  val OldMultiCheckboxSeparator = ","

  // Support old form rule which check whether the multiple checkbox value is empty by comparing with ""
  // Support old form rule which check whether the multiple checkbox only selects one specific option by using ==
  private def shouldUseOldValueFormat(values: Set[String]): Boolean = {
    values.size <= 1 && values.forall(!_.contains(OldMultiCheckboxSeparator))
  }

  def encodeMultipleCheckboxValueToJson(values: Set[String]): Json = {
    if (shouldUseOldValueFormat(values)) {
      Json.fromString(values.headOption.getOrElse(""))
    } else {
      values.toSeq.asJson
    }
  }

  def encodeMultipleCheckboxValueToString(values: Set[String]): String = {
    if (shouldUseOldValueFormat(values)) {
      values.headOption.getOrElse("")
    } else {
      values.toSeq.asJson.noSpaces
    }
  }

  def decodeMultipleCheckboxValueFromString(value: String): Seq[String] = {
    io.circe.parser
      .decode[Seq[String]](value)
      .toOption
      .getOrElse(
        // If cannot parse new json format, fallback to old format decoder
        value.split(OldMultiCheckboxSeparator).toSeq
      )
      .map(_.trim)
  }

  def decodeMultipleCheckboxValueAsSet(value: String): Set[String] = {
    decodeMultipleCheckboxValueFromString(value).toSet
  }

  def convertToStringValue(jsonValue: Json): String = {
    if (jsonValue.as[Seq[String]].isRight) {
      jsonValue.noSpaces
    } else {
      jsonValue.asString.getOrElse("")
    }
  }

  def convertToJsonValue(strValue: String): Json =
    encodeMultipleCheckboxValueToJson(decodeMultipleCheckboxValueFromString(strValue).toSet)

}
