// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dashboard.impl

import anduin.fdb.record.FDBRecordDatabase
import anduin.fundsub.models.FundSubModelStoreOperations
import anduin.protobuf.fundsub.models.FundSubPublicModel.FundType
import anduin.workflow.TemporalWorkflowService
import anduin.workflow.fundsub.dashboard.{AllFundIdsResponse, FundSubSyncDashboardActivities}

import java.time.Instant

final case class FundSubSyncDashboardActivitiesImpl(
)(
  using val temporalWorkflowService: TemporalWorkflowService
) extends FundSubSyncDashboardActivities {

  override def getFundIds: AllFundIdsResponse = {
    temporalWorkflowService.executeTask(
      FDBRecordDatabase
        .transact(FundSubModelStoreOperations.Production)(_.getAllFundSubPublicModel)
        .map { allFundModels =>
          val aWeekAgo = Instant
            .now()
            .minusSeconds(60 * 60 * 24 * 7)
          val filteredAndSortedFundModels = allFundModels
            .filter { fsPublicModel =>
              fsPublicModel.lastDashboardMutationAt
                .exists { lastDashboardMutationAt =>
                  lastDashboardMutationAt.isAfter(aWeekAgo)
                }
            }
            .sortBy { fundModel =>
              val fundTypePriorityScore = fundModel.fundType match {
                case FundType.Production => 0
                case FundType.External   => 1
                case FundType.Internal   => 2
                case _                   => 3
              }
              val lastActivityPriorityScore =
                fundModel.lastActivityAt
                  .fold(Long.MaxValue)(lastActivityInstant => Long.MaxValue - lastActivityInstant.getEpochSecond)
              (fundTypePriorityScore, lastActivityPriorityScore)
            }
          AllFundIdsResponse(filteredAndSortedFundModels.map(_.fundSubId))
        },
      "FundSubSyncDashboardActivities#getAllFundIds"
    )
  }

}
