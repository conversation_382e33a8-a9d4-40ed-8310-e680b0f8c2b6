// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.fundsub.datalakeingestion.handler

import anduin.evendim.client.EvendimClient
import anduin.fundsub.datalakeingestion.model.RemoveAdvisorTagParams
import anduin.kafka.KafkaAsyncExecutor
import fundsub.webhook.WebhookPayload
import java.time.Instant
import scala.annotation.unused

import anduin.evendim.model.datalake.*
import zio.Task

import anduin.id.fundsub.FundSubId

object RemoveAdvisorTagHandler extends DataLakeIngestionHandler[RemoveAdvisorTagParams] {

  private def unlinkAdvisorTagMutation(params: RemoveAdvisorTagParams) = {
    val fundIdStr = params.id.parent.idString
    val fundSubFilter = FundSubscriptionFilter(id = Option(StringHashFilter(eq = Option(fundIdStr))))
    Mutation.updateFundSubscription(
      UpdateFundSubscriptionInput(
        filter = fundSubFilter,
        remove = Option(
          FundSubscriptionPatch(
            advisorTags = Option(
              List(AdvisorTagRef(id = Option(params.id.idString)))
            )
          )
        )
      )
    )(
      UpdateFundSubscriptionPayload.numUids
    )
  }

  private def removeAdvisorTagNodeMutation(params: RemoveAdvisorTagParams) = {
    Mutation.deleteAdvisorTag(AdvisorTagFilter(id = Option(StringHashFilter(eq = Option(params.id.idString)))))(
      DeleteAdvisorTagPayload.numUids
    )
  }

  override def execute(
    params: RemoveAdvisorTagParams,
    now: Option[Instant]
  )(
    using evendimClient: EvendimClient,
    @unused webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    (evendimClient.mutation(unlinkAdvisorTagMutation(params) ~ removeAdvisorTagNodeMutation(params))).map(_ => ())
  }

}
