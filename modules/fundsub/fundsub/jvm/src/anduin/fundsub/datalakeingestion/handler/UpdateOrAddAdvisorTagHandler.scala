// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.fundsub.datalakeingestion.handler

import anduin.evendim.client.EvendimClient
import anduin.fundsub.datalakeingestion.model.UpdateOrAddAdvisorTagParams
import anduin.fundsub.datalakeingestion.utils.DataConversionUtils
import anduin.kafka.KafkaAsyncExecutor
import fundsub.webhook.WebhookPayload
import java.time.Instant
import scala.annotation.unused

import anduin.evendim.model.datalake.*
import zio.Task

import anduin.id.fundsub.FundSubId

object UpdateOrAddAdvisorTagHandler extends DataLakeIngestionHandler[UpdateOrAddAdvisorTagParams] {

  private def mutation(params: UpdateOrAddAdvisorTagParams) = {
    Mutation.addAdvisorTag(
      List(
        AddAdvisorTagInput(
          id = params.id.idString,
          fund = Option(FundSubscriptionRef(id = Option(params.id.parent.idString))),
          name = params.name,
          creator = params.creator.map(DataConversionUtils.toUserRef(_)),
          createdAt = Option(DataConversionUtils.toEvendimDateTime(params.createdAt))
        )
      ),
      upsert = Option(true)
    )(
      AddAdvisorTagPayload.numUids
    )
  }

  override def execute(
    params: UpdateOrAddAdvisorTagParams,
    now: Option[Instant] = None
  )(
    using evendimClient: EvendimClient,
    @unused webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    val _ = now
    evendimClient
      .mutation(mutation(params))
      .map(_ => ())
  }

}
