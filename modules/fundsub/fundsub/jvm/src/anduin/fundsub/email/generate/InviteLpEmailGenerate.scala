// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.email.generate

import yamusca.context.Context
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.actiontoken.TokenMetaDataUtils
import anduin.email.topic.{EmailTopic, FundSubInviteLp}
import anduin.fundsub.email.generate
import anduin.fundsub.email.generate.FundSubTokenUtils.{LinkType, MetaKey}
import anduin.id.account.EnterpriseLoginConfigId
import anduin.id.fundsub.FundSubLpId
import anduin.id.offering.OfferingId
import anduin.link.LinkGeneratorService
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.model.id.FileId
import anduin.portaluser.PortalUserService
import anduin.protobuf.actionlogger.event.FundSubEmailType
import anduin.protobuf.fundsub.{EmailTemplateMessage, FundSubEvent, FundSubLpRole}
import com.anduin.stargazer.service.GondorBackendConfig.EmailConfig.EmailDomainType
import com.anduin.stargazer.service.email.generate.common.Terms
import com.anduin.stargazer.service.email.{EmailPriority, OutgoingEmailVariable}
import stargazer.model.routing.DynamicAuthPage

final case class InviteLpEmailGenerate(
  inviter: UserId,
  invitee: UserId,
  lpId: FundSubLpId,
  attachedDocs: Seq[FileId] = Seq.empty,
  override val userCustomTemplateOpt: Option[EmailTemplateMessage] = None,
  enableSSO: Boolean = false
)(
  override val fundSubEmailUtils: FundSubEmailUtils
)(
  using val linkGeneratorService: LinkGeneratorService,
  val userProfileService: UserProfileService,
  portalUserService: PortalUserService
) extends FundSubEmailGenerate
    with FundSubEmailTemplate {

  override val priority: EmailPriority = EmailPriority.High

  override val topic: EmailTopic = FundSubInviteLp(fundSubId)

  protected override def fundSubLpIdOpt: Option[FundSubLpId] = Some(lpId)

  protected override def emailEvent: FundSubEvent = FundSubEvent.inviteLp

  protected override def actorOpt: Option[UserId] = Some(inviter)

  protected override def templateName: String = "fundsub/lp_invitation"

  protected override def getSenderAddress: Task[EmailAddress] = {
    fundSubEmailUtils.getUserSenderByTheme(
      userProfileService,
      portalUserService,
      inviter,
      lpId.parent,
      EmailDomainType.HighEngagement
    )
  }

  override protected def getReceiverUserIds: Task[Seq[UserId]] = ZIO.attempt(Seq(invitee))

  override protected def getAdditionalTerms: Task[List[Terms]] = {
    for {
      enterpriseLoginConfigIdOpt <-
        if (enableSSO) {
          fundSubEmailUtils.getEnterpriseLoginConfigIdOpt(fundSubId)
        } else {
          ZIO.attempt(Option.empty[EnterpriseLoginConfigId])
        }
      redirectUrlTerm = generate.RedirectSSOUrlTerm(
        offeringId = OfferingId.FundSub,
        tag = "redirectUrl",
        pageTask = ZIO.attempt(DynamicAuthPage.FundSubLpPage(lpId)),
        userId = Some(invitee),
        tokenMetaIdTask = TokenMetaDataUtils
          .createTokenMetaData(
            Map(
              MetaKey.FundSubLpIdKey.name -> lpId.idString,
              MetaKey.LinkTypeKey.name -> LinkType.ConfirmInvitation.name,
              MetaKey.UserRoleKey.name -> FundSubLpRole.LpCollaborator.name
            )
          )
          .map(Some(_)),
        linkRestrictionListIdTask = fundSubEmailUtils.getLinkRestrictionListId(lpId.parent),
        requiredDisclaimerTask = ZIO.attempt(Some(lpId.parent)),
        fundSubId = fundSubId,
        enterpriseLoginConfigIdOpt = enterpriseLoginConfigIdOpt
      )
    } yield {
      List(redirectUrlTerm)
    }
  }

  override protected def attachments: Seq[FileId] = attachedDocs

  override protected def getAdditionalMapping: Task[Context] = {
    for {
      mapping <- fundSubEmailUtils.getLpInfoMapping(lpId)
      terms <- baseTerms
      userVariableMappings <- getUserVariableMappings
      res <- getTemplateMapping(
        mapping,
        terms,
        userVariableMappings
      )
    } yield res
  }

  override def getVariables: Task[Map[OutgoingEmailVariable, String]] = {
    for {
      inviteeEmail <- userProfileService.getEmailAddress(invitee)
      inviterEmail <- userProfileService.getEmailAddress(inviter)
    } yield Map(
      OutgoingEmailVariable.FundSubLpIdVariable -> lpId.idString,
      OutgoingEmailVariable.FundSubEmailTypeVariable -> FundSubEmailType.Invitation.name,
      OutgoingEmailVariable.RecipientVariable -> inviteeEmail.address,
      OutgoingEmailVariable.ActorVariable -> inviterEmail.address
    )
  }

}
