// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.models

import com.apple.foundationdb.record.RecordMetaDataBuilder
import com.apple.foundationdb.record.metadata.{Index, Key}

import anduin.fdb.record.*
import anduin.fdb.record.model.common.RadixIdTupleConverter
import anduin.fdb.record.model.{FDBIndexMapping, FDBRecordKey, FDBTupleConverter}
import anduin.id.ModelIdRegistry
import anduin.id.environment.EnvironmentId
import anduin.id.fundsub.*
import anduin.protobuf.fundsub.models.*

final case class FundSubModelStoreProvider(
  override protected val keySpace: FDBRecordKeySpace
) extends FDBRecordStoreProvider[FDBRecordEnum.FundSubModelStore.type](
      FDBRecordEnum.FundSubModelStore,
      FundSubModelsProto
    ) {

  protected def recordBuilderFn(builder: RecordMetaDataBuilder): Unit = {
    builder
      .getRecordType(FundSubAdminRestrictedModel.scalaDescriptor.name)
      .setPrimaryKey(FundSubModelStoreProvider.fsAdminRstPrimaryKeyExpression)
    builder
      .getRecordType(FundSubPublicModel.scalaDescriptor.name)
      .setPrimaryKey(FundSubModelStoreProvider.fsPublicPrimaryKeyExpression)
    builder
      .getRecordType(FundSubAdminInfo.scalaDescriptor.name)
      .setPrimaryKey(FundSubModelStoreProvider.fsAdminInfoPrimaryKeyExpression)
    builder
      .getRecordType(FundSubCloseModel.scalaDescriptor.name)
      .setPrimaryKey(FundSubModelStoreProvider.fsCloseModelPrimaryKeyExpression)
    builder
      .getRecordType(FundSubReportingModel.scalaDescriptor.name)
      .setPrimaryKey(FundSubModelStoreProvider.fsReportingModelPrimaryKeyExpression)
    builder
      .getRecordType(anduin.protobuf.fundsub.FundSubAdvisorTag.scalaDescriptor.name)
      .setPrimaryKey(FundSubModelStoreProvider.fsAdvisorTagPrimaryKeyExpression)
  }

  protected def indexes: Seq[IndexMappingWithVersion] = {
    Seq(
      FundSubModelStoreProvider.environmentIdOptIndexMapping -> 1,
      FundSubModelStoreProvider.fundCustomIdIndexMapping -> 2
    )
  }

}

object FundSubModelStoreProvider extends FDBStoreProviderCompanion[FDBRecordEnum.FundSubModelStore.type] {

  // ---------- FundSubAdminRestrictedModel ----------
  private type FundSubAdminRestrictedPrimaryKey = FundSubAdminRestrictedId

  private val fsAdminRstPrimaryKeyExpression = Key.Expressions.field("fund_sub_admin_restricted_id")

  private given fsAdminRstPrimaryKeyTupleConverter: FDBTupleConverter[FundSubAdminRestrictedPrimaryKey] =
    RadixIdTupleConverter.instance[FundSubAdminRestrictedId]

  private given fsAdminRstPrimaryKeyFDBRecordKey: FDBRecordKey[FundSubAdminRestrictedPrimaryKey] = {
    FDBRecordKey.fromTupleConverter(
      using fsAdminRstPrimaryKeyTupleConverter
    )
  }

  private val fundCustomIdKeyExpression = Key.Expressions.concat(
    Key.Expressions
      .field("custom_fund_id_setting")
      .nest(Key.Expressions.concat(Key.Expressions.field("custom_fund_id"), scalarFieldNotNull("is_enabled"))),
    fsAdminRstPrimaryKeyExpression
  )

  private[models] final case class CustomIdKey(
    customId: String,
    isEnabled: Boolean
  )

  private[models] given customIdTupleConverter: FDBTupleConverter[CustomIdKey] =
    FDBTupleConverter.string
      .concat(FDBTupleConverter.bool)
      .biMap { (customId, isEnabled) =>
        CustomIdKey(customId, isEnabled)
      } { customIdKey => (customIdKey.customId, customIdKey.isEnabled) }

  private given fundCustomIdTupleConverter: FDBTupleConverter[(CustomIdKey, FundSubAdminRestrictedId)] =
    customIdTupleConverter.concat(fsAdminRstPrimaryKeyTupleConverter)

  val fundCustomIdIndexMapping: KeyValueIndexMapping[
    (CustomIdKey, FundSubAdminRestrictedId),
    Unit,
    FundSubAdminRestrictedModel
  ] = {
    keyValueIndexMapping(
      new Index(
        "fund_custom_id_index",
        fundCustomIdKeyExpression
      )
    )
  }

  given fsAdminRstMapping: Mapping[FundSubAdminRestrictedPrimaryKey, FundSubAdminRestrictedModel] =
    mappingInstance

  // ---------- FundSubPublicModel ----------

  private type FundSubPublicPrimaryKey = FundSubId

  private val fsPublicPrimaryKeyExpression = Key.Expressions.field("fund_sub_id")

  private given fsPublicPrimaryKeyTupleConverter: FDBTupleConverter[FundSubPublicPrimaryKey] =
    RadixIdTupleConverter.instance[FundSubId]

  private given fsPublicPrimaryKeyFDBRecordKey: FDBRecordKey[FundSubPublicPrimaryKey] = {
    FDBRecordKey.fromTupleConverter(
      using fsPublicPrimaryKeyTupleConverter
    )
  }

  given fsPublicMapping: Mapping[FundSubPublicPrimaryKey, FundSubPublicModel] =
    mappingInstance

  val environmentIdOptIndexMapping: FDBIndexMapping[FundSubPublicModel, FDBRecordEnum.FundSubModelStore.type] =
    FDBIndexMapping.instance[FundSubPublicModel, FDBRecordEnum.FundSubModelStore.type](
      new Index("environment_id_index", Key.Expressions.field("environment_id_opt"))
    )

  val environmentIdOptTupleConverter: FDBTupleConverter[Option[EnvironmentId]] = FDBTupleConverter.string.biMap(
    ModelIdRegistry.parser.parseAs[EnvironmentId]
  )(
    _.fold("")(_.idString)
  )

  // ---------- FundSubAdminInfo ----------

  private type FundSubAdminInfoPrimaryKey = FundSubAdminInfoId

  private val fsAdminInfoPrimaryKeyExpression = Key.Expressions.field("fund_sub_admin_info_id")

  private given fsAdminInfoPrimaryKeyTupleConverter: FDBTupleConverter[FundSubAdminInfoPrimaryKey] =
    RadixIdTupleConverter.instance[FundSubAdminInfoId]

  private given fsAdminInfoPrimaryKeyFDBRecordKey: FDBRecordKey[FundSubAdminInfoPrimaryKey] = {
    FDBRecordKey.fromTupleConverter(
      using fsAdminInfoPrimaryKeyTupleConverter
    )
  }

  given fsAdminInfoMapping: Mapping[FundSubAdminInfoPrimaryKey, FundSubAdminInfo] =
    mappingInstance

  // ---------- FundSubCloseModel ----------

  private type FundSubCloseModelPrimaryKey = FundSubCloseId

  private val fsCloseModelPrimaryKeyExpression = Key.Expressions.field("fund_sub_close_id")

  private given fsCloseModelPrimaryKeyTupleConverter: FDBTupleConverter[FundSubCloseModelPrimaryKey] =
    RadixIdTupleConverter.instance[FundSubCloseId]

  private given fsCloseModelPrimaryKeyFDBRecordKey: FDBRecordKey[FundSubCloseModelPrimaryKey] = {
    FDBRecordKey.fromTupleConverter(
      using fsCloseModelPrimaryKeyTupleConverter
    )
  }

  given fsCLoseModelMapping: Mapping[FundSubCloseModelPrimaryKey, FundSubCloseModel] =
    mappingInstance

  // ---------- FundSubReportingModel ----------

  private type FundSubReportingModelPrimaryKey = FundSubReportingId

  private val fsReportingModelPrimaryKeyExpression = Key.Expressions.field("fund_sub_reporting_id")

  private given fsReportingModelPrimaryKeyTupleConverter: FDBTupleConverter[FundSubReportingModelPrimaryKey] =
    RadixIdTupleConverter.instance[FundSubReportingId]

  private given fsReportingModelPrimaryKeyFDBRecordKey: FDBRecordKey[FundSubReportingModelPrimaryKey] =
    FDBRecordKey.fromTupleConverter(
      using fsReportingModelPrimaryKeyTupleConverter
    )

  given fsReportingModelMapping: Mapping[FundSubReportingModelPrimaryKey, FundSubReportingModel] = mappingInstance

  // ---------- FundSubAdvisorTag ----------

  private type FundSubAdvisorTagPrimaryKey = FundSubAdvisorTagId

  private val fsAdvisorTagPrimaryKeyExpression = Key.Expressions.field("id")

  private given fsAdvisorTagPrimaryKeyTupleConverter: FDBTupleConverter[FundSubAdvisorTagPrimaryKey] =
    RadixIdTupleConverter.instance[FundSubAdvisorTagId]

  private given fsAdvisorTagPrimaryKeyFDBRecordKey: FDBRecordKey[FundSubAdvisorTagPrimaryKey] =
    FDBRecordKey.fromTupleConverter(
      using fsAdvisorTagPrimaryKeyTupleConverter
    )

  given fsAdvisorTagMapping: Mapping[FundSubAdvisorTagPrimaryKey, anduin.protobuf.fundsub.FundSubAdvisorTag] = mappingInstance
}
