// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.models

import com.apple.foundationdb.record.{IndexScanType, TupleRange}

import anduin.fdb.record.FDBOperations
import anduin.fdb.record.model.{RecordIO, RecordReadTask, RecordTask}
import anduin.fundsub.models.FundSubModelStoreProvider.{*, given}
import anduin.id.environment.EnvironmentId
import anduin.id.fundsub.*
import anduin.protobuf.fundsub.models.*

final case class FundSubModelStoreOperations(store: Store) {

  // -------------------------------- FundSubAdminRestrictedModel
  def createFundSubAdminRestrictedModel(model: FundSubAdminRestrictedModel): RecordTask[Unit] = {
    store.create(model).unit
  }

  def getFundSubAdminRestrictedModel(fundSubId: FundSubId): RecordReadTask[FundSubAdminRestrictedModel] = {
    store.get(FundSubAdminRestrictedId(fundSubId))
  }

  def getFundSubAdminRestrictedModelByCustomId(customId: String): RecordTask[List[FundSubAdminRestrictedId]] = {
    val tupleRange = TupleRange.allOf(
      customIdTupleConverter
        .toTuple(
          CustomIdKey(
            customId = customId,
            isEnabled = true
          )
        )
    )
    store
      .scanKeyValueL(
        mapping = fundCustomIdIndexMapping,
        tupleRange = tupleRange,
        indexScanType = IndexScanType.BY_VALUE
      )
      .map(_.map(_._1._2))
  }

  def getFundSubAdminRestrictedModel(id: FundSubAdminRestrictedId): RecordReadTask[FundSubAdminRestrictedModel] = {
    store.get(id)
  }

  def getOptFundSubAdminRestrictedModel(id: FundSubAdminRestrictedId)
    : RecordReadTask[Option[FundSubAdminRestrictedModel]] = {
    store.getOpt(id)
  }

  def updateFundSubAdminRestrictedModel(
    id: FundSubAdminRestrictedId
  )(
    fn: FundSubAdminRestrictedModel => FundSubAdminRestrictedModel
  ): RecordTask[FundSubAdminRestrictedModel] = {
    for {
      model <- store.get(id)
      newModel <- RecordIO.succeed(fn(model))
      _ <- store.update(newModel)
    } yield newModel
  }

  // -------------------------------- FundSubPublicModel
  def createFundSubPublicModel(model: FundSubPublicModel): RecordTask[Unit] = {
    store.create(model).unit
  }

  def getFundSubPublicModel(id: FundSubId): RecordReadTask[FundSubPublicModel] = {
    store.get(id)
  }

  def getOptFundSubPublicModel(id: FundSubId): RecordReadTask[Option[FundSubPublicModel]] = {
    store.getOpt(id)
  }

  def getAllFundSubIds: RecordTask[List[FundSubId]] = {
    store
      .scanL(
        mapping = fsPublicMapping,
        tupleRange = TupleRange.ALL
      )
      .map(_.map(_._1))
  }

  /** to be deprecated */
  def getAllFundSubPublicModel: RecordTask[List[FundSubPublicModel]] = {
    store
      .scanL(
        mapping = fsPublicMapping,
        tupleRange = TupleRange.ALL
      )
      .map(_.map(_._2))
  }

  def updateFundSubPublicModel(
    id: FundSubId
  )(
    fn: FundSubPublicModel => FundSubPublicModel
  ): RecordTask[FundSubPublicModel] = {
    for {
      model <- store.get(id)
      newModel <- RecordIO.succeed(fn(model))
      _ <- store.update(newModel)
    } yield newModel
  }

  def queryFundsByEnvironment(environmentIdOpt: Option[EnvironmentId]): RecordTask[List[FundSubPublicModel]] = {
    val tupleRange = TupleRange.allOf(
      FundSubModelStoreProvider.environmentIdOptTupleConverter.toTuple(environmentIdOpt)
    )
    store.scanIndexRecordsL(
      FundSubModelStoreProvider.environmentIdOptIndexMapping,
      tupleRange,
      IndexScanType.BY_VALUE
    )
  }

  // -------------------------------- FundSubAdminInfo
  def getOptFundSubAdminInfo(id: FundSubAdminInfoId): RecordTask[Option[FundSubAdminInfo]] = {
    store.getOpt(id)
  }

  def upsertFundSubAdminInfo(
    id: FundSubAdminInfoId,
    defaultModel: FundSubAdminInfo,
    fn: FundSubAdminInfo => FundSubAdminInfo
  ): RecordTask[Unit] = {
    for {
      modelOpt <- store.getOpt(id)
      _ <- modelOpt.fold(store.create(defaultModel).unit) { model =>
        store.update(fn(model)).unit
      }
    } yield ()
  }

  // -------------------------------- FundSubCloseModel
  def createFundSubCloseModel(model: FundSubCloseModel): RecordTask[Unit] = {
    store.create(model).unit
  }

  def getFundSubCloseModel(id: FundSubCloseId): RecordReadTask[FundSubCloseModel] = {
    store.get(id)
  }

  def updateFundSubCloseModel(
    id: FundSubCloseId,
    fn: FundSubCloseModel => FundSubCloseModel
  ): RecordTask[FundSubCloseModel] = {
    for {
      model <- store.get(id)
      newModel <- RecordIO.succeed(fn(model))
      _ <- store.update(newModel)
    } yield newModel
  }

  // -------------------------------- FundSubReportingModel
  def getOptFundSubReportingModel(fundSubId: FundSubId): RecordTask[Option[FundSubReportingModel]] = {
    store.getOpt(FundSubReportingId(fundSubId))
  }

  def updateOrCreateFundSubReportingModel(
    fundSubId: FundSubId,
    model: FundSubReportingModel
  ): RecordTask[Unit] = {
    for {
      exist <- store.exist(FundSubReportingId(fundSubId))
      _ <- if (exist) store.update(model) else store.create(model)
    } yield ()
  }

  // -------------------------------- FundSubAdvisorTag
  def createFundSubAdvisorTag(model: anduin.protobuf.fundsub.FundSubAdvisorTag): RecordTask[Unit] = {
    store.create(model).unit
  }

  def getFundSubAdvisorTag(id: FundSubAdvisorTagId): RecordReadTask[anduin.protobuf.fundsub.FundSubAdvisorTag] = {
    store.get(id)
  }

  def getOptFundSubAdvisorTag(id: FundSubAdvisorTagId): RecordReadTask[Option[anduin.protobuf.fundsub.FundSubAdvisorTag]] = {
    store.getOpt(id)
  }

  def updateFundSubAdvisorTag(
    id: FundSubAdvisorTagId,
    fn: anduin.protobuf.fundsub.FundSubAdvisorTag => anduin.protobuf.fundsub.FundSubAdvisorTag
  ): RecordTask[anduin.protobuf.fundsub.FundSubAdvisorTag] = {
    for {
      model <- store.get(id)
      newModel <- RecordIO.succeed(fn(model))
      _ <- store.update(newModel)
    } yield newModel
  }

}

object FundSubModelStoreOperations
    extends FDBOperations.Single[RecordEnum, FundSubModelStoreOperations](FundSubModelStoreProvider)
