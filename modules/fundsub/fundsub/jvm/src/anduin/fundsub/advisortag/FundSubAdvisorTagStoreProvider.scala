// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.advisortag

import anduin.fdb.record.{FDBRecordEnum, FDBStoreProviderCompanion}
import anduin.fdb.record.mapping.{FDBIndexMapping, FDBTupleConverter}
import anduin.fdb.record.store.FDBStoreProvider
import anduin.id.fundsub.{FundSubAdvisorTagId, FundSubId}
import anduin.protobuf.fundsub.FundSubAdvisorTag
import anduin.radix.RadixIdTupleConverter
import com.apple.foundationdb.record.metadata.Index
import com.apple.foundationdb.record.metadata.expressions.Key

private[fundsub] object FundSubAdvisorTagStoreProvider
    extends FDBStoreProviderCompanion[FDBRecordEnum.FundSubAdvisorTagModelStore.type] {

  val RecordTypeName: String = FundSubAdvisorTag.scalaDescriptor.name

  val IdKey = "id"
  val FundSubIdKey = "fundSubId"
  val NameKey = "name"
  val IsRemovedKey = "isRemoved"

  val primaryKeyExpression = Key.Expressions.field(IdKey)
  val fundSubIdKeyExpression = Key.Expressions.field(FundSubIdKey)
  val nameKeyExpression = Key.Expressions.field(NameKey)
  val isRemovedKeyExpression = Key.Expressions.field(IsRemovedKey)

  val fundSubIdIndexMapping: FDBIndexMapping[FundSubAdvisorTag, FDBRecordEnum.FundSubAdvisorTagModelStore.type] =
    FDBIndexMapping.instance[FundSubAdvisorTag, FDBRecordEnum.FundSubAdvisorTagModelStore.type](
      new Index("fund_sub_id", fundSubIdKeyExpression)
    )

  val fundSubIdAndIsRemovedIndexMapping: FDBIndexMapping[FundSubAdvisorTag, FDBRecordEnum.FundSubAdvisorTagModelStore.type] =
    FDBIndexMapping.instance[FundSubAdvisorTag, FDBRecordEnum.FundSubAdvisorTagModelStore.type](
      new Index("fund_sub_id_is_removed", Key.Expressions.concatenateFields(FundSubIdKey, IsRemovedKey))
    )

  given primaryKeyTupleConverter: FDBTupleConverter[FundSubAdvisorTagId] =
    RadixIdTupleConverter.instance

  given fundSubIdTupleConverter: FDBTupleConverter[FundSubId] =
    RadixIdTupleConverter.instance

  type Store = FDBStoreProvider[FundSubAdvisorTag, FDBRecordEnum.FundSubAdvisorTagModelStore.type]

  val Production: Store = FDBStoreProvider.instance[FundSubAdvisorTag, FDBRecordEnum.FundSubAdvisorTagModelStore.type](
    FDBRecordEnum.FundSubAdvisorTagModelStore,
    primaryKeyExpression,
    Seq(
      fundSubIdIndexMapping,
      fundSubIdAndIsRemovedIndexMapping
    )
  )

}
