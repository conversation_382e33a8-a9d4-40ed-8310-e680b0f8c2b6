// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.advisortag

import anduin.fdb.record.{FDBCommonDatabase, FDBRecordDatabase}
import anduin.fundsub.datalakeingestion.FundSubDataLakeIngestionService
import anduin.fundsub.datalakeingestion.model.{RemoveAdvisorTagParams, UpdateOrAddAdvisorTagParams}
import anduin.fundsub.utils.FundSubDataLakeUtils
import anduin.id.fundsub.{FundSubAdvisorTagId, FundSubId}
import anduin.model.common.user.UserId
import anduin.protobuf.fundsub.{AdvisorTagColor, FundSubAdvisorTag}
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.util.date.DateCalculator
import zio.{Task, ZIO}

final case class FundSubAdvisorTagService(
  fundSubDataLakeIngestionService: FundSubDataLakeIngestionService
) {

  def createTag(
    fundSubId: FundSubId,
    actor: UserId,
    tagName: String,
    color: AdvisorTagColor
  ): Task[Option[FundSubAdvisorTag]] = {
    for {
      id <- ZIO.succeed(FundSubAdvisorTagIdFactory.unsafeRandomId(fundSubId))
      tag = FundSubAdvisorTag(
        id = id,
        fundSubId = fundSubId,
        name = tagName,
        color = color,
        creator = Some(actor),
        createdAt = Some(DateCalculator.instantNow)
      )
      addedTagOpt <- FDBRecordDatabase.transact(FundSubAdvisorTagStoreProvider.Production) { store =>
        for {
          tags <- FundSubAdvisorTagOperations(store).getTagsByFundSubId(fundSubId)
          allTagNames = tags.map(_.name)
          tagIsUnique = !allTagNames.exists(_.equalsIgnoreCase(tagName))
          res <- ZIOUtils.when(tagIsUnique) {
            FundSubAdvisorTagOperations(store).add(tag)
          }
        } yield res
      }
      _ <- ZIO.when(addedTagOpt.nonEmpty) {
        updateOrAddTagToDataLake(
          tagId = id,
          name = tagName,
          creatorOpt = Some(actor),
          createdAt = addedTagOpt.flatMap(_.createdAt)
        )
      }
    } yield addedTagOpt
  }

  def getTagsByFundSubId(fundSubId: FundSubId): Task[List[FundSubAdvisorTag]] = {
    FDBCommonDatabase().read(FundSubAdvisorTagStoreProvider.Production) { store =>
      FundSubAdvisorTagOperations(store).getTagsByFundSubId(fundSubId)
    }
  }

  def updateTag(
    tagId: FundSubAdvisorTagId,
    actor: UserId,
    newName: String,
    newColor: AdvisorTagColor
  ): Task[Option[FundSubAdvisorTag]] = {
    for {
      updatedTagOpt <- FDBRecordDatabase.transact(FundSubAdvisorTagStoreProvider.Production) { store =>
        for {
          tagOpt <- FundSubAdvisorTagOperations(store).getOpt(tagId)
          result <- tagOpt match {
            case Some(tag) if !tag.isRemoved =>
              val fundSubId = tag.fundSubId
              for {
                tags <- FundSubAdvisorTagOperations(store).getTagsByFundSubId(fundSubId)
                allTagNames = tags.filterNot(_.id == tagId).map(_.name)
                tagIsUnique = !allTagNames.exists(_.equalsIgnoreCase(newName))
                res <- ZIOUtils.when(tagIsUnique) {
                  FundSubAdvisorTagOperations(store).update(
                    tagId,
                    _.withName(newName).withColor(newColor)
                  )
                }
              } yield res
            case _ => ZIO.succeed(None)
          }
        } yield result
      }
      _ <- ZIO.when(updatedTagOpt.nonEmpty) {
        updateOrAddTagToDataLake(
          tagId = tagId,
          name = newName,
          creatorOpt = updatedTagOpt.flatMap(_.creator),
          createdAt = updatedTagOpt.flatMap(_.createdAt)
        )
      }
    } yield updatedTagOpt
  }

  def deleteTag(tagId: FundSubAdvisorTagId, actor: UserId): Task[Boolean] = {
    for {
      deletedOpt <- FDBRecordDatabase.transact(FundSubAdvisorTagStoreProvider.Production) { store =>
        for {
          tagOpt <- FundSubAdvisorTagOperations(store).getOpt(tagId)
          result <- tagOpt match {
            case Some(tag) if !tag.isRemoved =>
              FundSubAdvisorTagOperations(store).markAsRemoved(tagId).map(Some(_))
            case _ => ZIO.succeed(None)
          }
        } yield result
      }
      _ <- ZIO.when(deletedOpt.nonEmpty) {
        removeTagFromDataLake(tagId)
      }
    } yield deletedOpt.nonEmpty
  }

  private def updateOrAddTagToDataLake(
    tagId: FundSubAdvisorTagId,
    name: String,
    creatorOpt: Option[UserId],
    createdAt: Option[java.time.Instant]
  ): Task[Unit] = {
    val params = UpdateOrAddAdvisorTagParams(
      id = tagId,
      name = name,
      creator = creatorOpt.map { userId =>
        // This would need to be implemented to get user basic info
        // For now, creating a minimal structure
        anduin.fundsub.datalakeingestion.model.UserBasicInfo(
          id = userId,
          email = "", // Would need to fetch from user service
          firstName = "", // Would need to fetch from user service
          lastName = "" // Would need to fetch from user service
        )
      },
      createdAt = createdAt.getOrElse(DateCalculator.instantNow)
    )

    FundSubDataLakeUtils.sendUpdateParams(
      tagId.parent,
      params,
      fundSubDataLakeIngestionService
    )
  }

  private def removeTagFromDataLake(tagId: FundSubAdvisorTagId): Task[Unit] = {
    val params = RemoveAdvisorTagParams(id = tagId)
    FundSubDataLakeUtils.sendUpdateParams(
      tagId.parent,
      params,
      fundSubDataLakeIngestionService
    )
  }

}

object FundSubAdvisorTagService {
  import anduin.model.id.FundSubAdvisorTagIdFactory
}
