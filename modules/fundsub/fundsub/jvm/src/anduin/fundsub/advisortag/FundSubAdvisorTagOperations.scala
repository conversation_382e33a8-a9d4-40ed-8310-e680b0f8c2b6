// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.advisortag

import anduin.fdb.record.model.{RecordReadTask, RecordTask}
import anduin.fdb.record.store.FDBStoreProvider
import anduin.id.fundsub.{FundSubAdvisorTagId, FundSubId}
import anduin.protobuf.fundsub.FundSubAdvisorTag
import com.apple.foundationdb.record.query.RecordQuery
import com.apple.foundationdb.record.query.expressions.Query

final case class FundSubAdvisorTagOperations(store: FundSubAdvisorTagStoreProvider.Store) {

  def get(id: FundSubAdvisorTagId): RecordTask[FundSubAdvisorTag] = {
    store.get(id)
  }

  def getOpt(id: FundSubAdvisorTagId): RecordTask[Option[FundSubAdvisorTag]] = {
    store.getOpt(id)
  }

  def getTagsByFundSubId(fundSubId: FundSubId): RecordReadTask[List[FundSubAdvisorTag]] = {
    val query = RecordQuery
      .newBuilder()
      .setRecordType(FundSubAdvisorTagStoreProvider.RecordTypeName)
      .setFilter(
        Query.and(
          Query.field("fundSubId").equalsValue(fundSubId.idString),
          Query.field("isRemoved").equalsValue(Boolean.box(false))
        )
      )
      .build()

    store.queryL(query)
  }

  def add(tag: FundSubAdvisorTag): RecordTask[FundSubAdvisorTag] = {
    for {
      _ <- store.create(tag)
    } yield tag
  }

  def update(
    id: FundSubAdvisorTagId,
    fn: FundSubAdvisorTag => FundSubAdvisorTag
  ): RecordTask[FundSubAdvisorTag] = {
    for {
      tag <- store.get(id)
      newTag <- store.update(fn(tag))
    } yield newTag
  }

  def deletePermanently(id: FundSubAdvisorTagId): RecordTask[Boolean] = {
    store.delete(id)
  }

  def markAsRemoved(id: FundSubAdvisorTagId): RecordTask[FundSubAdvisorTag] = {
    update(id, _.withIsRemoved(true))
  }

}

object FundSubAdvisorTagOperations {
  val Production: FundSubAdvisorTagStoreProvider.Store => FundSubAdvisorTagOperations = FundSubAdvisorTagOperations(_)
}
