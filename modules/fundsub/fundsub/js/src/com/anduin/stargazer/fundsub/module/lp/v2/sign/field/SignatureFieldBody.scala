// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.lp.v2.sign.field

import com.raquo.laminar.api.L.*

import anduin.protobuf.signature.SignatureDataBox
import anduin.signature.model.ESignatureModel.SignField
import com.anduin.stargazer.fundsub.module.lp.v2.sign.{PageSize, Size}

private[field] case class SignatureFieldBody(
  ratioSizeSignal: Signal[Double],
  signatureImageUrl: Option[String],
  size: Size,
  pageSize: PageSize,
  signField: SignField,
  onCalculateRatioSize: Observer[Double]
) {

  def apply(): Node = {
    signField.data.data match {
      case _: SignatureDataBox.Data.Handwriting =>
        ViewHandwritingSignature(
          dataBox = signField.data,
          pageSize = pageSize,
          ratioSizeSignal = ratioSizeSignal,
          size = size,
          onCalculateRatioSize = onCalculateRatioSize
        )()

      case _: SignatureDataBox.Data.Typing =>
        ViewTextSignature(
          dataBox = signField.data,
          pageSize = pageSize,
          size = size,
          onCalculateRatioSize = onCalculateRatioSize
        )()

      case _: SignatureDataBox.Data.Image =>
        ViewImageSignature(
          dataBox = signField.data,
          pageSize = pageSize,
          signatureImageUrl = signatureImageUrl,
          size = size,
          onCalculateRatioSize = onCalculateRatioSize
        )()

      case _ =>
        div()
    }
  }

}
