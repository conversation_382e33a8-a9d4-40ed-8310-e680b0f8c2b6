package com.anduin.stargazer.fundsub.module.comment.laminar.share

import com.raquo.airstream.core.{Observer, Signal}
import com.raquo.laminar.api.L.*
import design.anduin.components.badge.Badge
import design.anduin.components.badge.laminar.BadgeL
import design.anduin.components.segment.laminar.SegmentL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*

private[laminar] case class OpenResolvedCommentSwitcherL(
  isResolvedTabSignal: Signal[Boolean],
  onSwitchToResolvedTab: Observer[Boolean],
  unreadOpenCommentsSignal: Signal[Int],
  unreadResolvedCommentsSignal: Signal[Int]
) {

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL(OpenResolvedCommentSwitcherL),
      child <-- isResolvedTabSignal.splitOne(identity) { case (_, isResolvedTab, _) =>
        SegmentL(
          initialActiveItem = if (isResolvedTab) 1 else 0,
          items = List(
            SegmentL.Item(
              renderContent = () =>
                renderSegmentHeader(
                  unreadCountSignal = unreadOpenCommentsSignal,
                  "Open"
                )
            ),
            SegmentL.Item(
              renderContent = () =>
                renderSegmentHeader(
                  unreadCountSignal = unreadResolvedCommentsSignal,
                  "Resolved"
                )
            )
          ),
          onClick = Observer[Int] { index =>
            onSwitchToResolvedTab.onNext(index == 1)
          }
        )()
      }
    )
  }

  private def renderSegmentHeader(unreadCountSignal: Signal[Int], label: String) = {
    val shouldRenderUnreadThreadBadgeSignal = unreadCountSignal.map(_ > 0)
    div(
      ComponentUtils.testIdL(label),
      tw.flex.itemsCenter,
      label,
      child <-- shouldRenderUnreadThreadBadgeSignal.splitBoolean(
        whenTrue = _ =>
          div(
            tw.pl8,
            BadgeL(
              color = Badge.Color.Primary,
              theme = Badge.Theme.Bold,
              count = unreadCountSignal.map(Some(_))
            )()
          ),
        whenFalse = _ => emptyNode
      )
    )
  }

}
