// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.lp.v2.comment

import com.raquo.laminar.api.L.*
import design.anduin.components.badge.Badge
import design.anduin.components.badge.laminar.BadgeL
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.style.tw.*

private[v2] final case class CommentEntryButton(
  newsSignal: Signal[Int],
  label: String,
  onClick: Observer[Unit],
  testId: String = "CommentEntryButton"
) {

  def apply(): Node = {
    ButtonL(
      style = ButtonL.Style.Minimal(isFullWidth = true),
      onClick = onClick.contramap(_ => ())
    )(
      div(
        tw.wPc100,
        child <-- newsSignal.map { news =>
          div(
            tw.flex.itemsCenter.wPc100,
            span(
              tw.mr8,
              if (news > 0) {
                BadgeL(
                  color = Badge.Color.Danger,
                  theme = Badge.Theme.Bold,
                  count = Val(Option(news))
                )()
              } else {
                IconL(Val(Icon.Glyph.Comment))()
              }
            ),
            span(tw.fontNormal.textSmall, label)
          )
        }
      )
    )
  }

}
