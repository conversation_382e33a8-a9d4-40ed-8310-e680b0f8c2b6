// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.dashboard.lp.table.managedocument.review

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.toast.Toast
import org.scalajs.dom.MouseEvent
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.review.{PerLpReview, ReviewSupportingDocItem, ReviewSupportingDocItemParams}
import anduin.id.fundsub.FundSubLpId
import anduin.model.common.user.UserId
import anduin.review.ReviewAction
import anduin.utils.StringUtils
import com.anduin.stargazer.fundsub.client.FundSubSupportingDocReviewEndpointClient
import com.anduin.stargazer.fundsub.module.investor.dashboard.lp.table.managedocument.review.ManageDocumentReviewModal.SupportingDocInfo

final case class BatchApproveSupportingDocumentButton(
  supportDocsInfo: Seq[SupportingDocInfo],
  currentUserId: UserId,
  lpId: FundSubLpId
) {

  private val isApprovingVar: Var[Boolean] = Var(false)

  def apply(): Node = {
    ModalL(
      renderTitle = _ => "Approve documents",
      size = ModalL.Size(width = ModalL.Width.Px720),
      renderTarget = openModal => {
        ButtonL(
          style = ButtonL.Style.Full(
            icon = Some(Icon.Glyph.CheckDouble),
            color = ButtonL.Color.Primary
          ),
          onClick = openModal.contramap(_ => ()),
          testId = "BatchApproveBtn"
        )("Approve documents")
      },
      renderContent = closeModal =>
        ManageDocumentReviewModal(
          supportDocsInfo = supportDocsInfo,
          currentUserId = currentUserId,
          lpId = lpId,
          onClose = closeModal,
          renderPrimaryButton = selectedDocTypes =>
            ButtonL(
              style = ButtonL.Style.Full(color = ButtonL.Color.Primary, isBusy = isApprovingVar.signal),
              isDisabled = Val(selectedDocTypes.isEmpty),
              onClick = Observer[MouseEvent] { _ =>
                approve(selectedDocTypes, closeModal)
              }
            ) {
              val additionalText = if (selectedDocTypes.nonEmpty) {
                s" ${StringUtils.pluralItem(selectedDocTypes.size, "document")}"
              } else {
                ""
              }
              s"Approve$additionalText"

            }
        )(),
      testId = "BatchApproveModal"
    )()
  }

  private def approve(docTypes: Seq[SupportingDocInfo], onClose: Observer[Unit]): Unit = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        _ <- ZIO.attempt(isApprovingVar.set(true))
        _ <- FundSubSupportingDocReviewEndpointClient
          .reviewDocItem(
            ReviewSupportingDocItemParams(
              perLpReviews = Seq(
                PerLpReview(
                  lpId = lpId,
                  reviews = docTypes
                    .map(doc =>
                      ReviewSupportingDocItem(
                        docType = doc.docType,
                        action = ReviewAction.Approve
                      )
                    )
                )
              )
            )
          )
          .map(
            _.fold(
              _ => {
                isApprovingVar.set(false)
                Toast.error(s"Failed to approve document")
              },
              _ => {
                isApprovingVar.set(false)
                Toast.success("Document approved")
                onClose.onNext(())
              }
            )
          )
      } yield ()

    }
    ()
  }

}
