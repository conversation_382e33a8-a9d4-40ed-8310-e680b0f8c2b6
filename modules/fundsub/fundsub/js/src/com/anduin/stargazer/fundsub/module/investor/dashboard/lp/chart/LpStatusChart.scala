// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.dashboard.lp.chart

import anduin.facades.chart.echarts.anon.{BarBorderRadius, DictunknownProperty}
import anduin.facades.chart.echarts.echarts.EChartOption.BasicComponents.CartesianAxis.{Label, SplitLine}
import anduin.facades.chart.echarts.echarts.EChartOption.BasicComponents.Line
import anduin.facades.chart.echarts.echarts.EChartOption.SeriesBar.DataObject
import anduin.facades.chart.echarts.echarts.EChartOption.{BaseTooltip, LineStyle, Series, Tooltip}
import anduin.facades.chart.echarts.{echartsInts, echartsStrings}
import anduin.fundsub.status.LpStatusSharedUtils
import anduin.protobuf.external.squants.CurrencyMessage
import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus
import anduin.utils.CurrencyJsUtils
import com.anduin.stargazer.fundsub.module.status.LpStatusUtils.LpStatusGroup
import design.anduin.chart.Chart
import design.anduin.style.CssVar

import scala.scalajs.js
import scala.util.control.Exception.catching

import anduin.facades.chart.echarts.echarts.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

// scalafix:off DisableSyntax.asInstanceOf
final case class LpStatusChart(
  statusList: List[LpStatus],
  lpWeightByStatus: Map[LpStatus, Double],
  lpCommitmentAmountByStatus: Map[LpStatus, Double],
  lpCountByStatus: Map[LpStatus, Int],
  targetCommitmentAmount: Double,
  currency: CurrencyMessage,
  onBarClicked: LpStatus => Callback,
  yAxisLabelFormatter: js.Dynamic => String,
  columnLabelFormatter: js.Dynamic => String
) {
  def apply(): VdomElement = LpStatusChart.component(this)
}

object LpStatusChart {
  private type Props = LpStatusChart

  private val LineColor = CssVar.Color.Gray5
  private val TextColor = CssVar.Color.Gray8
  private val MaxColumnLabelNameChar = 11

  private def getBarColors(statusList: List[LpStatus]) = {
    statusList.map(status => status -> LpStatusGroup.getGroupByLpStatus(status).map(_.getColor).getOrElse("#BACDDB"))
  }

  private def breakdownLabel(label: String) = {
    val breakedBySpace = label.split(' ').foldLeft(Array[String]()) { case (sum, token) =>
      if (sum.length > 0 && token.length + sum.last.length < MaxColumnLabelNameChar) {
        val newSum = sum.dropRight(1)
        newSum :+ s"${sum.last} $token"
      } else {
        sum :+ token
      }
    }
    val breakedByLength = breakedBySpace.foldLeft(Array[String]()) { case (sum, token) =>
      if (token.length > MaxColumnLabelNameChar) {
        sum ++ Seq(token.substring(0, token.length / 2) ++ "-", token.substring(token.length / 2))
      } else {
        sum :+ token
      }
    }
    breakedByLength.mkString("\n")
  }

  private def tooltipFormatter(props: Props): Tooltip.Formatter = { (p, _, _) =>
    catching(classOf[Tooltip.Format])
      .opt {
        p.asInstanceOf[Tooltip.Format]
      }
      .fold("") { params =>
        val category = params.name.getOrElse("")

        val statusOpt = LpStatus.values.find { status =>
          LpStatusSharedUtils.getStatusName(status).equalsIgnoreCase(category)
        }

        val commitmentAmount = statusOpt.flatMap(props.lpCommitmentAmountByStatus.get).getOrElse(0.0)

        val commitmentAmountString = CurrencyJsUtils.convertToMoneyString(
          value = commitmentAmount,
          currency = props.currency,
          CurrencyJsUtils.MoneyFormat0
        )
        val count = statusOpt.flatMap(props.lpCountByStatus.get).getOrElse(0)
        val percentageComparedToTarget = if (props.targetCommitmentAmount > 0) {
          commitmentAmount / props.targetCommitmentAmount * 100
        } else {
          100.0
        }

        val columnName = if (statusOpt.contains(LpStatus.LPInProgress)) "Form in progress" else category

        def semiBoldSpan(s: String): String = {
          s"""<span style="font-weight: 600">$s</span>"""
        }

        s"""
           |<div>
           |  <div style="color: #20394D; line-height: 20px; font-size: 13px">
           |    <div>${semiBoldSpan(columnName + ":")} $count</div>
           |    <div>${semiBoldSpan(columnName + " amount:")} $commitmentAmountString</div>
           |    <div>${semiBoldSpan(s"${percentageComparedToTarget.round}%")} of target</div>
           |  </div>
           |</div>
           |""".stripMargin
      }

  }

  private def xAxisLabelFormatter: js.Function1[js.Dynamic, String] = { axisValue =>
    breakdownLabel(axisValue.toString)
  }

  private def toJsFunction1[T, R](f: T => R): js.Function1[T, R] = f

  private def makeChartParams(props: Props): EChartsResponsiveOption = {

    val statuses = props.statusList.map(status => LpStatusSharedUtils.getStatusName(status))

    val countAndColors = getBarColors(props.statusList)
      .map { case (status, color) =>
        props.lpWeightByStatus.getOrElse(status, 0.0) -> color
      }
      .map { case (count, color) =>
        DataObject()
          .setItemStyle(
            BarBorderRadius().setColor(
              color
            )
          )
          .setValue(count)
      }

    val option: EChartOption[Series] = EChartOption[Series]()
      .setTooltip(
        EChartOption
          .Tooltip()
          .setTrigger(echartsStrings.item)
          .setBackgroundColor("rgba(255, 255, 255, 0.88)")
          .setPadding(16.0)
          .setBorderColor("#809AAD")
          .setBorderWidth(1)
      )
      .setXAxis(
        EChartOption
          .XAxis()
          .setType(echartsStrings.category)
          .setAxisLine(Line().setLineStyle(LineStyle().setColor(LineColor)))
          .setAxisLabel(
            Label()
              .setFontSize(11)
              .setFontWeight(echartsInts.`400`)
              .setColor(TextColor)
              .setLineHeight(16)
              .setFormatter(xAxisLabelFormatter)
              .setInterval(0)
          )
          .setData(js.Array(statuses*))
      )
      .setYAxis(
        EChartOption
          .YAxis()
          .setType(echartsStrings.value)
          .setSplitNumber(4)
          .setAxisLine(Line().setLineStyle(LineStyle().setColor(LineColor)))
          .setSplitLine(SplitLine().setLineStyle(LineStyle().setColor("#BACDDB").setType(echartsStrings.dashed)))
          .setAxisLabel(
            Label()
              .setFontSize(13)
              .setFontWeight(echartsInts.`400`)
              .setColor(TextColor)
              .setLineHeight(20)
              .setFormatter(toJsFunction1(props.yAxisLabelFormatter))
          )
          .setOffset(10)
      )
      .setSeries(
        js.Array[Series](
          EChartOption
            .SeriesBar()
            .setType(echartsStrings.bar)
            .setData(js.Array(countAndColors*))
            .setTooltip(BaseTooltip().setFormatter(tooltipFormatter(props)))
            .setBarWidth(42)
            .setLabel(
              DictunknownProperty()
                .setShow(true)
                .setColor("#20394D")
                .setPosition("top")
                .setFontSize(11)
                .setFontWeight(600)
                .setFormatter(toJsFunction1(props.columnLabelFormatter))
            )
        )
      )
      .setGrid(
        EChartOption
          .Grid()
          .setTop(34)
          .setLeft(58)
          .setRight(0)
          .setBottom(43)
      )

    EChartsResponsiveOption().setBaseOption(option)
  }

  private def render(props: Props) = {
    Chart(
      width = 698,
      height = 405,
      params = makeChartParams(props),
      onClickOpt = Option((dataIndex: Int) => {
        props.statusList.lift(dataIndex).fold(Callback.empty) { status =>
          props.onBarClicked(status)
        }
      })
    )()
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
