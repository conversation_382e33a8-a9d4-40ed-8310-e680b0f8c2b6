// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dashboard.model

import java.time.format.DateTimeFormatter
import java.time.{LocalDate, ZoneId}
import scala.annotation.unused
import scala.reflect.TypeTest
import scala.util.Try

import anduin.circe.generic.semiauto.CirceCodec
import anduin.dashboard.data.*
import anduin.dashboard.data.DataExtractionRequestDashboardSchema.DataExtractionDashboardStatus
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.id.fundsub.ria.FundSubRiaGroupId
import anduin.id.fundsub.{FundSubCloseId, FundSubLpTagId}
import anduin.model.codec.ProtoCodecs.given
import anduin.protobuf.amlcheck.AmlCheckStatus
import anduin.protobuf.dashboard.*
import anduin.protobuf.dashboard.CellFilterModel.CellType
import anduin.protobuf.flow.fundsub.admin.lpdashboard.{LpDocRequestStatus, LpStatus}
import anduin.protobuf.fundsub.models.customdata.item.*
import anduin.protobuf.fundsub.models.formfield.*
import anduin.protobuf.fundsub.models.formfield.FormFieldFilterValue.Filter
import anduin.protobuf.fundsub.{LpDataExtractRequestStatusModel, LpOrderType}
import com.anduin.stargazer.util.date.DateCalculator

class CellFilterType[Cell <: ColumnCell](
  using tt: TypeTest[ColumnCell, Cell]
) {

  def upcastCell(columnCell: ColumnCell): Option[Cell] = {
    tt.unapply(columnCell)
  }

}

sealed trait CellFilter derives CanEqual, CirceCodec.WithDefaultsAndTypeName {
  type Cell <: ColumnCell

  def columnId: String

  def types: CellFilterType[Cell]

  def validateCell(cell: Cell, dashboardMetadata: DashboardMetadata): Boolean

  def validateColumnCell(columnCell: ColumnCell, dashboardMetadata: DashboardMetadata): Boolean = {
    types.upcastCell(columnCell).exists(validateCell(_, dashboardMetadata))
  }

  def filterCntDetail: Int = 0

  def filterCnt: Int = {
    if (filterCntDetail > 0) 1 else 0
  }

}

object CellFilter {
  private val dummyEmptyCellFilter: CustomDataFilter = CustomDataFilter("", CustomDataFilterValue())

  def fromStoreModel(model: CellFilterModel): CellFilter = {
    model.cellType match {
      case CellType.Empty => CellFilter.dummyEmptyCellFilter
      case CellType.ContactFilter(value) =>
        ContactFilter(
          columnId = model.columnId,
          selectedOrderTypes = value.selectedOrderTypes.toList,
          filterInactiveOrders = value.filterInactiveOrders
        )
      case CellType.DocumentRequestFilter(value) =>
        DocumentRequestFilter(
          columnId = model.columnId,
          selectedDocRequestStatus = value.selectedDocRequestStatus.toList
        )
      case CellType.CloseFilter(value) =>
        CloseFilter(
          columnId = model.columnId,
          selectedCloseIds = value.selectedCloseIds
        )
      case CellType.SubscriptionDocStatusFilter(value) =>
        SubscriptionDocStatusFilter(
          columnId = model.columnId,
          selectedStatuses = value.selectedStatuses,
          formProgressRangeOpt = if (value.formProgressMin == 0 && value.formProgressMax == 100) {
            None
          } else {
            Option((value.formProgressMin, value.formProgressMax))
          }
        )
      case CellType.TagFilter(value) =>
        TagFilter(
          columnId = model.columnId,
          selectedTags = value.selectedTags
        )
      case CellType.CustomDataFilter(value) =>
        value.customDataFilterValue.fold(CellFilter.dummyEmptyCellFilter) { customDataFilterValue =>
          CustomDataFilter(columnId = model.columnId, filterValue = customDataFilterValue)
        }
      case CellType.FormFieldFilter(value) =>
        value.formFieldFilterValue.fold(CellFilter.dummyEmptyCellFilter) { formFieldFilterValue =>
          FormFieldFilter(columnId = model.columnId, filterValue = formFieldFilterValue)
        }
      case CellType.DataExtractRequestStatusFilter(value) =>
        DataExtractionFilter(
          columnId = model.columnId,
          selectedStatus = value.dataExtractRequestStatus.toList
        )
      case CellType.AmlCheckFilter(value) =>
        AmlCheckFilter(
          columnId = model.columnId,
          selectedStatuses = value.amlCheckStatuses.toList
        )
      case CellType.AdvisorGroupFilter(value) =>
        AdvisorGroupFilter(
          columnId = model.columnId,
          selectedAdvisorGroupOptions = value.selectedAdvisorGroupOptions.map {
            case Some(id) => AdvisorGroupFilter.FilterOption.AdvisorGroup(id)
            case None     => AdvisorGroupFilter.FilterOption.NoGroup
          }
        )

      case CellType.InvestorGroupFilter(value) =>
        InvestorGroupFilter(
          columnId = model.columnId,
          selectedInvestorGroupOptions = value.selectedInvestorGroupOptions.map {
            case Some(id) => InvestorGroupFilter.FilterOption.InvestorGroup(id)
            case None     => InvestorGroupFilter.FilterOption.NoGroup
          }
        )
    }
  }

  def toStoreModel(cellFilter: CellFilter): CellFilterModel = {
    cellFilter match {
      case contactFilter: ContactFilter =>
        CellFilterModel(
          columnId = contactFilter.columnId,
          cellType = CellFilterModel.CellType.ContactFilter(
            ContactFilterModel(
              selectedOrderTypes = contactFilter.selectedOrderTypes,
              filterInactiveOrders = contactFilter.filterInactiveOrders
            )
          )
        )
      case documentRequestFilter: DocumentRequestFilter =>
        CellFilterModel(
          columnId = documentRequestFilter.columnId,
          cellType = CellFilterModel.CellType.DocumentRequestFilter(
            DocumentRequestFilterModel(
              selectedDocRequestStatus = documentRequestFilter.selectedDocRequestStatus
            )
          )
        )
      case closeFilter: CloseFilter =>
        CellFilterModel(
          columnId = closeFilter.columnId,
          cellType = CellFilterModel.CellType.CloseFilter(
            CloseFilterModel(
              selectedCloseIds = closeFilter.selectedCloseIds
            )
          )
        )
      case subscriptionDocStatusFilter: SubscriptionDocStatusFilter =>
        CellFilterModel(
          columnId = subscriptionDocStatusFilter.columnId,
          cellType = CellFilterModel.CellType.SubscriptionDocStatusFilter(
            SubscriptionDocStatusFilterModel(
              selectedStatuses = subscriptionDocStatusFilter.selectedStatuses,
              formProgressMin = subscriptionDocStatusFilter.formProgressRangeOpt.map(_._1).getOrElse(0),
              formProgressMax = subscriptionDocStatusFilter.formProgressRangeOpt.map(_._2).getOrElse(100)
            )
          )
        )
      case tagFilter: TagFilter =>
        CellFilterModel(
          columnId = tagFilter.columnId,
          cellType = CellFilterModel.CellType.TagFilter(
            TagFilterModel(
              selectedTags = tagFilter.selectedTags
            )
          )
        )
      case customDataFilter: CustomDataFilter =>
        CellFilterModel(
          columnId = customDataFilter.columnId,
          cellType = CellFilterModel.CellType.CustomDataFilter(
            CustomDataFilterModel(
              customDataFilterValue = Option(customDataFilter.filterValue)
            )
          )
        )
      case formFieldFilter: FormFieldFilter =>
        CellFilterModel(
          columnId = formFieldFilter.columnId,
          cellType = CellFilterModel.CellType.FormFieldFilter(
            FormFieldFilterModel(
              formFieldFilterValue = Option(formFieldFilter.filterValue)
            )
          )
        )
      case dataExtractionStatusFilter: DataExtractionFilter =>
        CellFilterModel(
          columnId = dataExtractionStatusFilter.columnId,
          cellType = CellFilterModel.CellType.DataExtractRequestStatusFilter(
            DataExtractRequestStatusFilterModel(
              columnId = dataExtractionStatusFilter.columnId,
              dataExtractRequestStatus = dataExtractionStatusFilter.selectedStatus
            )
          )
        )
      case amlCheckFilter: AmlCheckFilter =>
        CellFilterModel(
          columnId = amlCheckFilter.columnId,
          cellType = CellFilterModel.CellType.AmlCheckFilter(
            AmlCheckFilterModel(
              columnId = amlCheckFilter.columnId,
              amlCheckStatuses = amlCheckFilter.selectedStatuses.toSeq
            )
          )
        )

      case advisorGroupFilter: AdvisorGroupFilter =>
        CellFilterModel(
          columnId = advisorGroupFilter.columnId,
          cellType = CellFilterModel.CellType.AdvisorGroupFilter(
            AdvisorGroupFilterModel(
              selectedAdvisorGroupOptions = advisorGroupFilter.selectedAdvisorGroupOptions.map(_.advisorGroupIdOpt)
            )
          )
        )
      case investorGroupFilter: InvestorGroupFilter =>
        CellFilterModel(
          columnId = investorGroupFilter.columnId,
          cellType = CellFilterModel.CellType.InvestorGroupFilter(
            InvestorGroupFilterModel(
              selectedInvestorGroupOptions = investorGroupFilter.selectedInvestorGroupOptions.map(_.investorGroupIdOpt)
            )
          )
        )
    }
  }

}

case class ContactFilter(
  columnId: String,
  selectedOrderTypes: List[LpOrderType],
  filterInactiveOrders: Boolean
) extends CellFilter derives CirceCodec.WithDefaults {
  override type Cell = ContactCell

  override val types: CellFilterType[ContactCell] = new CellFilterType[ContactCell]

  override def validateCell(cell: ContactCell, dashboardMetadata: DashboardMetadata): Boolean = {
    val passedOrderTypeFilter = selectedOrderTypes.isEmpty || selectedOrderTypes.contains(cell.orderType)
    val inactiveNotificationEnabled = dashboardMetadata.inactiveNotificationSettingOpt.exists(_.enabled)
    val passedInactiveFilter = !inactiveNotificationEnabled || !filterInactiveOrders ||
      dashboardMetadata.inactiveNotificationSettingOpt.exists { inactiveSettings =>
        cell.lastActivityAt.exists { lastActivityAt =>
          DateCalculator.dayDiff(lastActivityAt, DateCalculator.instantNow) >= inactiveSettings.days
        }
      }
    passedOrderTypeFilter && passedInactiveFilter
  }

  override def filterCntDetail: Int = {
    val orderTypeCnt = selectedOrderTypes.size
    val inactiveOrderCnt = if (filterInactiveOrders) 1 else 0
    orderTypeCnt + inactiveOrderCnt
  }

  override def filterCnt: Int = {
    val orderTypeCnt = if (selectedOrderTypes.nonEmpty) 1 else 0
    val inactiveOrderCnt = if (filterInactiveOrders) 1 else 0
    orderTypeCnt + inactiveOrderCnt
  }

}

case class DocumentRequestFilter(
  columnId: String,
  selectedDocRequestStatus: List[LpDocRequestStatus] = List.empty
) extends CellFilter derives CirceCodec.WithDefaults {
  override type Cell = DocumentRequestCell

  override val types: CellFilterType[DocumentRequestCell] = new CellFilterType[DocumentRequestCell]

  override def validateCell(cell: DocumentRequestCell, dashboardMetadata: DashboardMetadata): Boolean = {
    val docRequestProgress =
      DocumentRequestCell
        .getDocRequestStatusMap(
          docRequests = cell.docs.map { doc =>
            DocumentRequestCell.SupportingDocumentRequestInfo(doc.name, doc.submitted, doc.markedAsNa)
          },
          docReviews = cell.reviews.map { review =>
            DocumentRequestCell.SupportingDocumentReviewInfo(review.docType, review.status)
          },
          providedDocs = cell.providedDocs,
          isSupportingDocReviewEnabled = dashboardMetadata.supportingDocReviewEnabled
        )
        .values
        .toSeq
        .map {
          case DocumentRequestCell.PendingSubmissionState => LpDocRequestStatus.PendingSubmission
          case DocumentRequestCell.ChangesInProgressState => LpDocRequestStatus.ChangesInProgress
          case DocumentRequestCell.PendingReviewState     => LpDocRequestStatus.PendingReview
          case DocumentRequestCell.CompletedState         => LpDocRequestStatus.Complete
        }
    val hasSelectedIncompleteStatus = docRequestProgress
      .intersect(selectedDocRequestStatus.filterNot(_ == LpDocRequestStatus.Complete))
      .nonEmpty

    val allSupDocComplete = docRequestProgress.nonEmpty && docRequestProgress.forall(_ == LpDocRequestStatus.Complete)
    val hasSelectedCompleteStatus = selectedDocRequestStatus.contains(LpDocRequestStatus.Complete) && allSupDocComplete

    selectedDocRequestStatus.isEmpty || hasSelectedIncompleteStatus || hasSelectedCompleteStatus
  }

  override def filterCntDetail: Int = selectedDocRequestStatus.size

}

case class CloseFilter(
  columnId: String,
  selectedCloseIds: Seq[FundSubCloseId] = Seq.empty
) extends CellFilter derives CirceCodec.WithDefaults {
  override type Cell = CloseCell

  override val types: CellFilterType[CloseCell] = new CellFilterType[CloseCell]

  @unused
  override def validateCell(cell: Cell, dashboardMetadata: DashboardMetadata): Boolean = {
    selectedCloseIds.isEmpty || cell.idOpt.exists(selectedCloseIds.contains(_))
  }

  override def filterCntDetail: Int = selectedCloseIds.size

}

case class SubscriptionDocStatusFilter(
  columnId: String,
  selectedStatuses: Seq[LpStatus] = Seq.empty,
  formProgressRangeOpt: Option[(Int, Int)] = None
) extends CellFilter derives CirceCodec.WithDefaults {
  override type Cell = SubscriptionDocumentCell
  override val types: CellFilterType[SubscriptionDocumentCell] = new CellFilterType[SubscriptionDocumentCell]

  @unused
  override def validateCell(cell: SubscriptionDocumentCell, dashboardMetadata: DashboardMetadata): Boolean = {
    val passedStatusFilter = selectedStatuses.isEmpty || selectedStatuses.contains(cell.status)
    val passedFormProgressFilter = formProgressRangeOpt.isEmpty || !selectedStatuses.contains(LpStatus.LPInProgress) ||
      formProgressRangeOpt.exists { case (pMin, pMax) =>
        val percentage = (cell.formProgress * 100).toInt // so that it is what we showed in front-end
        pMin <= percentage && percentage <= pMax
      }
    passedStatusFilter && passedFormProgressFilter
  }

  override def filterCntDetail: Int = selectedStatuses.size

}

case class TagFilter(
  columnId: String,
  selectedTags: Seq[FundSubLpTagId] = Seq.empty
) extends CellFilter derives CirceCodec.WithDefaults {
  override type Cell = TagCell
  override val types: CellFilterType[TagCell] = new CellFilterType[TagCell]

  @unused
  override def validateCell(cell: TagCell, dashboardMetadata: DashboardMetadata): Boolean = {
    selectedTags.isEmpty || cell.tags.exists { case (tagId, _) =>
      selectedTags.contains(tagId)
    }
  }

  override def filterCntDetail: Int = selectedTags.size

}

case class CustomDataFilter(
  columnId: String,
  filterValue: CustomDataFilterValue
) extends CellFilter derives CirceCodec.WithDefaults {
  override type Cell = CustomDataCell
  override val types: CellFilterType[CustomDataCell] = new CellFilterType[CustomDataCell]

  override def validateCell(cell: CustomDataCell, dashboardMetadata: DashboardMetadata): Boolean = {
    cell.data.flatMap(_.asNonEmpty) match {
      case Some(customData) =>
        customData match {
          case DateTimeValue(valueOpt, _) =>
            CustomDataFilter.processFilter[CustomDataFilterValue.Filter.DateTimeValueFilter](
              filterValue,
              _.value.asNonEmpty.map {
                case DateTimeValueFilterBetween(fromOpt, toOpt, _) =>
                  if (fromOpt.isEmpty && toOpt.isEmpty) {
                    true
                  } else if (fromOpt.isEmpty) {
                    valueOpt.fold(true)(_.toEpochMilli <= toOpt.get.toEpochMilli)
                  } else if (toOpt.isEmpty) {
                    valueOpt.fold(false)(_.toEpochMilli >= fromOpt.get.toEpochMilli)
                  } else {
                    valueOpt.fold(false)(value =>
                      value.toEpochMilli >= fromOpt.get.toEpochMilli && value.toEpochMilli <= toOpt.get.toEpochMilli
                    )
                  }
                case DateTimeValueFilterNotBetween(fromOpt, toOpt, _) =>
                  if (fromOpt.isEmpty && toOpt.isEmpty) {
                    false
                  } else if (fromOpt.isEmpty) {
                    valueOpt.fold(false)(_.toEpochMilli > toOpt.get.toEpochMilli)
                  } else if (toOpt.isEmpty) {
                    valueOpt.fold(true)(_.toEpochMilli < fromOpt.get.toEpochMilli)
                  } else {
                    valueOpt.fold(true)(value =>
                      value.toEpochMilli < fromOpt.get.toEpochMilli || value.toEpochMilli > toOpt.get.toEpochMilli
                    )
                  }
                case DateTimeValueFilterEmpty(_) => valueOpt.isEmpty
              }
            )
          case CurrencyValue(amount, _, _) =>
            CustomDataFilter.processFilter[CustomDataFilterValue.Filter.CurrencyValueFilter](
              filterValue,
              _.value.asNonEmpty.map {
                case CurrencyValueFilterBetween(fromOpt, toOpt, _) =>
                  if (fromOpt.isEmpty && toOpt.isEmpty) {
                    true
                  } else if (fromOpt.isEmpty) {
                    amount <= toOpt.get
                  } else if (toOpt.isEmpty) {
                    amount >= fromOpt.get
                  } else {
                    amount >= fromOpt.get && amount <= toOpt.get
                  }
                case CurrencyValueFilterEmpty(_) => amount == 0
              }
            )
          case SingleStringValue(valueWithColor, _) =>
            val values = valueWithColor.map(_.content)
            CustomDataFilter.processFilter[CustomDataFilterValue.Filter.SingleStringValueFilter](
              filterValue,
              _.value.asNonEmpty.map {
                case SingleStringValueFilterContains(filterValues, _) =>
                  filterValues.isEmpty || filterValues.exists(values.contains)
                case SingleStringValueFilterNotContains(filterValues, _) =>
                  filterValues.isEmpty || filterValues.forall(filterValue => !values.contains(filterValue))
                case SingleStringValueFilterEmpty(_) => values.isEmpty
              }
            )
          case MultipleStringValue(valueWithColor, _) =>
            val values = valueWithColor.map(_.content)
            CustomDataFilter.processFilter[CustomDataFilterValue.Filter.MultipleStringValueFilter](
              filterValue,
              _.value.asNonEmpty.map {
                case MultipleStringValueFilterContains(filterValues, _) =>
                  filterValues.isEmpty || filterValues.exists(values.contains)
                case MultipleStringValueFilterNotContains(filterValues, _) =>
                  filterValues.isEmpty || filterValues.forall(filterValue => !values.contains(filterValue))
                case MultipleStringValueFilterEmpty(_) => values.isEmpty
              }
            )
          case StringValue(value, _) =>
            CustomDataFilter.processFilter[CustomDataFilterValue.Filter.StringValueFilter](
              filterValue,
              _.value.asNonEmpty.map {
                case StringValueFilterContains(filterValue, _) =>
                  filterValue.isEmpty || value.toLowerCase.contains(filterValue.toLowerCase)
                case StringValueFilterNotContains(filterValue, _) =>
                  filterValue.isEmpty || !value.toLowerCase.contains(filterValue.toLowerCase)
                case StringValueFilterEmpty(_) => value.isEmpty
              }
            )
          case ChecklistValue(_, _) => false
          case MetadataValue(value, _) =>
            CustomDataFilter.processFilter[CustomDataFilterValue.Filter.MetadataValueFilter](
              filterValue,
              _.value.asNonEmpty.map {
                case MetadataValueFilterContains(filterValue, _) =>
                  filterValue.isEmpty || value.contains(filterValue)
                case MetadataValueFilterNotContains(filterValue, _) =>
                  filterValue.isEmpty || !value.contains(filterValue)
                case MetadataValueFilterEmpty(_) => value.isEmpty
              }
            )
        }
      case None => CustomDataFilter.processFilterForEmptyCell(filterValue)
    }
  }

  override def filterCntDetail: Int = 1

}

object CustomDataFilter {

  private def processFilter[A <: CustomDataFilterValue.Filter](
    filterValue: CustomDataFilterValue,
    validateFnc: A => Option[Boolean]
  )(
    using tt: TypeTest[CustomDataFilterValue.Filter, A]
  ) = {
    tt.unapply(filterValue.filter).flatMap(validateFnc).contains(true)
  }

  private def processFilterForEmptyCell(filterValue: CustomDataFilterValue): Boolean = {
    filterValue.filter match {
      case CustomDataFilterValue.Filter.Empty => true
      case CustomDataFilterValue.Filter.DateTimeValueFilter(value) =>
        value.asNonEmpty
          .map {
            case DateTimeValueFilterBetween(_, _, _)    => false
            case DateTimeValueFilterNotBetween(_, _, _) => true
            case DateTimeValueFilterEmpty(_)            => true
          }
          .fold(false)(identity)
      case CustomDataFilterValue.Filter.CurrencyValueFilter(value) =>
        value.asNonEmpty
          .map {
            case CurrencyValueFilterBetween(from, _, _) =>
              from.fold(true)(_ == 0)
            case CurrencyValueFilterEmpty(_) => true
          }
          .fold(false)(identity)
      case CustomDataFilterValue.Filter.SingleStringValueFilter(value) =>
        value.asNonEmpty
          .map {
            case SingleStringValueFilterContains(values, _)    => values.isEmpty
            case SingleStringValueFilterNotContains(values, _) => values.nonEmpty
            case SingleStringValueFilterEmpty(_)               => true
          }
          .fold(false)(identity)
      case CustomDataFilterValue.Filter.MultipleStringValueFilter(value) =>
        value.asNonEmpty
          .map {
            case MultipleStringValueFilterContains(values, _)    => values.isEmpty
            case MultipleStringValueFilterNotContains(values, _) => values.nonEmpty
            case MultipleStringValueFilterEmpty(_)               => true
          }
          .fold(false)(identity)
      case CustomDataFilterValue.Filter.StringValueFilter(value) =>
        value.asNonEmpty
          .map {
            case StringValueFilterContains(value, _)    => value.isEmpty
            case StringValueFilterNotContains(value, _) => value.nonEmpty
            case StringValueFilterEmpty(_)              => true
          }
          .fold(false)(identity)
      case CustomDataFilterValue.Filter.MetadataValueFilter(value) =>
        value.asNonEmpty
          .map {
            case MetadataValueFilterContains(values, _)    => values.isEmpty
            case MetadataValueFilterNotContains(values, _) => values.nonEmpty
            case MetadataValueFilterEmpty(_)               => true
          }
          .fold(false)(identity)
    }
  }

}

case class FormFieldFilter(
  columnId: String,
  filterValue: FormFieldFilterValue
) extends CellFilter derives CirceCodec.WithDefaults {
  override type Cell = FormFieldCell
  override val types: CellFilterType[FormFieldCell] = new CellFilterType[FormFieldCell]

  override def validateCell(cell: FormFieldCell, dashboardMetadata: DashboardMetadata): Boolean = {
    cell.value.fold[Boolean](
      jsonNull = FormFieldFilter.processFilterForEmptyCell(filterValue),
      jsonBoolean = value =>
        FormFieldFilter.processFilter[FormFieldFilterValue.Filter.BooleanFormValueFilter](
          filterValue,
          _.value.asNonEmpty.map {
            case BooleanFormValueFilterValue(filterValue, _) => filterValue == value
            case BooleanFormValueFilterEmpty(_)              => false
          }
        ),
      jsonNumber = value => {
        val amount = value.toDouble
        FormFieldFilter.processFilter[FormFieldFilterValue.Filter.NumberFormValueFilter](
          filterValue,
          _.value.asNonEmpty.map {
            case NumberFormValueFilterBetween(fromOpt, toOpt, _) =>
              if (fromOpt.isEmpty && toOpt.isEmpty) {
                true
              } else if (fromOpt.isEmpty) {
                amount <= toOpt.get
              } else if (toOpt.isEmpty) {
                amount >= fromOpt.get
              } else {
                amount >= fromOpt.get && amount <= toOpt.get
              }
            case NumberFormValueFilterNotBetween(fromOpt, toOpt, _) =>
              if (fromOpt.isEmpty && toOpt.isEmpty) {
                false
              } else if (fromOpt.isEmpty) {
                amount > toOpt.get
              } else if (toOpt.isEmpty) {
                amount < fromOpt.get
              } else {
                amount < fromOpt.get || amount > toOpt.get
              }
            case NumberFormValueFilterEmpty(_) => false
          }
        )
      },
      jsonString = value => {
        val dateTimeValueOpt = Try(LocalDate.parse(value, DateTimeFormatter.ISO_LOCAL_DATE)).toOption
          .map(_.atStartOfDay(ZoneId.systemDefault()).toInstant())
        dateTimeValueOpt.fold(FormFieldFilter.validateStringFormValue(filterValue, value)) { dateTimeValue =>
          FormFieldFilter.processFilter[FormFieldFilterValue.Filter.DateTimeFormValueFilter](
            filterValue,
            _.value.asNonEmpty.map {
              case DateTimeFormValueFilterBetween(fromOpt, toOpt, _) =>
                if (fromOpt.isEmpty && toOpt.isEmpty) {
                  true
                } else if (fromOpt.isEmpty) {
                  dateTimeValue.toEpochMilli <= toOpt.get.toEpochMilli
                } else if (toOpt.isEmpty) {
                  dateTimeValue.toEpochMilli >= fromOpt.get.toEpochMilli
                } else {
                  dateTimeValue.toEpochMilli >= fromOpt.get.toEpochMilli && dateTimeValue.toEpochMilli <= toOpt.get.toEpochMilli
                }
              case DateTimeFormValueFilterNotBetween(fromOpt, toOpt, _) =>
                if (fromOpt.isEmpty && toOpt.isEmpty) {
                  false
                } else if (fromOpt.isEmpty) {
                  dateTimeValue.toEpochMilli > toOpt.get.toEpochMilli
                } else if (toOpt.isEmpty) {
                  dateTimeValue.toEpochMilli < fromOpt.get.toEpochMilli
                } else {
                  dateTimeValue.toEpochMilli < fromOpt.get.toEpochMilli || dateTimeValue.toEpochMilli > toOpt.get.toEpochMilli
                }
              case DateTimeFormValueFilterEmpty(_) => false
            }
          )
        }

      },
      jsonArray = value =>
        FormFieldFilter.validateStringFormValue(
          filterValue,
          value.flatMap(_.asString).mkString("\n")
        ), // assume that array json is always an array of string
      jsonObject = _ => true
    )
  }

  override def filterCntDetail: Int = 1

}

object FormFieldFilter {

  private def processFilter[A <: FormFieldFilterValue.Filter](
    filterValue: FormFieldFilterValue,
    validateFnc: A => Option[Boolean]
  )(
    using tt: TypeTest[FormFieldFilterValue.Filter, A]
  ) = {
    tt.unapply(filterValue.filter).flatMap(validateFnc).contains(true)
  }

  private def validateStringFormValue(filterValue: FormFieldFilterValue, value: String) = {
    processFilter[FormFieldFilterValue.Filter.StringFormValueFilter](
      filterValue,
      _.value.asNonEmpty.map {
        case StringFormValueFilterContains(filterValues, _) =>
          filterValues.isEmpty || filterValues.exists(filterValue => value.toLowerCase.contains(filterValue.toLowerCase))
        case StringFormValueFilterNotContains(filterValues, _) =>
          filterValues.isEmpty || filterValues.forall(filterValue => !value.toLowerCase.contains(filterValue.toLowerCase))
        case StringFormValueFilterEmpty(_) => value.isEmpty
      }
    )
  }

  private def processFilterForEmptyCell(filterValue: FormFieldFilterValue): Boolean = {
    filterValue.filter match {
      case FormFieldFilterValue.Filter.Empty => false
      case FormFieldFilterValue.Filter.NumberFormValueFilter(value) =>
        value.asNonEmpty
          .map {
            case NumberFormValueFilterBetween(from, _, _) =>
              from.fold(true)(_ == 0)
            case NumberFormValueFilterNotBetween(from, _, _) =>
              from.fold(false)(_ > 0)
            case NumberFormValueFilterEmpty(_) => true
          }
          .fold(false)(identity)
      case FormFieldFilterValue.Filter.StringFormValueFilter(value) =>
        value.asNonEmpty
          .map {
            case StringFormValueFilterContains(value, _)    => value.isEmpty
            case StringFormValueFilterNotContains(value, _) => value.nonEmpty
            case _: StringFormValueFilterEmpty              => true
          }
          .fold(false)(identity)
      case FormFieldFilterValue.Filter.BooleanFormValueFilter(value) =>
        value.asNonEmpty
          .map {
            case BooleanFormValueFilterValue(value, _) => !value
            case _: BooleanFormValueFilterEmpty        => true
          }
          .fold(false)(identity)
      case FormFieldFilterValue.Filter.DateTimeFormValueFilter(value) =>
        value.asNonEmpty
          .map {
            case _: DateTimeFormValueFilterBetween    => false
            case _: DateTimeFormValueFilterNotBetween => true
            case _: DateTimeFormValueFilterEmpty      => true
          }
          .fold(false)(identity)
    }
  }

}

case class DataExtractionFilter(
  columnId: String,
  selectedStatus: List[LpDataExtractRequestStatusModel]
) extends CellFilter derives CirceCodec.WithDefaults {
  override type Cell = DataExtractionCell
  override val types: CellFilterType[DataExtractionCell] = new CellFilterType[DataExtractionCell]

  @unused
  override def validateCell(cell: DataExtractionCell, dashboardMetadata: DashboardMetadata): Boolean = {
    val requestStatusModelOpt = cell.dataExtractionRequestOpt.map(_.status match {
      case DataExtractionDashboardStatus.ReadyForReview => LpDataExtractRequestStatusModel.ReadyForReview
      case DataExtractionDashboardStatus.Approved       => LpDataExtractRequestStatusModel.Approved
      case DataExtractionDashboardStatus.InProgress     => LpDataExtractRequestStatusModel.InProgress
    })
    selectedStatus.isEmpty || requestStatusModelOpt.exists(selectedStatus.contains)
  }

  override def filterCntDetail: Int = selectedStatus.size

}

case class AmlCheckFilter(
  columnId: String,
  selectedStatuses: List[AmlCheckStatus]
) extends CellFilter derives CirceCodec.WithDefaults {
  override type Cell = AmlCheckCell
  override val types: CellFilterType[AmlCheckCell] = new CellFilterType[AmlCheckCell]

  @unused
  override def validateCell(cell: AmlCheckCell, dashboardMetadata: DashboardMetadata): Boolean = {
    selectedStatuses.isEmpty || cell.amlChecks.exists(check =>
      selectedStatuses.contains(check.status)
    ) || cell.amlChecks.isEmpty && selectedStatuses.contains(AmlCheckStatus.NOT_STARTED)
  }

  override def filterCntDetail: Int = selectedStatuses.size

}

case class AdvisorGroupFilter(
  columnId: String,
  selectedAdvisorGroupOptions: Seq[AdvisorGroupFilter.FilterOption] = Seq.empty
) extends CellFilter derives CirceCodec.WithDefaults {
  override type Cell = AdvisorGroupCell

  override val types: CellFilterType[AdvisorGroupCell] = new CellFilterType[AdvisorGroupCell]

  @unused
  override def validateCell(cell: Cell, dashboardMetadata: DashboardMetadata): Boolean = {
    val selectAll = selectedAdvisorGroupOptions.isEmpty
    val matchSelectedOption = selectedAdvisorGroupOptions
      .map(_.advisorGroupIdOpt)
      .contains(cell.advisorGroupOpt.map(_.id))
    selectAll || matchSelectedOption
  }

  override def filterCntDetail: Int = selectedAdvisorGroupOptions.size
}

object AdvisorGroupFilter {

  sealed trait FilterOption derives CanEqual, CirceCodec.WithDefaultsAndTypeName {
    def urlParam: String
    def advisorGroupIdOpt: Option[FundSubRiaGroupId]
  }

  object FilterOption {

    case object NoGroup extends FilterOption derives CirceCodec.WithDefaults {
      override def urlParam: String = "nogroup"
      override def advisorGroupIdOpt: Option[FundSubRiaGroupId] = None
    }

    case class AdvisorGroup(advisorGroupId: FundSubRiaGroupId) extends FilterOption derives CirceCodec.WithDefaults {
      override def urlParam: String = advisorGroupId.value.value
      override def advisorGroupIdOpt: Option[FundSubRiaGroupId] = Some(advisorGroupId)
    }

  }

}

case class InvestorGroupFilter(
  columnId: String,
  selectedInvestorGroupOptions: Seq[InvestorGroupFilter.FilterOption] = Seq.empty
) extends CellFilter derives CirceCodec.WithDefaults {
  override type Cell = InvestorGroupCell

  override val types: CellFilterType[InvestorGroupCell] = new CellFilterType[InvestorGroupCell]

  @unused
  override def validateCell(cell: Cell, dashboardMetadata: DashboardMetadata): Boolean = {
    val selectAll = selectedInvestorGroupOptions.isEmpty
    val matchSelectedOption = selectedInvestorGroupOptions
      .map(_.investorGroupIdOpt)
      .contains(cell.investorGroupOpt.map(_.id))
    selectAll || matchSelectedOption
  }

  override def filterCntDetail: Int = selectedInvestorGroupOptions.size
}

case class AdvisorTagFilter(
  columnId: String,
  selectedTags: Seq[FundSubAdvisorTagId] = Seq.empty
) extends CellFilter derives CirceCodec.WithDefaults {
  override type Cell = AdvisorTagCell
  override val types: CellFilterType[AdvisorTagCell] = new CellFilterType[AdvisorTagCell]

  @unused
  override def validateCell(cell: AdvisorTagCell, dashboardMetadata: DashboardMetadata): Boolean = {
    selectedTags.isEmpty || cell.tags.exists { case (tagId, _) =>
      selectedTags.contains(tagId)
    }
  }

  override def filterCntDetail: Int = selectedTags.size

}

object InvestorGroupFilter {

  sealed trait FilterOption derives CanEqual, CirceCodec.WithDefaultsAndTypeName {
    def urlParam: String
    def investorGroupIdOpt: Option[FundSubInvestorGroupId]
  }

  object FilterOption {

    case object NoGroup extends FilterOption derives CirceCodec.WithDefaults {
      override def urlParam: String = "nogroup"
      override def investorGroupIdOpt: Option[FundSubInvestorGroupId] = None
    }

    case class InvestorGroup(investorGroupId: FundSubInvestorGroupId) extends FilterOption
        derives CirceCodec.WithDefaults {
      override def urlParam: String = investorGroupId.value.value
      override def investorGroupIdOpt: Option[FundSubInvestorGroupId] = Some(investorGroupId)
    }

  }

}
