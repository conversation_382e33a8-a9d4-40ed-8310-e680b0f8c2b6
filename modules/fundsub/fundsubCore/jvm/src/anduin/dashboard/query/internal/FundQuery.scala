// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dashboard.query.internal

import caliban.client.SelectionBuilder

import anduin.dashboard.data.Documents.FileInfo
import anduin.dashboard.data.{AdvisorGroupBasicInfo, InvestorGroupBasicInfo, TemplateVersionInfo, User as UserData}
import anduin.dashboard.model.Fund
import anduin.dashboard.model.Fund.{CloseData, TagData}
import anduin.dashboard.query.{CommonQueries, ConstructQueryError}
import anduin.evendim.model.ScalarTypes.EvendimDateTime
import anduin.evendim.model.datalake.*
import anduin.id.ModelIdRegistry
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.id.fundsub.ria.FundSubRiaGroupId
import anduin.id.fundsub.{FundSubCloseId, FundSubId, FundSubLpTagId, InvestmentFundId}

case object FundQuery {

  private def subFundQuery = (SubFund.id ~ SubFund.customId.map(_.getOrElse("")) ~ SubFund.currency.map(
    _.getOrElse("")
  ) ~ SubFund.name.map(_.getOrElse("")))
    .mapN[
      String,
      String,
      String,
      String,
      Option[anduin.dashboard.data.SubFund]
    ] { case (id, customId, currency, name) =>
      val subFundIdOpt = ModelIdRegistry.parser
        .parseAs[InvestmentFundId](
          id
        )
      subFundIdOpt.map { id =>
        anduin.dashboard.data.SubFund(
          id = id,
          customId = customId,
          currency = currency,
          name = name
        )
      }
    }

  def query: Either[ConstructQueryError, SelectionBuilder[FundSubscription, Fund]] = {
    Right(
      (
        FundSubscription.id ~
          FundSubscription.customFundId ~
          FundSubscription.name ~
          FundSubscription.admins()(CommonQueries.selectUserInfo).map(_.flatten) ~
          FundSubscription.currency ~
          FundSubscription.closes()(queryClose).map(_.flatten) ~
          FundSubscription.tags()(queryTag).map(_.flatten) ~
          FundSubscription.supportingDocReviewEnabled ~
          FundSubscription.inactiveNotificationEnabled ~
          FundSubscription.inactiveNotificationDuration ~
          FundSubscription.referenceDocs()(CommonQueries.selectFileInfo).map(_.flatten) ~
          FundSubscription.templates()(CommonQueries.selectTemplate).map(_.flatten) ~
          FundSubscription
            .subFunds()(subFundQuery)
            .map(_.map(_.flatten).getOrElse(List.empty)) ~
          FundSubscription.advisorEntities()(queryAdvisorGroup).map(_.flatten) ~
          FundSubscription.investorGroups()(queryInvestorGroup).map(_.flatten)
      ).mapN[
        String,
        Option[String],
        Option[String],
        List[UserData],
        String,
        List[CloseData],
        List[TagData],
        Option[Boolean],
        Boolean,
        Int,
        List[FileInfo],
        List[TemplateVersionInfo],
        List[anduin.dashboard.data.SubFund],
        List[AdvisorGroupBasicInfo],
        List[InvestorGroupBasicInfo],
        Fund
      ] {
        case (
              idStr,
              customFundIdOpt,
              nameOpt,
              admins,
              currency,
              closes,
              tags,
              supportingDocReviewEnabledOpt,
              inactiveNotificationEnabled,
              inactiveNotificationDuration,
              referenceDocs,
              templates,
              subFunds,
              advisorGroups,
              investorGroups
            ) =>
          val id = ModelIdRegistry.parser.parseAs[FundSubId](idStr).getOrElse(FundSubId.defaultValue.get)
          Fund(
            id = id,
            customFundId = customFundIdOpt,
            name = nameOpt.getOrElse(""),
            admins = admins.sortBy(_.userId.idString),
            currency = currency,
            closes = closes.sortBy(_.id.idString),
            tags = tags.sortBy(_.id.idString),
            supportingDocReviewEnabled = supportingDocReviewEnabledOpt.getOrElse(false),
            inactiveNotificationEnabled = inactiveNotificationEnabled,
            inactiveNotificationDuration = inactiveNotificationDuration,
            referenceDocs = referenceDocs.sortBy(_.id.idString),
            templates = templates,
            subFunds = subFunds,
            advisorGroups = advisorGroups.sortBy(_.id.idString),
            investorGroups = investorGroups.sortBy(_.id.idString)
          )
      }
    )

  }

  private val queryClose = (Close.id ~ Close.name ~ Close.customCloseId ~ Close.targetDate)
    .mapN[
      String,
      String,
      Option[String],
      Option[EvendimDateTime],
      Option[CloseData]
    ] { case (idStr, name, customCloseIdOpt, targetDate) =>
      val closeIdOpt = ModelIdRegistry.parser
        .parseAs[FundSubCloseId](idStr)
      closeIdOpt.map { closeId =>
        CloseData(
          id = closeId,
          name = name,
          customCloseId = customCloseIdOpt.getOrElse(""),
          targetClosingDate = targetDate.map(_.dateTime)
        )
      }
    }

  private val queryTag = (Tag.id ~ Tag.name ~ Tag.creator()(CommonQueries.selectUserInfo) ~ Tag.createdAt)
    .mapN[
      String,
      String,
      Option[Option[UserData]],
      Option[EvendimDateTime],
      Option[TagData]
    ] { case (idStr, name, creator, createdAt) =>
      val tagIdOpt = ModelIdRegistry.parser.parseAs[FundSubLpTagId](idStr)
      tagIdOpt.map { tagId =>
        TagData(
          id = tagId,
          name = name,
          creator = creator.flatten,
          createdAt = createdAt.map(_.dateTime)
        )
      }
    }

  private val queryAdvisorGroup = (AdvisorEntity.id ~ AdvisorEntity.name)
    .mapN[
      String,
      String,
      Option[AdvisorGroupBasicInfo]
    ] { case (idStr, name) =>
      val advisorGroupIdOpt = ModelIdRegistry.parser.parseAs[FundSubRiaGroupId](idStr)
      advisorGroupIdOpt.map { advisorGroupId =>
        AdvisorGroupBasicInfo(
          id = advisorGroupId,
          name = name
        )
      }
    }

  private val queryInvestorGroup = (InvestorGroup.id ~ InvestorGroup.name)
    .mapN[
      String,
      String,
      Option[InvestorGroupBasicInfo]
    ] { case (idStr, name) =>
      val investorGroupIdOpt = ModelIdRegistry.parser.parseAs[FundSubInvestorGroupId](idStr)
      investorGroupIdOpt.map { investorGroupId =>
        InvestorGroupBasicInfo(
          id = investorGroupId,
          name = name
        )
      }
    }

}
