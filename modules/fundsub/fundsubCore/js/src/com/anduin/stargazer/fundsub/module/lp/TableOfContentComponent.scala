// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.lp

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.tree.Tree
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import anduin.id.issuetracker.IssueId
import anduin.utils.StringUtils
import com.anduin.stargazer.dynamicform.constants.DynamicFormConstants
import com.anduin.stargazer.dynamicform.core.{FormProcessor, FormProperty, FormState}
import com.anduin.stargazer.dynamicform.misc.FormSectionAttribute

import anduin.validator.CommonValidator.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class TableOfContentComponent(
  currentPage: Option[String],
  formProcessor: FormProcessor,
  onNavigate: String => Callback,
  viewedSections: Seq[String],
  showWarning: Boolean,
  isCommentEnabled: Boolean = false,
  targetSectionIdsWithCommentInfo: Map[String, List[(String, (String, String, IssueId))]],
  commentNotifs: Set[IssueId],
  onClose: Callback
) {
  def apply(): VdomElement = TableOfContentComponent.component(this)
}

object TableOfContentComponent {

  private val ComponentName = this.getClass.getSimpleName

  private val TypedTree = (new Tree[FormState.FormSectionState])()

  private val CommentIconPx = "16px"

  private val MaxCountNewNotifications = 9

  // To be marked completed, this field must not be hidden and:
  // - The validation of this field, and all children field which are not hidden, must be success
  // - This page must be viewed, else if not a page, then all children must be marked completed.
  private def isNodeCompleted(props: TableOfContentComponent, node: FormState.FormSectionState): Boolean = {
    if (node.isHidden) {
      true
    } else {
      val isViewed = props.viewedSections.contains(node.id)
      val isPage = isNodePage(props)(node)
      val isChildrenCompleted = () => node.children.forall(isNodeCompleted(props, _))
      isSuccess(node.validation) && (isViewed || !isPage && isChildrenCompleted())
    }
  }

  private def renderIcon(completed: Boolean, showWarning: Boolean): VdomElement = {
    if (completed) {
      IconR(name = Icon.Glyph.Check)()
    } else if (showWarning) {
      TooltipR(
        renderTarget = IconR(name = Icon.Glyph.Error)(),
        renderContent = _("There are missing fields in this page.")
      )()
    } else {
      IconR(name = Icon.Glyph.CircleLine)()
    }
  }

  // Return: (numOpenComments, numHiddenComments, numCommentNotifs)
  private def getCommentCountForPageSection(
    props: TableOfContentComponent,
    sectionId: String
  ): (Int, Int, Int) = {
    val sectionCommentListOpt = props.targetSectionIdsWithCommentInfo.get(sectionId)
    sectionCommentListOpt.fold[(Int, Int, Int)]((0, 0, 0)) { commentList =>
      val hiddenFieldsCheckList = commentList.map { case (_, (fieldId, _, _)) =>
        props.formProcessor.isSectionHidden(fieldId)
      }
      val numCommentNotifs = commentList.count(data => props.commentNotifs.contains(data._2._3))
      (hiddenFieldsCheckList.count(b => !b), hiddenFieldsCheckList.count(b => b), numCommentNotifs)
    }
  }

  // Return: (numOpenComments, numHiddenComments, numCommentNotifs)
  private def getCommentCountNonPageForSection(
    props: TableOfContentComponent,
    sectionId: String
  ): (Int, Int, Int) = {
    val childrenPages = props.targetSectionIdsWithCommentInfo.toList
      .filter { case (pageId, _) =>
        FormProperty.isParentOf(sectionId, pageId)
      }
    val commentCountForPages = childrenPages.map { case (pageId, _) =>
      getCommentCountForPageSection(props, pageId)
    }
    (commentCountForPages.map(_._1).sum, commentCountForPages.map(_._2).sum, commentCountForPages.map(_._3).sum)
  }

  // Return: (numOpenComments, numHiddenComments, numCommentNotifs)
  private def getCommentCounts(
    props: TableOfContentComponent,
    sectionId: String,
    isPage: Boolean
  ): (Int, Int, Int) = {
    if (isPage) {
      getCommentCountForPageSection(props, sectionId)
    } else {
      getCommentCountNonPageForSection(props, sectionId)
    }
  }

  private def renderCommentIcon(props: TableOfContentComponent, sectionId: String, isPage: Boolean) = {
    TagMod.when(props.isCommentEnabled) { // Don't combine these 2 conditions to take advantage of lazy vals.
      val commentCounts = getCommentCounts(
        props,
        sectionId,
        isPage
      )
      val (totalComments, numCommentNofis) = (commentCounts._1 + commentCounts._2, commentCounts._3)
      TagMod.when(totalComments > 0) {
        TooltipR(
          renderTarget = <.div(
            tw.mx8.hidden,
            tw.lg(tw.block),
            <.div(
              tw.relative,
              <.span(tw.textGray7, IconR(Icon.Glyph.Comment)()),
              TagMod.when(numCommentNofis > 0) {
                val isExceededMaxNotifs = numCommentNofis > MaxCountNewNotifications
                val content = if (isExceededMaxNotifs) "9+" else numCommentNofis.toString
                <.span(
                  tw.absolute.top0.right0.roundedFull,
                  tw.borderAll.border1.borderGray0,
                  tw.flex.justifyCenter.itemsCenter,
                  tw.textGray0.text11.leading16.fontSemiBold,
                  if (isExceededMaxNotifs) tw.px4 else TagMod.empty,
                  content,
                  ^.transform := "translate(50%, -50%)",
                  ^.height := CommentIconPx,
                  ^.minWidth := CommentIconPx,
                  tw.bgDanger5
                )
              }
            )
          ),
          renderContent = _(
            TagMod.when(commentCounts._1 > 0) {
              <.div(StringUtils.pluralItem(commentCounts._1, "open comment"))
            },
            TagMod.when(commentCounts._2 > 0) {
              <.div(StringUtils.pluralItem(commentCounts._2, "hidden comment"))
            }
          )
        )()
      }
    }
  }

  private def renderItem(
    props: TableOfContentComponent
  )(
    rProps: Tree.NodeRenderProps[FormState.FormSectionState]
  ): Option[VdomElement] = {
    if (rProps.node.id == props.formProcessor.formState.formSectionStates.id) {
      None
    } else {
      val sectionId = rProps.node.id
      val sectionDesc = props.formProcessor.getSection(sectionId).flatMap(_.formDescription)
      val isPage = isNodePage(props)(rProps.node)
      val hasChildren = loader(props).hasChildren(rProps.node)
      val selected = props.currentPage.contains(sectionId)
      val testID = if (selected) "SelectedItem" else "Item"
      val completed = isNodeCompleted(props, rProps.node)
      val content = if (rProps.node.isHidden) {
        val tooltip = sectionDesc
          .map(_.tooltip)
          .filterNot(StringUtils.isEmptyOrWhitespace)
          .getOrElse(DynamicFormConstants.DefaultPageTooltip)
        <.div(
          tw.flex.justifyBetween.textGray5.mb16,
          <.div(
            tw.flex.justifyBetween.flexFill,
            TooltipR(
              renderTarget = sectionDesc.map(_.label),
              renderContent = _(tooltip)
            )(),
            TagMod.unless(rProps.isExpanded && hasChildren) {
              renderCommentIcon(
                props,
                sectionId,
                isPage
              )
            }
          ),
          <.span(
            tw.hPx20.flex.itemsCenter,
            renderIcon(completed = false, showWarning = false)
          )
        )
      } else {
        <.div(
          ComponentUtils.testId(TableOfContentComponent, testID),
          tw.mb16,
          TagMod.when(selected)(tw.fontBold),
          TagMod.when(selected && (isSuccess(rProps.node.validation) || !props.showWarning))(tw.textPrimary4),
          TagMod.when(isFailure(rProps.node.validation) && props.showWarning)(tw.textDanger4),
          <.div(
            tw.flex.justifyBetween,
            <.div(
              tw.flex.justifyBetween.flexFill,
              sectionDesc.map(_.label),
              TagMod.unless(rProps.isExpanded && hasChildren) {
                renderCommentIcon(
                  props,
                  sectionId,
                  isPage
                )
              }
            ),
            <.span(
              ComponentUtils.testId(
                TableOfContentComponent,
                if (isSuccess(rProps.node.validation)) "Complete" else "Incomplete"
              ),
              tw.hPx20.flex.itemsCenter,
              TagMod.when(completed)(tw.textSuccess3),
              renderIcon(completed, props.showWarning && isFailure(rProps.node.validation))
            )
          ),
          TagMod.when(hasChildren || isPage) {
            tw.cursorPointer
          },
          ^.onClick --> (Callback.when(hasChildren)(rProps.toggle) >> Callback.when(isPage)(props.onNavigate(sectionId)))
        )
      }
      Some(content)
    }
  }

  private def loader(props: TableOfContentComponent) = Tree.Loader.Sync[FormState.FormSectionState](node =>
    if (isNodePage(props)(node)) {
      Seq.empty
    } else {
      node.children
        .filterNot(childNode =>
          props.formProcessor.getSection(childNode.id).exists { childSection =>
            FormSectionAttribute.hasAttribute(childSection.properties, FormSectionAttribute.Embedded)
          }
        )
        .map(child => child.copy(isHidden = child.isHidden || node.isHidden))
    }
  )

  private def isNodePage(props: TableOfContentComponent)(node: FormState.FormSectionState): Boolean = {
    props.formProcessor.getSection(node.id).exists { section =>
      FormSectionAttribute.hasAttribute(section.properties, FormSectionAttribute.Page)
    }
  }

  def render(props: TableOfContentComponent): VdomElement = {
    <.div(
      ComponentUtils.testId(TableOfContentComponent, "Container"),
      <.div(
        tw.flex.itemsCenter.mb16.pb16.borderBottom.borderGray3,
        <.h4(
          ComponentUtils.testId(TableOfContentComponent, "Header"),
          tw.fontBold.text15.leading24,
          "Table of Contents"
        ),
        <.div(tw.flexFill),
        <.div(
          tw.lg(tw.hidden),
          Button(
            style = Button.Style.Minimal(
              icon = Option(Icon.Glyph.Cross)
            ),
            onClick = props.onClose
          )()
        )
      ),
      <.div(
        ComponentUtils.testId(TableOfContentComponent, "ListItem"),
        tw.fontMedium,
        TypedTree(
          node = props.formProcessor.formState.formSectionStates,
          render = renderItem(props),
          getKey = _.id,
          loader = loader(props),
          shouldExpanded = node =>
            node == props.formProcessor.formState.formSectionStates || props.currentPage.exists(id =>
              FormProperty.isParentOf(node.id, id)
            )
        )()
      )
    )
  }

  private val component = ScalaComponent
    .builder[TableOfContentComponent](ComponentName)
    .stateless
    .render_P(render)
    .build

}
