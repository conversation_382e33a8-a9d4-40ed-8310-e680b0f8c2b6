// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.permission

import zio.Task

import anduin.funddata.endpoint.permission.*
import anduin.funddata.error.FundDataError.{FundDataAuthorizationError, FundDataValidationError}
import anduin.funddata.permission.FundDataRebacModel.*
import anduin.id.investorportal.InvestorPortalId
import anduin.model.common.user.UserId
import anduin.service.GeneralServiceException
import anduin.tapir.endpoint.AuthenticatedEndpointValidationParams
import anduin.tapir.server.AuthenticatedEndpointValidator
import com.anduin.stargazer.service.utils.ZIOUtils

private abstract class FundDataAuthorizationValidator[Params <: AuthenticatedEndpointValidationParams](
  override val id: String
) extends AuthenticatedEndpointValidator[Params](id) {

  final override def validate(params: Params, actor: UserId): Task[Unit] = {
    validateAuthorization(params, actor).mapError(err => GeneralServiceException(err.getMessage))
  }

  def validateAuthorization(params: Params, actor: UserId): Task[Unit]
}

/** External validation
  */

private[permission] final case class FundDataAnduinAdminValidator(
  override val id: String
)(
  using fundDataPermissionService: FundDataPermissionService
) extends FundDataAuthorizationValidator[FundDataAnduinAdminValidationParams](id) {

  override def validateAuthorization(params: FundDataAnduinAdminValidationParams, actor: UserId): Task[Unit] = {
    fundDataPermissionService.validateAnduinAdmin(actor)
  }

}

/** Firm-level validation
  */

private[permission] final case class FundDataFirmValidator(
  override val id: String,
  firmPermission: FirmPermission
)(
  using fundDataPermissionService: FundDataPermissionService
) extends FundDataAuthorizationValidator[FundDataFirmValidationParams](id) {

  override def validateAuthorization(params: FundDataFirmValidationParams, actor: UserId): Task[Unit] = {
    fundDataPermissionService.validateHasFirmPermission(actor, firmPermission, params.firmId)
  }

}

/** Client group-level validation
  */

private[permission] final case class FundDataClientGroupsValidator(
  override val id: String,
  clientGroupPermission: ClientGroupPermission
)(
  using fundDataPermissionService: FundDataPermissionService
) extends FundDataAuthorizationValidator[FundDataClientGroupsValidationParams](id) {

  override def validateAuthorization(params: FundDataClientGroupsValidationParams, actor: UserId): Task[Unit] = {
    ZIOUtils
      .foreachParN(4)(params.clientGroupIds) { clientGroupId =>
        fundDataPermissionService.validateHasClientGroupPermission(actor, clientGroupPermission, clientGroupId)
      }
      .unit
  }

}

/** Client-level validation
  */

private[permission] final case class FundDataClientsValidator(
  override val id: String,
  clientPermission: ClientPermission
)(
  using fundDataPermissionService: FundDataPermissionService
) extends FundDataAuthorizationValidator[FundDataInvestorsValidationParams](id) {

  override def validateAuthorization(params: FundDataInvestorsValidationParams, actor: UserId): Task[Unit] = {
    fundDataPermissionService
      .validateHasPermissionOnMultipleClients(actor, clientPermission, params.investorIds)
  }

}

/** Investment entity level validation
  */

private[permission] final case class FundDataInvestmentEntitiesValidator(
  override val id: String,
  investmentEntityPermission: InvestmentEntityPermission
)(
  using fundDataPermissionService: FundDataPermissionService
) extends FundDataAuthorizationValidator[FundDataInvestmentEntitiesValidationParams](id) {

  override def validateAuthorization(params: FundDataInvestmentEntitiesValidationParams, actor: UserId): Task[Unit] = {
    fundDataPermissionService
      .validateHasPermissionOnMultipleInvestmentEntities(
        actor,
        investmentEntityPermission,
        params.investmentEntityIds
      )
  }

}

private[permission] final case class FundDataOpportunityPagesValidator(
  override val id: String,
  opportunityPagePermission: OpportunityPagePermission
)(
  using fundDataPermissionService: FundDataPermissionService
) extends FundDataAuthorizationValidator[FundDataOpportunityPagesValidationParams](id) {

  override def validateAuthorization(params: FundDataOpportunityPagesValidationParams, actor: UserId): Task[Unit] = {
    ZIOUtils
      .foreachParN(4)(params.opportunityPageIds) { opportunityPageId =>
        fundDataPermissionService.validateHasOpportunityPagePermission(
          actor,
          opportunityPagePermission,
          opportunityPageId
        )
      }
      .unit
  }

}

/** Utilities validation
  */

private[permission] final case class FundDataPortalDocumentsAccessValidator(
  override val id: String
) extends FundDataAuthorizationValidator[FundDataPortalDocumentsValidationParams](id) {

  override def validateAuthorization(params: FundDataPortalDocumentsValidationParams, actor: UserId): Task[Unit] = {
    val fileChannels = params.fileIds.map { fileId =>
      fileId.channel match {
        case investorPortalId: InvestorPortalId => Some(investorPortalId)
        case _                                  => None
      }
    }
    val folderChannels = params.folderIds.map { folderId =>
      folderId.channel match {
        case investorPortalId: InvestorPortalId => Some(investorPortalId)
        case _                                  => None
      }
    }
    for {
      investorPortalIdOpt <- ZIOUtils.uniqueSeqToTask(
        fileChannels ++ folderChannels,
        FundDataValidationError(s"${fileChannels ++ folderChannels}'s channels are not unique")
      )
      _ <- ZIOUtils.fromOption(
        investorPortalIdOpt,
        FundDataValidationError(s"${fileChannels ++ folderChannels}'s channel is not DataRoomWorkflowId")
      )
      // TODO: Validate user is Firm Admin or Lp Guests
    } yield ()
  }

}

private[permission] final case class FundDataParentRelationValidator[P, C](
  override val id: String
) extends FundDataAuthorizationValidator[FundDataParentRelationValidationParams[P, C]](id) {

  override def validateAuthorization(params: FundDataParentRelationValidationParams[P, C], actor: UserId)
    : Task[Unit] = {
    ZIOUtils
      .foreachParN(4)(params.children) { child =>
        ZIOUtils.validate(params.isParent(params.parent, child))(
          FundDataAuthorizationError(
            s"$actor doesn't authorized to access $child as $child doesn't belong to ${params.parent}"
          )
        )
      }
      .unit
  }

}
