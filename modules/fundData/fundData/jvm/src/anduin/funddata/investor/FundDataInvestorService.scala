// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.investor

import java.time.Instant

import io.circe.syntax.*
import io.github.arainko.ducktape.*
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import squants.market.Money
import sttp.model.MediaType
import zio.implicits.*
import zio.prelude.Validation
import zio.temporal.workflow.ZWorkflowStub
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.batchaction.{BatchActionFrontendTracking, BatchActionService, BatchActionType}
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.documentcontent.spreadsheet.FillSheetData.{FillSheetCell, FillSheetRow}
import anduin.documentcontent.spreadsheet.{FillSheetData, FillSpreadsheet, SpreadsheetUtils}
import anduin.fdb.record.model.RecordIO
import anduin.fdb.record.{DefaultCluster, FDBRecordDatabase}
import anduin.forms.diff.ResolveFormDataConflictV2Engine
import anduin.funddata.clientgroup.FundDataClientGroupService
import anduin.funddata.constant.Constant
import anduin.funddata.contact.{FundDataContactRelationUtils, FundDataContactService}
import anduin.funddata.dashboard.{DashboardPagination, DashboardQuery}
import anduin.funddata.endpoint.clientgroup.{
  CheckDuplicatedNameClientGroupParams,
  CreateClientGroupParams,
  GetClientGroupsParams
}
import anduin.funddata.endpoint.fund.{
  CreateSubscriptionLinkToFundSubOrderParams,
  LinkSubscriptionToInvestmentEntityParams
}
import anduin.funddata.endpoint.investmententity.{
  ImportProfileDataFromLinkedSubscriptionOrderParams,
  MoveInvestmentEntityParams
}
import anduin.funddata.endpoint.investor.*
import anduin.funddata.endpoint.investor.ImportInvestorsBySpreadsheetParams.InvestorInfo
import anduin.funddata.endpoint.investor.ImportInvestorsFromSubscriptionParams.ImportOption
import anduin.funddata.endpoint.tag.FundDataTagItem
import anduin.funddata.error.FundDataError.FundDataValidationError
import anduin.funddata.event.FundDataEventService
import anduin.funddata.firm.FundDataFirmStoreOperations
import anduin.funddata.fund.subscription.{FundDataFundSubscriptionService, FundDataFundSubscriptionStoreOperations}
import anduin.funddata.fund.{FundDataFundQueryUtils, FundDataFundService, FundDataFundV2StoreOperations}
import anduin.funddata.investmententity.assessment.FundDataInvestmentEntityAssessmentService
import anduin.funddata.investmententity.contact.FundDataInvestmentEntityContactService
import anduin.funddata.investmententity.document.FundDataInvestmentEntityDocumentService.DocumentFromSource
import anduin.funddata.investmententity.document.{
  FundDataInvestmentEntityDocumentRequestService,
  FundDataInvestmentEntityDocumentService
}
import anduin.funddata.investmententity.profile.{FundDataInvestmentEntityProfileService, FundDataProfileConflictService}
import anduin.funddata.investmententity.{FundDataInvestmentEntityService, InvestmentEntityStoreOperations}
import anduin.funddata.investor.FundDataInvestorService.{CheckDuplicatedCustomIdParams, InvestorCommitment}
import anduin.funddata.permission.FundDataPermissionService
import anduin.funddata.permission.FundDataRebacModel.ClientPermission
import anduin.funddata.tag.FundDataTagService
import anduin.funddata.tag.FundDataTagService.{DocumentType, GenericTag, TagType}
import anduin.funddata.validation.FundDataValidationUtils
import anduin.fundsub.integration.FundSubExternalIntegrationService
import anduin.greylin.GreylinDataService
import anduin.greylin.modelti.{
  FundSubscriptionFlowType,
  SubscriptionOrderStatus,
  SubscriptionOrderSubscriptionDocumentType,
  SubscriptionOrderSupportingDocument
}
import anduin.greylin.operation.*
import anduin.greylin.operation.fundsub.SubscriptionOrderSubFundInvestmentOperations
import anduin.greylin.transform.FundSubscriptionTransform
import anduin.greylin.transform.FundSubscriptionTransform.CommitmentType
import anduin.id.batchaction.BatchActionId
import anduin.id.funddata.*
import anduin.id.fundsub.FundSubLpId
import anduin.id.tag.TagItemId
import anduin.model.common.user.UserId
import anduin.model.id.FundDataInvestorIdFactory
import anduin.model.notichannel.FundDataNotificationChannels
import anduin.model.optics.iso.SquantsIso
import anduin.protobuf.funddata.event.*
import anduin.protobuf.funddata.fund.subscription.v2.OfflineSubscription
import anduin.protobuf.funddata.investmententity.document.FundDataInvestmentEntityDocumentSourceImportDocumentFromSubscription
import anduin.protobuf.funddata.investmententity.profile.conflict.FundDataProfileConflictSourceImportFromFund
import anduin.protobuf.funddata.investor.FundDataInvestorModel
import anduin.service.GeneralServiceException
import anduin.signature.integration.SignatureIntegrationService
import anduin.storageservice.common.FileContentOrigin
import anduin.tag.v2.standard.AnduinStandardDocumentType
import anduin.tag.v2.{AddTagItemParams, TagItemColor}
import anduin.temporal.TemporalEnvironment
import anduin.user.UserService
import anduin.utils.{DateTimeUtils, SquantsUtils}
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOUtils

final case class FundDataInvestorService(
  fundDataEventService: FundDataEventService,
  fundDataFundSubscriptionService: FundDataFundSubscriptionService,
  fundDataInvestmentEntityProfileService: FundDataInvestmentEntityProfileService,
  fundDataInvestmentEntityContactService: FundDataInvestmentEntityContactService,
  batchActionService: BatchActionService,
  userProfileService: UserProfileService,
  temporalEnvironment: TemporalEnvironment,
  natsNotificationService: NatsNotificationService,
  fundSubExternalIntegrationService: FundSubExternalIntegrationService,
  fundDataProfileConflictService: FundDataProfileConflictService,
  fundDataClientGroupService: FundDataClientGroupService,
  fundDataInvestmentEntityDocumentRequestService: FundDataInvestmentEntityDocumentRequestService,
  fundDataContactService: FundDataContactService
)(
  using val signatureIntegrationService: SignatureIntegrationService,
  fundDataFundService: FundDataFundService,
  userService: UserService,
  fileService: FileService,
  fundDataInvestmentEntityDocumentService: FundDataInvestmentEntityDocumentService,
  fundDataInvestmentEntityAssessmentService: FundDataInvestmentEntityAssessmentService,
  fundDataInvestmentEntityService: FundDataInvestmentEntityService,
  greylinDataService: GreylinDataService,
  fundDataTagService: FundDataTagService,
  fundDataPermissionService: FundDataPermissionService
) {
  private given tagType: TagType = GenericTag

  private val parallelism = 4

  def checkDuplicatedInvestorData(
    params: CheckDuplicatedInvestorDataParams,
    actor: UserId
  ): Task[CheckDuplicatedInvestorDataResponse] = {
    val firmId = params.firmId
    for {
      _ <- ZIO.logInfo(s"$actor check duplicated investor data for $firmId")
      _ <- fundDataPermissionService.validateUserHasRole(firmId, actor)

      investorModels <- getAllInvestorsUnsafe(firmId)
      existingNames = investorModels.map(_.name).filter(_.nonEmpty).toSet
      existingCustomIds = investorModels.map(_.customId).filter(_.nonEmpty).toSet
    } yield CheckDuplicatedInvestorDataResponse(
      nameDuplicated = params.names.map(name => name -> existingNames.contains(name)).toMap,
      customIdDuplicated = params.customIds.map(id => id -> existingCustomIds.contains(id)).toMap
    )
  }

  def createInvestor(
    rawParams: CreateInvestorParams,
    actor: UserId
  ): Task[FundDataInvestorId] = {
    val params = rawParams.trim
    val clientGroupId =
      rawParams.clientGroupIdOpt.getOrElse(FundDataClientGroupId.PredefinedId.DefaultClientGroup(params.firmId))
    for {
      _ <- ZIO.logInfo(s"$actor creates investor for ${params.firmId}")
      _ <- fundDataPermissionService.validateUserHasRole(params.firmId, actor)
      _ <- fundDataPermissionService.validateUserCanCreateClientInClientGroup(clientGroupId, actor)

      newInvestorId <- createInvestorUnsafe(clientGroupId, params.name, params.customId, params.tags, actor)
    } yield newInvestorId
  }

  private def createInvestorUnsafe(
    clientGroupId: FundDataClientGroupId,
    name: String,
    customId: String,
    tags: Seq[String],
    actor: UserId
  ): Task[FundDataInvestorId] = {
    val firmId = clientGroupId.parent
    for {
      _ <- Validation
        .validate(
          FundDataValidationUtils.nonEmpty("name", name),
          FundDataValidationUtils.maxLength("name", name, maxLength = Constant.Client.maxNameLength),
          FundDataValidationUtils.when(customId.trim.nonEmpty)(
            FundDataValidationUtils.isValidCustomId("trackingId", customId.trim)
          ),
          FundDataValidationUtils.maxSize("tags", tags.toList, maxSize = Constant.Client.maxTagsWhenCreate)
        )
        .toZIO
        .mapError(FundDataValidationError(_))
      _ <- checkDuplicatedCustomIds(firmId, List(CheckDuplicatedCustomIdParams(customId)))
      _ <- fundDataClientGroupService.validateClientGroupExists(clientGroupId)

      newInvestorId <- ZIO.attempt(FundDataInvestorIdFactory.unsafeRandomId(firmId))
      _ <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
        _.createInvestor(
          FundDataInvestorModel(
            investorId = newInvestorId,
            name = name.trim,
            customId = customId.trim,
            createdAt = Some(Instant.now),
            createdBy = actor
          )
        )
      )
      _ <- fundDataPermissionService.updatePermissionWhenCreateInvestor(
        toAddInvestorId = newInvestorId,
        clientGroupIdOpt = Some(clientGroupId)
      )

      tagIds <- setNewInvestorTags(
        newInvestorId,
        tags.distinct,
        actor
      )
      _ <- fundDataEventService.push(
        FundDataEvent(
          firmId = firmId,
          actor = actor,
          createdAt = Some(Instant.now),
          detail = CreateInvestorEvent(
            investorId = newInvestorId,
            name = name.trim,
            customId = customId.trim,
            tagIds = tagIds
          )
        )
      )
    } yield newInvestorId
  }

  def getInvestorsComplianceSummary(
    firmId: FundDataFirmId,
    actor: UserId
  ): Task[InvestorsComplianceSummary] = {
    for {
      _ <- ZIO.logInfo(s"$actor gets investors' compliances of firm $firmId")
      _ <- fundDataPermissionService.validateUserHasRole(firmId, actor)
      allInvestorIds <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
        _.getInvestors(firmId).map(_.map(_.investorId))
      )
      accessibleInvestorIds <- fundDataPermissionService.filterInvestorsWithPermission(
        investorIds = allInvestorIds,
        permissionName = ClientPermission.CanView,
        actor = actor
      )
      documentSummaryByInvestors <- fundDataInvestmentEntityDocumentService.getInvestorsDocumentSummaryUnsafe(
        accessibleInvestorIds
      )
      assessmentDueDateByInvestors <- fundDataInvestmentEntityAssessmentService.getInvestorsAssessmentSummaryUnsafe(
        accessibleInvestorIds
      )
    } yield InvestorsComplianceSummary(
      noOfExpiredDocuments = documentSummaryByInvestors.values.map(_.noOfExpiredDocuments).sum,
      noOfExpiringSoonDocuments = documentSummaryByInvestors.values.map(_.noOfExpiringSoonDocuments).sum,
      noOfRequested = documentSummaryByInvestors.values.map(_.noOfRequested).sum,
      noOfPendingReview = documentSummaryByInvestors.values.map(_.noOfPendingReview).sum,
      noOfRequestedAndExpiring = documentSummaryByInvestors.values.map(_.noOfRequestedAndExpiring).sum,
      noOfRequestedAndExpired = documentSummaryByInvestors.values.map(_.noOfRequestedAndExpired).sum,
      noOfOverDueAssessments = assessmentDueDateByInvestors.values.map(_.noOfOverDueAssessments).sum,
      noOfUpcomingSoonAssessments = assessmentDueDateByInvestors.values.map(_.noOfUpcomingSoonAssessments).sum
    )
  }

  def getInvestors(
    params: GetInvestorsParams,
    actor: UserId
  ): Task[GetInvestorsResponse] = {
    val firmId = params.firmId
    for {
      _ <- ZIO.logInfo(s"$actor gets investors of firm $firmId")
      _ <- fundDataPermissionService.validateUserHasRole(firmId, actor)
      _ <- Validation
        .validate(
          FundDataValidationUtils.minValue("first", params.queryParams.limit, 1),
          FundDataValidationUtils.maxValue("first", params.queryParams.limit, 100),
          FundDataValidationUtils.minValue("offset", params.queryParams.offset, 0)
        )
        .toZIO
        .mapError(FundDataValidationError(_))

      allInvestorIds <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
        _.getInvestors(firmId).map(_.map(_.investorId))
      )
      accessibleInvestorIds <- fundDataPermissionService.filterInvestorsWithPermission(
        investorIds = allInvestorIds,
        permissionName = ClientPermission.CanView,
        actor = actor
      )
      accessibleInvestorIdWithIes <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production) { ops =>
        RecordIO.parTraverseN(parallelism)(accessibleInvestorIds) { investorId =>
          for {
            ieIds <- ops.get(investorId).map(_.map(_.investmentEntityId))
          } yield investorId -> ieIds
        }
      }
      dashboardQuery = DashboardQuery[(FundDataInvestorId, List[FundDataInvestmentEntityId])](
        data = accessibleInvestorIdWithIes,
        filters = List(
          Option.when(params.queryParams.filterByClientGroups.nonEmpty)(
            InvestorFilterByClientGroups(firmId, filterByClientGroups = params.queryParams.filterByClientGroups)
          ),
          Option.when(params.queryParams.searchText.trim.nonEmpty)(
            InvestorFilterBySearch(firmId, params.queryParams.searchText)
          ),
          Option.when(params.queryParams.filterByDocuments.nonEmpty)(
            InvestorFilterByDocument(params.queryParams.filterByDocuments)
          ),
          Option.when(params.queryParams.filterByFunds.nonEmpty)(
            InvestorFilterByFund(firmId, filterByFunds = params.queryParams.filterByFunds)
          ),
          Option.when(params.queryParams.filterByAssessments.nonEmpty)(
            InvestorFilterByAssessment(params.queryParams.filterByAssessments)
          ),
          Option.when(params.queryParams.filterByRisks.nonEmpty)(
            InvestorFilterByRisks(firmId, params.queryParams.filterByRisks)
          ),
          Option.when(params.queryParams.filterByInvestorTypes.nonEmpty)(
            InvestorFilterByTypes(firmId, params.queryParams.filterByInvestorTypes)
          ),
          Option.when(params.queryParams.filterByJurisdictions.nonEmpty) {
            InvestorFilterByJurisdiction(firmId, params.queryParams.filterByJurisdictions)
          },
          Option.when(params.queryParams.filterByTags.nonEmpty) {
            InvestorFilterByTags(firmId, params.queryParams.filterByTags)
          }
        ).flatten,
        sort = Some(InvestorSort(params.queryParams.sortBy, params.queryParams.sortOrder)),
        pagination = Some(DashboardPagination(params.queryParams.limit, params.queryParams.offset))
      )
      (investorsWithIEs, filteredInvestorsWithIEs) <- dashboardQuery.query
      investors <- getInvestorsInternal(firmId, investorsWithIEs.map(_._1), actor)
    } yield GetInvestorsResponse(
      investors,
      filteredInvestorIds = filteredInvestorsWithIEs.map(_._1).toSet,
      totalInvestors = accessibleInvestorIds.size
    )
  }

  def getInvestor(
    investorId: FundDataInvestorId,
    actor: UserId
  ): Task[GetInvestorResponse] = {
    val firmId = investorId.parent
    for {
      _ <- ZIO.logInfo(s"$actor gets investor $investorId")
      _ <- fundDataPermissionService.validateUserCanViewClient(investorId, actor)
      investors <- getInvestorsInternal(firmId, List(investorId), actor)
      investor <- ZIOUtils.optionToTask(
        investors.find(_.investorId == investorId),
        GeneralServiceException(s"Failed to get investor $investorId")
      )
      clientGroupOpt <- ZIOUtils.traverseOption(investor.clientGroupId)(
        fundDataClientGroupService.getClientGroupInternal(_)
      )
    } yield GetInvestorResponse(
      investor = investor,
      clientGroupOpt = clientGroupOpt
    )
  }

  private def getInvestorsInternal(
    firmId: FundDataFirmId,
    investorIds: List[FundDataInvestorId],
    actor: UserId
  ): Task[List[FundDataInvestor]] = {
    for {
      investorModels <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
        _.getInvestors(investorIds)
      )
      fundsByInvestor <- fundDataFundService.getFundsByInvestorInternal(
        firmId,
        investorIds
      )
      fundModels <- FDBRecordDatabase.transact(FundDataFundV2StoreOperations.Production)(ops =>
        RecordIO.parTraverseN(parallelism)(fundsByInvestor.values.flatten.toList) { fundId =>
          ops.getFund(fundId)
        }
      )
      linkedFundSubIds <- ZIO.succeed(fundModels.flatMap(_.linkedFundSubId).distinct)
      linkedFundSubsInfo <- greylinDataService.run(
        FundSubscriptionOperations
          .get(linkedFundSubIds)
          .map(_.map(fund => fund.id -> fund).toMap)
      )
      fundModelsMap = fundModels.map { fundModel =>
        val linkedFundSubInfoOpt = fundModel.linkedFundSubId.flatMap(linkedFundSubsInfo.get)
        fundModel.fundId -> linkedFundSubInfoOpt.map(_.name).getOrElse(fundModel.name)
      }

      investmentEntitiesByInvestor <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.get(investorIds)
      )
      documentSummaryByInvestor <- fundDataInvestmentEntityDocumentService.getInvestorsDocumentSummaryUnsafe(
        investorIds
      )
      assessmentSummaryByInvestor <- fundDataInvestmentEntityAssessmentService.getInvestorsAssessmentSummaryUnsafe(
        investorIds
      )
      genericTagMap <- fundDataTagService.getTagsByObjectsSameTagType(
        firmId,
        investorIds
      )
      totalCommitmentByInvestor <- getInvestorsTotalCommitment(investorIds, actor)
      clientGroupByInvestorMap <- fundDataPermissionService.getClientGroupIdByInvestorIdMap(firmId, investorIds)
    } yield investorModels.map {
      _.into[FundDataInvestor]
        .transform(
          Field.computed(
            _.funds,
            investor =>
              fundsByInvestor
                .getOrElse(investor.investorId, Set.empty)
                .flatMap(fundModelsMap.toMap.get)
                .toList
          ),
          Field.computed(
            _.investmentEntities,
            investor =>
              investmentEntitiesByInvestor.getOrElse(investor.investorId, List.empty).map { investmentEntity =>
                investmentEntity.investmentEntityId -> investmentEntity.name
              }
          ),
          Field.computed(
            _.documentSummary,
            investor => documentSummaryByInvestor.getOrElse(investor.investorId, FundDataInvestorDocumentSummary())
          ),
          Field.computed(
            _.assessmentSummary,
            investor => assessmentSummaryByInvestor.getOrElse(investor.investorId, FundDataInvestorAssessmentSummary())
          ),
          Field.computed(
            _.tags,
            investor => genericTagMap.getOrElse(investor.investorId, Seq.empty).toList
          ),
          Field.computed(
            _.totalCommitment,
            investor => totalCommitmentByInvestor.getOrElse(investor.investorId, List.empty)
          ),
          Field.computed(
            _.clientGroupId,
            investor => clientGroupByInvestorMap.get(investor.investorId)
          )
        )
    }
  }

  def getInvestorsBasic(
    firmId: FundDataFirmId,
    actor: UserId
  ): Task[List[FundDataInvestorBasic]] = {
    for {
      _ <- ZIO.logInfo(s"$actor get all investors names of $firmId")
      _ <- fundDataPermissionService.validateUserHasRole(firmId, actor)
      investorModels <- getAllInvestorsUnsafe(firmId)
      accessibleInvestorIds <- fundDataPermissionService.filterInvestorsWithPermission(
        investorIds = investorModels.map(_.investorId),
        permissionName = ClientPermission.CanView,
        actor
      )
      investorIdAndClientGroupIdMap <- fundDataPermissionService.getClientGroupIdByInvestorIdMap(
        firmId,
        accessibleInvestorIds
      )
      genericTagMap <- fundDataTagService.getTagsByObjectsSameTagType(
        firmId,
        investorIdAndClientGroupIdMap.keys.toList
      )
    } yield investorModels.flatMap { investorModel =>
      investorIdAndClientGroupIdMap.get(investorModel.investorId).map { clientGroupId =>
        FundDataInvestorBasic(
          investorId = investorModel.investorId,
          investorName = investorModel.name,
          customId = investorModel.customId,
          tags = genericTagMap.getOrElse(investorModel.investorId, Seq.empty),
          clientGroupId = clientGroupId
        )
      }
    }
  }

  def getInvestorsBasicByClientGroup(
    clientGroupId: FundDataClientGroupId,
    actor: UserId
  ): Task[List[FundDataInvestorBasic]] = {
    for {
      _ <- ZIO.logInfo(s"$actor get all investors names of $clientGroupId")
      _ <- fundDataPermissionService.validateUserCanViewClientGroup(clientGroupId, actor)

      // get investors in groups
      investorIds <- fundDataPermissionService.getInvestorIdsInClientGroup(clientGroupId)
      investorModels <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
        _.getInvestors(investorIds)
      )
      genericTagMap <- fundDataTagService.getTagsByObjectsSameTagType(clientGroupId.parent, investorIds)
    } yield investorModels.map { investorModel =>
      FundDataInvestorBasic(
        investorId = investorModel.investorId,
        investorName = investorModel.name,
        customId = investorModel.customId,
        tags = genericTagMap.getOrElse(investorModel.investorId, Seq.empty),
        clientGroupId = clientGroupId
      )
    }
  }

  def getInvestorsBasic(
    firmId: FundDataFirmId,
    clientIds: Set[FundDataInvestorId]
  ): Task[Map[FundDataInvestorId, FundDataInvestorBasic]] = {
    for {
      investorModels <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
        _.getInvestors(clientIds.toList)
      )
      genericTagMap <- fundDataTagService.getTagsByObjectsSameTagType(firmId, clientIds.toList)
      clientGroupMap <- fundDataPermissionService.getClientGroupIdByInvestorIdMap(firmId, clientIds.toList)
    } yield investorModels.map { investorModel =>
      investorModel.investorId -> FundDataInvestorBasic(
        investorId = investorModel.investorId,
        investorName = investorModel.name,
        customId = investorModel.customId,
        tags = genericTagMap.getOrElse(investorModel.investorId, Seq.empty),
        clientGroupId = clientGroupMap.getOrElse(
          investorModel.investorId,
          FundDataClientGroupId.PredefinedId.DefaultClientGroup(firmId)
        )
      )
    }.toMap
  }

  def getInvestorCommitments(
    investorId: FundDataInvestorId,
    actor: UserId
  ): Task[FundDataInvestorCommitment] = {
    for {
      _ <- ZIO.logInfo(s"$actor gets commitments of investor $investorId")
      _ <- fundDataPermissionService.validateUserHasRole(investorId.parent, actor)
      investorCommitment <- getInvestorsCommitment(List(investorId), actor)
      investmentEntityCommitments = investorCommitment.getOrElse(investorId, List.empty)
      fundNameMap <- FundDataFundQueryUtils.getFundsNameInternal(
        investmentEntityCommitments.flatMap(_.fundCommitments).map(_.fundId).distinct
      )
      investmentEntityNameMap <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.get(investorId)
          .map(_.map(investmentEntity => investmentEntity.investmentEntityId -> investmentEntity.name).toMap)
      )
      investmentEntitiesInfo = investmentEntityCommitments.map { investmentEntityCommitment =>
        FundDataInvestorCommitment.InvestmentEntityInfo(
          investmentEntityId = investmentEntityCommitment.investmentEntityId,
          investmentEntityName = investmentEntityNameMap.getOrElse(investmentEntityCommitment.investmentEntityId, ""),
          fundsInfo = investmentEntityCommitment.fundCommitments.map { fundCommitment =>
            FundDataInvestorCommitment.FundInfo(
              fundId = fundCommitment.fundId,
              fundName = fundNameMap.getOrElse(fundCommitment.fundId, ""),
              commitments = fundCommitment.commitments
            )
          }
        )
      }
    } yield FundDataInvestorCommitment(
      investorId,
      investmentEntities = investmentEntitiesInfo
    )
  }

  def editInvestor(
    rawParams: EditInvestorParams,
    actor: UserId
  ): Task[Unit] = {
    val params = rawParams.trim
    val investorId = params.investorId
    for {
      _ <- ZIO.logInfo(s"$actor edit investor $investorId")
      _ <- fundDataPermissionService.validateUserCanEditClient(investorId, actor)
      _ <- editInvestorUnsafe(
        investorId = params.investorId,
        nameOpt = params.name,
        customIdOpt = params.customId,
        tagsOpt = params.tags,
        actor = actor
      )
      _ <- natsNotificationService.publish(
        investorId,
        FundDataNotificationChannels.fundDataInvestorDashboard(investorId)
      )
    } yield ()
  }

  private def editInvestorUnsafe(
    investorId: FundDataInvestorId,
    nameOpt: Option[String],
    customIdOpt: Option[String],
    tagsOpt: Option[Seq[TagItemId]],
    actor: UserId
  ): Task[Unit] = {
    val firmId = investorId.parent
    for {
      _ <- ZIOUtils.traverseOption(nameOpt)(newName =>
        Validation
          .validate(
            FundDataValidationUtils.nonEmpty("name", newName),
            FundDataValidationUtils.maxLength("name", newName)
          )
          .toZIO
          .mapError(FundDataValidationError(_))
      )
      _ <- ZIOUtils.traverseOption(customIdOpt)(customId =>
        for {
          _ <- FundDataValidationUtils.convertError(
            FundDataValidationUtils.when(customId.trim.nonEmpty)(
              FundDataValidationUtils.isValidCustomId("trackingId", customId.trim)
            )
          )
          _ <- checkDuplicatedCustomIds(firmId, List(CheckDuplicatedCustomIdParams(customId, Some(investorId))))
        } yield ()
      )
      oldModel <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(ops =>
        for {
          oldModel <- ops.getInvestor(investorId)
          _ <- ops.updateInvestor(investorId)(oldInvestor =>
            oldInvestor.copy(
              name = nameOpt.map(_.trim).getOrElse(oldInvestor.name),
              customId = customIdOpt.map(_.trim).getOrElse(oldInvestor.customId)
            )
          )
        } yield oldModel
      )
      (oldTags, newTags) <- tagsOpt.fold(
        fundDataTagService.getTagsByObject(firmId, investorId).map(tags => tags -> tags)
      )(newTags =>
        fundDataTagService
          .updateTagsAssignedToObject(
            firmId,
            investorId,
            newTags.toList
          )
          .map(resp => resp.oldTags -> resp.newTags)
      )
      _ <- fundDataEventService.push(
        FundDataEvent(
          firmId = firmId,
          actor = actor,
          createdAt = Some(Instant.now),
          detail = UpdateInvestorEvent(
            investorId = investorId,
            oldName = oldModel.name,
            newName = nameOpt.map(_.trim).getOrElse(oldModel.name),
            oldCustomId = oldModel.customId,
            newCustomId = customIdOpt.map(_.trim).getOrElse(oldModel.customId),
            oldTotalCommitment = oldModel.totalCommitment,
            newTotalCommitment = oldModel.totalCommitment,
            oldTagIds = oldTags,
            newTagIds = newTags
          )
        )
      )
    } yield ()
  }

  def deleteInvestor(
    investorId: FundDataInvestorId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor delete investor $investorId")
      _ <- fundDataPermissionService.validateUserCanDeleteClient(investorId, actor)
      _ <- deleteInvestorUnsafe(investorId, actor)
    } yield ()
  }

  def deleteFirmInvestors(params: DeleteFirmInvestorsParams, actor: UserId): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor delete all investors in firm ${params.firmId}")
      _ <- fundDataPermissionService.validateAnduinAdmin(actor)
      investors <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
        _.getInvestors(params.firmId)
      )
      _ <- ZIO.foreachDiscard(investors) { investor =>
        deleteInvestorUnsafe(investor.investorId, actor)
      }
    } yield ()
  }

  private def deleteInvestorUnsafe(investorId: FundDataInvestorId, actor: UserId) = {
    for {
      clientGroupIdOpt <- fundDataPermissionService.getClientGroupIdOptOfInvestor(investorId)

      investorModel <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
        _.deleteInvestor(investorId)
      )
      _ <- fundDataPermissionService.updatePermissionWhenDeleteInvestor(investorId)
      _ <- fundDataInvestmentEntityService.deleteInvestmentEntitiesWhenDeleteInvestor(
        investorId,
        actor
      )
      _ <- FundDataContactRelationUtils.deleteContactRelationsWhenDeleteInvestor(investorId, actor)
      _ <- fundDataTagService.deleteObjectTags(List(investorId))
      _ <- ZIOUtils.traverseOption(clientGroupIdOpt) { clientGroupId =>
        fundDataClientGroupService.sendUpdateClientToClientGroupRelationNotification(investorId, clientGroupId)
      }
      _ <- fundDataEventService.push(
        FundDataEvent(
          firmId = investorId.parent,
          actor = actor,
          createdAt = Some(Instant.now),
          detail = DeleteInvestorEvent(
            investorId = investorId,
            name = investorModel.name,
            customId = investorModel.customId
          )
        )
      )
    } yield ()
  }

  def importInvestorsBySpreadsheet(params: ImportInvestorsBySpreadsheetParams, actor: UserId): Task[BatchActionId] = {
    val firmId = params.firmId
    val investorInfos = params.investors
    for {
      _ <- ZIO.logInfo(s"$actor import investors by spreadsheet for $firmId")
      _ <- fundDataPermissionService.validateUserHasRole(firmId, actor)
      _ <- FundDataValidationUtils
        .minSize("investors", investorInfos)
        .toZIO
        .mapError(GeneralServiceException(_))

      // Validate permission before run the whole batch
      _ <- ZIOUtils.foreachParN(parallelism)(investorInfos) { investorInfo =>
        investorInfo.importOption match {
          case ImportInvestorsBySpreadsheetParams.ImportOption.ImportToExistingInvestor(investorId) =>
            for {
              _ <- ZIOUtils.validate(investorId.parent == firmId)(
                FundDataValidationError(s"Investor $investorId doesn't belong to firm $firmId")
              )
              _ <- ZIOUtils.validate(verifyInvestorExisted(firmId, investorId))(
                FundDataValidationError(s"Investor $investorId doesn't exist in firm $firmId")
              )
            } yield ()
          case _ => ZIO.unit
        }
      }
      _ <- fundDataPermissionService.validateHasPermissionOnMultipleClients(
        actor,
        ClientPermission.CanEdit,
        params.investorIds
      )
      _ <- ZIOUtils.foreachParN(parallelism)(params.clientGroupIds) { clientGroupId =>
        fundDataPermissionService.validateUserCanCreateClientInClientGroup(clientGroupId, actor)
      }

      // create new client groups
      newClientGroupNames = params.investors
        .flatMap(_.importOption match {
          case ImportInvestorsBySpreadsheetParams.ImportOption.ImportNew(clientGroup) => clientGroup.toOption
          case _                                                                      => None
        })
        .distinct
      duplicatedNamesResp <- fundDataClientGroupService.checkDuplicatedNameClientGroup(
        params = CheckDuplicatedNameClientGroupParams(params.firmId, newClientGroupNames),
        actor = actor
      )
      _ <- ZIOUtils.validate(duplicatedNamesResp.duplicatedNames.isEmpty)(
        FundDataValidationError(s"Client group names ${duplicatedNamesResp.duplicatedNames} existed")
      )

      tagList <- fundDataTagService
        .getFirmTagListInternal(firmId)(
          using FundDataTagService.GenericTag
        )
      tagItems = tagList.tagItems.toSet
      notYetCreatedTags = params.investors
        .flatMap(_.tagsOpt.getOrElse(Seq.empty))
        .filterNot(tagName => tagItems.exists(tagItem => tagItem.name == tagName))
      _ <- fundDataTagService.createTagItemsIfNotExist(
        tagList.tagListId,
        tagItems = notYetCreatedTags
          .map(name =>
            AddTagItemParams(
              name.trim,
              TagItemColor.Gray,
              None
            )
          ),
        actor
      )
      batchActionId <- batchActionService.startBatchActionInternal(
        firmId,
        actor,
        actionType = BatchActionType.FundDataImportInvestorsBySpreadsheet,
        batchActionItemsData = params.investors.map(_.asJson),
        frontendTracking = BatchActionFrontendTracking.ACTOR_TRACKING,
        startWorkflow = workflowParams => {
          FundDataImportInvestorsBySpreadsheetWorkflowImpl.instance
            .getWorkflowStub()
            .provideEnvironment(temporalEnvironment.workflowClient)
            .flatMap(workflowStub => ZWorkflowStub.start(workflowStub.execute(workflowParams)))
        }
      )
    } yield batchActionId
  }

  def getDownloadSkippedClientsReport(
    params: GetDownloadSkippedClientReportsParams,
    actor: UserId
  ) // TODO: refactor to common function
    : Task[GetDownloadSkippedClientReportResp] = {
    for {
      _ <- ZIO.logInfo(s"$actor get download skipped client link when import investor for ${params.firmId}")
      _ <- fundDataPermissionService.validateUserHasRole(params.firmId, actor)
      _ <- FundDataValidationUtils.minSize("investors", params.clientInfos).toZIO.mapError(FundDataValidationError(_))
      firmModel <- FDBRecordDatabase.transact(FundDataFirmStoreOperations.Production)(_.get(params.firmId))

      userZoneId <- userService.getUserTimeZone(actor).map(_.getOrElse(DateTimeUtils.defaultTimezone))
      formattedDateTime = DateTimeUtils.formatInstant(
        Instant.now,
        DateTimeUtils.DefaultDateFormatter
      )(
        using userZoneId
      )

      rawData = List(List("Client name", "Tracking ID", "Tags", "Import errors")) ++ params.clientInfos.map {
        skippedClientInfo =>
          val skippedReasons =
            skippedClientInfo.skippedNameColReasons ++ skippedClientInfo.skippedTrackingIdReasons ++ skippedClientInfo.skippedTagsReasons
          List(
            skippedClientInfo.name,
            skippedClientInfo.customId,
            skippedClientInfo.tags,
            skippedReasons.map(_.reason).mkString(" ; ")
          )
      }

      fileName = firmModel.name + "_investor data import errors_" + formattedDateTime + ".xlsx"

      fillSpreadsheetData = FillSheetData(
        startCol = 0,
        startRow = 0,
        rows = rawData.map { row =>
          FillSheetRow(row.map(valueStr => FillSheetCell(valueStr)))
        }
      )

      result <- ZIO
        .attempt {
          new XSSFWorkbook()
        }
        .bracket { workbook =>
          val sheet = workbook.createSheet("Import_errors")
          for {
            _ <- ZIO.attempt {
              FillSpreadsheet.fillSheet(sheet, fillSpreadsheetData)
              FillSpreadsheet.setBoldRow(
                workbook = workbook,
                sheet = sheet,
                rowIndex = 1
              )
            }
            resultSource <- SpreadsheetUtils.workbookToStream(workbook)
          } yield resultSource
        } { workbook =>
          ZIO.succeed {
            workbook.close()
          }
        }

      userFolderId <- fileService.createUserTemporaryFolderIfNeeded(actor)

      generatedFileId <- fileService.uploadFile(
        userFolderId,
        fileName,
        FileContentOrigin.FromSource(
          result,
          MediaType("application", "vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        ),
        actor
      )
    } yield GetDownloadSkippedClientReportResp(fileId = generatedFileId)
  }

  def importInvestorsFromSubscription(
    params: ImportInvestorsFromSubscriptionParams,
    actor: UserId
  ): Task[BatchActionId] = {
    val firmId = params.firmId
    for {
      _ <- ZIO.logInfo(s"$actor import investors from subscription for ${params.firmId}")
      _ <- fundDataPermissionService.validateUserHasRole(firmId, actor)
      _ <- FundDataValidationUtils
        .minSize("investors", params.investors)
        .toZIO
        .mapError(GeneralServiceException(_))

      fundSubIds = params.investors.map(_.importFromSubscription.parent).distinct
      _ <- FundDataValidationUtils
        .maxSize("fund subs", fundSubIds, maxSize = 1)
        .toZIO
        .mapError(FundDataValidationError(_))

      fundIds = params.investors.map(_.fundId).distinct
      _ <- FundDataValidationUtils
        .maxSize("funds", fundIds, maxSize = 1)
        .toZIO
        .mapError(FundDataValidationError(_))
      _ <- ZIOUtils.traverseOption(fundSubIds.headOption.zip(fundIds.headOption)) { case (fundSubId, fundId) =>
        for {
          _ <- ZIOUtils.validate(fundId.parent == firmId)(
            FundDataValidationError(s"Fund $fundId doesn't belong to firm $firmId")
          )
          _ <- fundDataFundService.validateFundSubLinkWithFund(fundId, fundSubId)
          _ <- fundDataFundSubscriptionService.validateFundSubOrdersNotLinked(
            firmId,
            fundSubId,
            params.investors.map(_.importFromSubscription)
          )
        } yield ()
      }
      _ <- ZIOUtils.foreachParN(parallelism)(params.investors) { investor =>
        for {
          _ <- fundDataFundSubscriptionService.validateFundSubOrderAccessPermission(
            investor.importFromSubscription,
            actor
          )
          _ <- investor.importOption match {
            case ImportInvestorsFromSubscriptionParams.ImportOption.ImportToExistingInvestor(investorId) =>
              ZIOUtils.validate(investorId.parent == firmId)(
                FundDataValidationError(s"Investor $investorId doesn't belong to firm $firmId")
              )
            case ImportInvestorsFromSubscriptionParams.ImportOption.MergeToExistingInvestmentEntity(
                  investmentEntityId
                ) =>
              ZIOUtils.validate(investmentEntityId.parent.parent == firmId)(
                FundDataValidationError(s"Investment entity $investmentEntityId doesn't belong to firm $firmId")
              )
            case ImportInvestorsFromSubscriptionParams.ImportOption.ImportNew(clientGroupId) =>
              ZIOUtils.validate(clientGroupId.parent == firmId)(
                FundDataValidationError(s"Client group $clientGroupId doesn't belong to firm $firmId")
              )
          }
        } yield ()
      }

      investorsData =
        if (params.shouldGroupInvestorsWithSameName) {
          val toCreateNewInvestors = params.investors.collect { investor =>
            investor.importOption match {
              case importOption: ImportOption.ImportNew => investor -> importOption.clientGroupId
            }
          }
          val toCreateNewInvestorsGroupedByNameAndGroup = toCreateNewInvestors.groupMap {
            case (investor, clientGroupId) => investor.investorName -> clientGroupId
          }(_._1)
          val otherOptionInvestors = params.investors.collect { investor =>
            investor.importOption match {
              case _: ImportOption.ImportToExistingInvestor | _: ImportOption.MergeToExistingInvestmentEntity =>
                investor
            }
          }
          toCreateNewInvestorsGroupedByNameAndGroup.values.toList.map { investors =>
            ImportInvestorsFromSubscriptionParams.InvestorInfoBatch(
              firmId = firmId,
              investorInfos = investors,
              shouldGroupInvestorsWithSameName = params.shouldGroupInvestorsWithSameName
            )
          } ++ otherOptionInvestors.map { investor =>
            ImportInvestorsFromSubscriptionParams.InvestorInfoBatch(
              firmId = firmId,
              investorInfos = List(investor),
              shouldGroupInvestorsWithSameName = params.shouldGroupInvestorsWithSameName
            )
          }
        } else {
          params.investors.map { investor =>
            ImportInvestorsFromSubscriptionParams.InvestorInfoBatch(
              firmId = firmId,
              investorInfos = List(investor),
              shouldGroupInvestorsWithSameName = params.shouldGroupInvestorsWithSameName
            )
          }
        }
      sortedInvestorsData = investorsData.sortBy(_.investorInfos.headOption.fold("")(_.investorName.toLowerCase))

      batchActionId <- batchActionService.startBatchActionInternal(
        params.firmId,
        actor,
        actionType = BatchActionType.FundDataImportInvestorFromSubscription,
        batchActionItemsData = sortedInvestorsData.map(_.asJson),
        frontendTracking = BatchActionFrontendTracking.ACTOR_TRACKING,
        startWorkflow = workflowParams => {
          FundDataImportInvestorsFromSubscriptionWorkflowImpl.instance
            .getWorkflowStub()
            .provideEnvironment(temporalEnvironment.workflowClient)
            .flatMap(workflowStub => ZWorkflowStub.start(workflowStub.execute(workflowParams)))
        }
      )
    } yield batchActionId
  }

  def constructImportInvestorDataFromSubscriptionWhenGrouping(
    firmId: FundDataFirmId,
    investors: List[ImportInvestorsFromSubscriptionParams.InvestorInfo],
    actor: UserId
  ) = {
    val toCreateNewInvestorNameAndGroups = investors.flatMap { investor =>
      investor.importOption match {
        case ImportOption.ImportNew(clientGroupId) => Some(investor.investorName -> clientGroupId)
        case _                                     => None
      }
    }.distinct
    for {
      investorIdByInvestorName <- ZIO
        .foreach(toCreateNewInvestorNameAndGroups) { case (investorName, clientGroupId) =>
          createInvestor(
            CreateInvestorParams(
              firmId = firmId,
              name = investorName,
              customId = "",
              tags = List.empty,
              clientGroupIdOpt = Some(clientGroupId)
            ),
            actor = actor
          ).map(investorName -> _)
        }
        .map(_.toMap)
    } yield investors.flatMap { investor =>
      investor.importOption match {
        case _: ImportOption.ImportNew =>
          investorIdByInvestorName.get(investor.investorName).map { investorId =>
            investor.copy(importOption = ImportOption.ImportToExistingInvestor(investorId))
          }
        case _ => Some(investor)
      }
    }
  }

  private def verifyInvestorExisted(firmId: FundDataFirmId, investorId: FundDataInvestorId): Task[Boolean] = {
    FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
      _.getInvestorOpt(investorId).map(_.exists(_.investorId.parent == firmId))
    )
  }

  private[investor] def importInvestorBySpreadsheet(
    investorInfo: InvestorInfo,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor import investor by spreadsheet for ${investorInfo.firmId}")
      _ <- investorInfo.importOption match {
        case ImportInvestorsBySpreadsheetParams.ImportOption.ImportNew(clientGroupIdOrName) =>
          for {
            clientGroupId <- clientGroupIdOrName.fold(
              clientGroupId => ZIO.succeed(clientGroupId),
              clientGroupName => {
                for {
                  clientGroups <- fundDataClientGroupService.getClientGroups(
                    params = GetClientGroupsParams(investorInfo.firmId),
                    actor = actor
                  )
                  clientGroupId <- clientGroups.clientGroups
                    .find(_.name == clientGroupName)
                    .fold(
                      fundDataClientGroupService
                        .createClientGroup(
                          params = CreateClientGroupParams(investorInfo.firmId, clientGroupName),
                          actor = actor
                        )
                        .map(_.clientGroupId)
                    )(group => ZIO.succeed(group.clientGroupId))
                } yield clientGroupId
              }
            )
            _ <- createInvestorUnsafe(
              clientGroupId = clientGroupId,
              name = investorInfo.nameOpt.getOrElse(""),
              customId = investorInfo.customIdOpt.getOrElse(""),
              tags = investorInfo.tagsOpt.getOrElse(Seq.empty),
              actor = actor
            )
          } yield ()
        case ImportInvestorsBySpreadsheetParams.ImportOption.ImportToExistingInvestor(investorId) =>
          val firmId = investorId.parent
          for {
            _ <- ZIOUtils.validate(
              verifyInvestorIdExisted(firmId, investorId)
            )(
              GeneralServiceException(s"Unable to merge into a investor $investorId not in firm $firmId or is deleted")
            )
            tagItems <- fundDataTagService
              .getFirmTagListInternal(firmId)(
                using FundDataTagService.GenericTag
              )
              .map(_.tagItems)
            tagItemIdsOpt = investorInfo.tagsOpt
              .map(
                _.flatMap(tagItemName => tagItems.find(tagItem => tagItem.name == tagItemName))
                  .map(_.tagItemId)
              )
              .map(_.distinct)
            _ <- editInvestorUnsafe(
              investorId = investorId,
              nameOpt = investorInfo.nameOpt,
              customIdOpt = investorInfo.customIdOpt,
              tagsOpt = tagItemIdsOpt,
              actor = actor
            )
          } yield ()
      }
    } yield ()
  }

  private[investor] def importInvestorFromSubscription(
    investorInfo: ImportInvestorsFromSubscriptionParams.InvestorInfo,
    actor: UserId
  ): Task[ImportInvestorFromSubscriptionResponse] = {
    val firmId = investorInfo.fundId.parent
    for {
      _ <- ZIO.logInfo(s"$actor import investor from subscription in fund ${investorInfo.fundId}")
      _ <- fundDataPermissionService.validateUserHasRole(firmId, actor)
      _ <- fundDataFundService.validateFundSubLinkWithFirm(investorInfo.importFromSubscription.parent, firmId)

      ieId <- investorInfo.importOption match {
        case ImportOption.ImportNew(clientGroupId) =>
          createInvestorAndInvestmentEntityForImportFromPastSubscription(
            investorInfo.investorName,
            clientGroupId,
            investorInfo.investmentEntityName,
            investorInfo.importFromSubscription,
            actor
          )
        case ImportOption.ImportToExistingInvestor(investorId) =>
          for {
            _ <- ZIOUtils.validate(
              verifyInvestorIdExisted(firmId, investorId)
            )(
              GeneralServiceException(s"Unable to merge into a investor $investorId not in firm $firmId or is deleted")
            )
            ieId <- createInvestmentEntityForImportFromPastSubscription(
              investorId,
              investorInfo.investmentEntityName,
              investorInfo.importFromSubscription,
              actor
            )
          } yield ieId
        case ImportOption.MergeToExistingInvestmentEntity(investmentEntityId) => {
          for {
            _ <- ZIOUtils.validate(investmentEntityId.parent.parent == firmId)(
              GeneralServiceException(
                s"Unable to merge into investment entity $investmentEntityId not in firm $firmId or is deleted"
              )
            )
            _ <- ZIOUtils.validate(
              fundDataInvestmentEntityService.verifyInvestmentEntityExisted(firmId, investmentEntityId)
            )(
              GeneralServiceException(s"Unable to merge into a deleted investment entity $investmentEntityId")
            )
          } yield investmentEntityId
        }
      }

      subscriptionId <- fundDataFundSubscriptionService.createSubscriptionLinkToFundSubOrder(
        CreateSubscriptionLinkToFundSubOrderParams(
          firmId,
          investorInfo.importFromSubscription
        ),
        actor
      )
      _ <- fundDataFundSubscriptionService.linkSubscriptionToInvestmentEntity(
        LinkSubscriptionToInvestmentEntityParams(
          subscriptionId = subscriptionId,
          investmentEntityId = ieId
        ),
        actor
      )

      flowType <- greylinDataService
        .run(FundSubscriptionOperations.get(investorInfo.importFromSubscription.parent))
        .map(_.flowType)

      // import documents
      documentTypes <- fundDataTagService
        .getFirmTagListInternal(firmId)(
          using DocumentType
        )
        .map(_.tagItems)
      _ <- importSubscriptionDocumentsFromSubscription(
        investmentEntityId = ieId,
        subscriptionOrderId = investorInfo.importFromSubscription,
        documentTypes = documentTypes,
        actor = actor
      )
      _ <- importSupportingDocumentsFromSubscription(
        investmentEntityId = ieId,
        subscriptionOrderId = investorInfo.importFromSubscription,
        documentTypes = documentTypes,
        actor = actor,
        flowType = flowType
      )

      // import contacts
      _ <- importContactsFromSubscription(
        investmentEntityId = ieId,
        subscriptionOrderId = investorInfo.importFromSubscription,
        actor = actor
      )

      // import profile
      profileConflictIdOpt <- importProfileFromSubscriptionAndRaiseConflictIfAny(
        investmentEntityId = ieId,
        importFromSubscription = investorInfo.importFromSubscription,
        subscriptionId = subscriptionId,
        actor = actor,
        flowType = flowType
      )

    } yield ImportInvestorFromSubscriptionResponse(
      investorId = ieId.parent,
      investmentEntityId = ieId,
      fundSubscriptionId = subscriptionId,
      profileConflictIdOpt = profileConflictIdOpt
    )
  }

  private def createInvestorAndInvestmentEntityForImportFromPastSubscription(
    investorName: String,
    clientGroupId: FundDataClientGroupId,
    investmentEntityName: String,
    importFromSubscription: FundSubLpId,
    actor: UserId
  ) = {
    for {
      investorId <- createInvestorUnsafe(
        clientGroupId = clientGroupId,
        name = investorName,
        customId = "",
        tags = Seq.empty,
        actor = actor
      )
      ieId <- createInvestmentEntityForImportFromPastSubscription(
        investorId,
        investmentEntityName,
        importFromSubscription,
        actor
      )
    } yield ieId
  }

  private def createInvestmentEntityForImportFromPastSubscription(
    investorId: FundDataInvestorId,
    investmentEntityName: String,
    importFromSubscription: FundSubLpId,
    actor: UserId
  ) = {
    fundDataInvestmentEntityService.createInvestmentEntityUnsafe(
      investorId = investorId,
      name = investmentEntityName,
      customId = "",
      jurisdictionType = None,
      jurisdictionOtherType = "",
      investorType = None,
      investorOtherType = "",
      riskLevelOpt = None,
      actor = actor,
      source = ImportInvestmentEntityFromPastSubscription(importFromSubscription)
    )
  }

  private def importProfileFromSubscriptionAndRaiseConflictIfAny(
    investmentEntityId: FundDataInvestmentEntityId,
    importFromSubscription: FundSubLpId,
    subscriptionId: FundDataFundSubscriptionId,
    actor: UserId,
    flowType: FundSubscriptionFlowType
  ): Task[Option[FundDataProfileConflictId]] = {
    for {
      latestSubscriptionOrderVersionOpt <- greylinDataService.run(
        SubscriptionOrderVersionOperations.getLatestVersion(importFromSubscription)
      )

      // support import profile for flexible flow fund sub only
      profileConflictIdOpt <- ZIOUtils.whenOption(
        latestSubscriptionOrderVersionOpt.nonEmpty && flowType == FundSubscriptionFlowType.Flexible
      ) {
        for {
          computedProfileResp <- fundDataInvestmentEntityProfileService
            .computeProfileDataAppendWithSubscriptionDataInternal(
              investmentEntityId = investmentEntityId,
              fundSubOrderId = importFromSubscription,
              actor = actor
            )
          profileForm <- fundDataInvestmentEntityProfileService.formService
            .getForm(
              computedProfileResp.profileFormId.parent,
              Option(computedProfileResp.profileFormId),
              actor,
              shouldCheckPermission = false
            )
            .map(_.formData)
          resolveConflictEngine = ResolveFormDataConflictV2Engine(
            formData = profileForm,
            originalFormState = computedProfileResp.profileData,
            toUpdateFormState = computedProfileResp.convertedProfileData
          )
          (_, fieldConflictList) = resolveConflictEngine.computeCurrentFormStateAndFieldUpdateConflictList(
            selectedResolveMethod = Map.empty
          )
          profileConflictIdOpt <-
            if (fieldConflictList.isEmpty) {
              fundDataInvestmentEntityProfileService
                .importProfileDataFromLinkedSubscriptionOrder(
                  ImportProfileDataFromLinkedSubscriptionOrderParams(
                    investmentEntityId = investmentEntityId,
                    linkedSubscriptionId = subscriptionId,
                    resolvedProfileData = computedProfileResp.convertedProfileData,
                    importMethod = ImportProfileDataFromLinkedSubscriptionOrderParams.ManualImport
                  ),
                  actor
                )
                .as(Option.empty[FundDataProfileConflictId])
            } else {
              fundDataProfileConflictService
                .createProfileConflictUnsafe(
                  investmentEntityId,
                  Some(subscriptionId),
                  actor,
                  importSource = FundDataProfileConflictSourceImportFromFund(subscriptionId)
                )
                .map(Some(_))
            }
        } yield profileConflictIdOpt
      }
    } yield profileConflictIdOpt.flatten
  }

  private def importSubscriptionDocumentsFromSubscription(
    investmentEntityId: FundDataInvestmentEntityId,
    subscriptionOrderId: FundSubLpId,
    documentTypes: List[FundDataTagItem],
    actor: UserId
  ): Task[Unit] = {
    val subscriptionDocTypeOpt = documentTypes.find(
      _.anduinStandardType.contains(AnduinStandardDocumentType.SubscriptionDocument)
    )
    for {
      documents <- greylinDataService.run(
        for {
          subscriptionOrder <- SubscriptionOrderOperations.get(subscriptionOrderId)
          documents <- SubscriptionOrderSubscriptionDocumentOperations.getByOrderId(subscriptionOrderId)
        } yield subscriptionOrder.status match {
          case SubscriptionOrderStatus.PendingApproval | SubscriptionOrderStatus.Submitted =>
            documents.filter { doc =>
              doc.documentType == SubscriptionOrderSubscriptionDocumentType.LpFilled ||
              doc.documentType == SubscriptionOrderSubscriptionDocumentType.LpSigned
            }
          case SubscriptionOrderStatus.Countersigned | SubscriptionOrderStatus.Complete =>
            documents.filter { doc =>
              doc.documentType == SubscriptionOrderSubscriptionDocumentType.GpCountersigned
            }
          case _ => List.empty
        }
      )
      _ <- ZIO.when(documents.nonEmpty)(
        fundDataInvestmentEntityDocumentService.addDocumentsFromSource(
          investmentEntityId = investmentEntityId,
          documents = documents.map { doc =>
            DocumentFromSource(
              fileId = doc.fileId,
              note = "Subscription document",
              documentType = subscriptionDocTypeOpt.map(_.tagItemId),
              source = FundDataInvestmentEntityDocumentSourceImportDocumentFromSubscription(doc.subscriptionOrderId)
            )
          },
          actor = actor
        )
      )
    } yield ()
  }

  private def importSupportingDocumentsFromSubscription(
    investmentEntityId: FundDataInvestmentEntityId,
    subscriptionOrderId: FundSubLpId,
    documentTypes: List[FundDataTagItem],
    actor: UserId,
    flowType: FundSubscriptionFlowType
  ): Task[Unit] = {
    val nonSubscriptionDocTypes = documentTypes.filterNot(
      _.anduinStandardType.contains(AnduinStandardDocumentType.SubscriptionDocument)
    )
    for {
      // get supporting documents
      supportingDocuments <- flowType match {
        case FundSubscriptionFlowType.Flexible =>
          greylinDataService.run(
            SubscriptionOrderSupportingDocumentOperations.getByOrderId(subscriptionOrderId)
          )
        case FundSubscriptionFlowType.Restricted =>
          fundSubExternalIntegrationService
            .getRestrictedFlowLpSupportingDocs(lpId = subscriptionOrderId, actor = actor)
            .map(_.flatMap { supportingDoc =>
              supportingDoc.files.map { fileId =>
                SubscriptionOrderSupportingDocument(
                  fileId = fileId,
                  subscriptionOrderId = supportingDoc.lpId,
                  docType = supportingDoc.name,
                  isFundShared = false
                )
              }
            })
        case _ => ZIO.succeed(List.empty)
      }
      supportingDocumentParams <- ZIOUtils.foreachParN(parallelism)(supportingDocuments) { document =>
        fundDataInvestmentEntityDocumentService
          .autoDetectDocumentTypes(nonSubscriptionDocTypes, Some(document.fileId), Seq(document.docType))
          .map { docTypes =>
            DocumentFromSource(
              fileId = document.fileId,
              note = document.docType,
              documentType = docTypes.headOption.map(_.tagItemId),
              source = FundDataInvestmentEntityDocumentSourceImportDocumentFromSubscription(subscriptionOrderId),
              isDocumentTypeAutoDetected = docTypes.nonEmpty
            )
          }
      }

      // get supporting forms' files
      supportingFormFiles <- greylinDataService.run(
        TransformedSubscriptionOrderSupportingFormFileOperations.get(subscriptionOrderId)
      )
      supportingFormFileParams <- ZIOUtils.foreachParN(parallelism)(supportingFormFiles) { document =>
        fundDataInvestmentEntityDocumentService
          .autoDetectDocumentTypes(nonSubscriptionDocTypes, Some(document.fileId), Seq(document.docType))
          .map { docTypes =>
            DocumentFromSource(
              fileId = document.fileId,
              note = document.docType,
              documentType = docTypes.headOption.map(_.tagItemId),
              source = FundDataInvestmentEntityDocumentSourceImportDocumentFromSubscription(subscriptionOrderId),
              isDocumentTypeAutoDetected = docTypes.nonEmpty
            )
          }
      }

      // get supporting signature requests' files
      supportingSignatureRequests <- greylinDataService.run(
        SubscriptionOrderSupportingSignatureRequestOperations.get(subscriptionOrderId)
      )
      supportingSignatureFiles <- ZIO
        .foreach(supportingSignatureRequests) { request =>
          signatureIntegrationService.fetchRequestBasic(request.id, actor)
        }
        .map(_.flatMap(_.signedFileIds))
      supportingSignatureFileParams <- ZIO.attempt(
        supportingSignatureFiles.map { fileId =>
          DocumentFromSource(
            fileId = fileId,
            source = FundDataInvestmentEntityDocumentSourceImportDocumentFromSubscription(subscriptionOrderId)
          )
        }
      )

      documents = supportingDocumentParams ++ supportingFormFileParams ++ supportingSignatureFileParams
      _ <- ZIO.when(documents.nonEmpty)(
        fundDataInvestmentEntityDocumentService.addDocumentsFromSource(
          investmentEntityId = investmentEntityId,
          documents = documents,
          actor = actor
        )
      )
    } yield ()
  }

  private def importContactsFromSubscription(
    investmentEntityId: FundDataInvestmentEntityId,
    subscriptionOrderId: FundSubLpId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor import contacts from subscription $subscriptionOrderId")

      _ <- fundDataContactService.extractAndCreateContactFromOrderUnsafe(
        investmentEntityId = investmentEntityId,
        orderId = subscriptionOrderId,
        actor = actor
      )

    } yield ()
  }

  private def setNewInvestorTags(
    investorId: FundDataInvestorId,
    tags: Seq[String],
    actor: UserId
  ): Task[List[TagItemId]] = {
    for {
      genericTagListId <- FDBRecordDatabase.transact(FundDataFirmStoreOperations.Production)(
        _.get(investorId.parent).map(_.genericTagListId)
      )
      tagItemIds <- fundDataTagService.createTagItemsIfNotExist(
        genericTagListId,
        tagItems = tags
          .map(name =>
            AddTagItemParams(
              name.trim,
              TagItemColor.Gray,
              None
            )
          )
          .toList,
        actor
      )
      _ <- fundDataTagService.createOrUpdateObjectTags(
        investorId.parent,
        investorId,
        tagItemIds
      )
    } yield tagItemIds
  }

  def updateInvestorTags(
    params: UpdateInvestorTagsParams,
    actor: UserId
  ): Task[Unit] = {
    val investorId = params.investorId
    val firmId = investorId.parent
    for {
      _ <- ZIO.logInfo(s"$actor update tags for investor $investorId")
      _ <- fundDataPermissionService.validateUserHasRole(firmId, actor)
      resp <- fundDataTagService.updateTagsAssignedToObject(
        firmId,
        investorId,
        params.updatedTags
      )
      _ <- ZIOUtils.when(resp.oldTags.toSet != resp.newTags.toSet) {
        for {
          investorModel <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
            _.getInvestor(params.investorId)
          )
          _ <- fundDataEventService.push(
            FundDataEvent(
              firmId = firmId,
              actor = actor,
              createdAt = Some(Instant.now),
              detail = UpdateInvestorEvent(
                investorId = investorId,
                oldName = investorModel.name,
                newName = investorModel.name,
                oldCustomId = investorModel.customId,
                newCustomId = investorModel.customId,
                oldTotalCommitment = investorModel.totalCommitment,
                newTotalCommitment = investorModel.totalCommitment,
                oldTagIds = resp.oldTags,
                newTagIds = resp.newTags
              )
            )
          )
        } yield ()
      }
      _ <- natsNotificationService.publish(
        investorId,
        FundDataNotificationChannels.fundDataInvestorDashboard(investorId)
      )
    } yield ()
  }

  def getInvestorIdsByCustomIdInternal(firmId: FundDataFirmId, customId: String): Task[List[FundDataInvestorId]] = {
    FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
      _.getInvestorsByCustomId(firmId, customId).map(_.map(_.investorId))
    )
  }

  def verifyInvestorIdExisted(firmId: FundDataFirmId, investorId: FundDataInvestorId): Task[Boolean] =
    FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
      _.getInvestorOpt(investorId).map(_.exists(_.investorId.parent == firmId))
    )

  private[funddata] def getAllInvestorsUnsafe(firmId: FundDataFirmId): Task[List[FundDataInvestorModel]] = {
    FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
      _.getInvestors(firmId)
    )
  }

  private[funddata] def getAccessibleInvestorsUnsafe(investorIds: List[FundDataInvestorId], userId: UserId)
    : Task[List[FundDataInvestorModel]] = {
    for {
      accessibleInvestorIds <- fundDataPermissionService.filterInvestorsWithPermission(
        investorIds = investorIds,
        permissionName = ClientPermission.CanView,
        actor = userId
      )
      investorModels <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
        _.getInvestors(accessibleInvestorIds)
      )
    } yield investorModels

  }

  private def checkDuplicatedCustomIds(
    firmId: FundDataFirmId,
    customIds: List[CheckDuplicatedCustomIdParams]
  ): Task[Unit] = {
    val customIdsNonEmpty = customIds.filter(_.customId.trim.nonEmpty)
    for {
      _ <- ZIOUtils.failWhen(customIdsNonEmpty.distinctBy(_.customId).length != customIdsNonEmpty.length)(
        FundDataValidationError("duplicated tracking IDs")
      )

      duplicatedCustomIds <- FDBRecordDatabase
        .transact(FundDataInvestorStoreOperations.Production) { ops =>
          RecordIO.parTraverseN(parallelism)(customIdsNonEmpty) {
            case CheckDuplicatedCustomIdParams(customId, investorIdOpt) =>
              for {
                isDuplicated <- ops
                  .getInvestorsByCustomId(firmId, customId)
                  .map(_.map(_.investorId))
                  .map(_.filterNot(investorIdOpt.contains(_)))
                  .map(_.nonEmpty)
              } yield customId -> isDuplicated
          }
        }
      _ <- ZIOUtils.foreachParN(parallelism)(duplicatedCustomIds) { case (customId, isDuplicated) =>
        ZIOUtils.failWhen(isDuplicated)(
          FundDataValidationError(s"tracking ID '$customId' is already existed")
        )
      }
    } yield ()
  }

  private[investor] def getInvestorsTotalCommitment(investorIds: List[FundDataInvestorId], actor: UserId)
    : Task[Map[FundDataInvestorId, List[Money]]] = {
    for {
      commitmentByInvestor <- getInvestorsCommitment(investorIds, actor)
    } yield commitmentByInvestor.map { case (investorId, investmentEntityCommitment) =>
      val allInvestmentEntityCommitment = investmentEntityCommitment
        .flatMap(_.fundCommitments)
        .flatMap(_.commitments)
      investorId -> SquantsUtils.aggregateMoneys(allInvestmentEntityCommitment)
    }
  }

  private def getInvestorsCommitment(
    investorIds: List[FundDataInvestorId],
    actor: UserId
  ): Task[Map[FundDataInvestorId, List[InvestorCommitment.InvestmentEntityCommitment]]] = {
    for {
      subscriptionsByInvestor <- FDBRecordDatabase.transact(FundDataFundSubscriptionStoreOperations.Production) { ops =>
        RecordIO.parTraverseN(parallelism)(investorIds) { investorId =>
          ops.getSubscriptions(investorId).map(investorId -> _)
        }
      }

      // online subscription
      subscriptionIds = subscriptionsByInvestor
        .flatMap(_._2)
        .flatMap(_.linkedFundSubOrderId)
        .distinct
      commitmentsBySubscription <- greylinDataService.run(
        for {
          investments <- SubscriptionOrderSubFundInvestmentOperations.Default.getInvestmentsBySubscriptionOrders(
            subscriptionIds
          )
        } yield investments.groupMap(_._1.subscriptionOrderId) { case (investment, currency, subscriptionStatus) =>
          val amountOpt = FundSubscriptionTransform.subscriptionOrderStatusToCommitmentType(subscriptionStatus) match {
            case CommitmentType.AcceptedCommitment    => investment.acceptedCommitmentAmount
            case CommitmentType.UnderReviewCommitment => investment.commitmentAmount
            case CommitmentType.ExpectedCommitment    => investment.expectedCommitmentAmount
            case CommitmentType.Unclassified          => None
          }
          amountOpt.map(Money(_, currency))
        }
      )
      fundCurrencyConfigs <- fundSubExternalIntegrationService
        .getCurrencyConfig(fundSubIds = subscriptionIds.map(_.parent).distinct, actor)
        .map(_.map(config => config.fundSubId -> config).toMap)
      aggregatedCommitmentsByOnlineSubscription = commitmentsBySubscription.map { case (orderId, commitments) =>
        val currencyConfigOpt = fundCurrencyConfigs.get(orderId.parent)
        orderId -> SquantsUtils.aggregateMoneys(
          commitments.flatten,
          mainCurrencyOpt = currencyConfigOpt.flatMap(_.mainCurrencyOpt),
          exchangeRate = currencyConfigOpt.map(_.exchangeRates).getOrElse(Map.empty)
        )
      }

      // offline subscription
      aggregatedCommitmentsByOfflineSubscription = subscriptionsByInvestor.flatMap { case (_, subscriptions) =>
        subscriptions.flatMap { subscription =>
          subscription.subscriptionType match {
            case offline: OfflineSubscription => {
              val commitmentMessageOpt = offline.acceptedCommitment.orElse(offline.expectedCommitment)
              val commitmentOpt = commitmentMessageOpt.map(SquantsIso.moneyIso.reverseGet)
              commitmentOpt.map(subscription.subscriptionId -> _)
            }
            case _ => None
          }
        }
      }.toMap
    } yield subscriptionsByInvestor.toMap.map { case (investorId, subscriptions) =>
      val subscriptionByInvestmentEntity = subscriptions
        .flatMap(subscription => subscription.linkedInvestmentEntityId.map(_ -> subscription))
        .groupMap(_._1)(_._2)
      val investmentEntityCommitment = subscriptionByInvestmentEntity
        .map { case (investmentEntityId, subscriptions) =>
          val fundCommitments = subscriptions
            .groupBy(_.subscriptionId.parent)
            .map { case (fundId, subscriptions) =>
              val onlineCommitments = subscriptions
                .flatMap(_.linkedFundSubOrderId)
                .flatMap(aggregatedCommitmentsByOnlineSubscription.get)
                .flatten
              val offlineCommitments = subscriptions.flatMap { subscription =>
                aggregatedCommitmentsByOfflineSubscription.get(subscription.subscriptionId)
              }
              InvestorCommitment.FundCommitment(
                fundId = fundId,
                commitments = SquantsUtils.aggregateMoneys(onlineCommitments ++ offlineCommitments)
              )
            }
          InvestorCommitment.InvestmentEntityCommitment(
            investmentEntityId = investmentEntityId,
            fundCommitments = fundCommitments.toList
          )
        }
      investorId -> investmentEntityCommitment.toList
    }
  }

  def getInvestorsToMerge(params: GetInvestorsToMergeParams, actor: UserId): Task[GetInvestorsToMergeResponse] = {
    for {
      _ <- ZIO.logInfo(s"$actor get ${params.investorIds.size} investors to merge in ${params.firmId}")
      _ <- Validation
        .validate(
          FundDataValidationUtils.minSize("investorIds", params.investorIds),
          FundDataValidationUtils.maxSize(
            "investorIds",
            params.investorIds,
            maxSize = Constant.Client.maxMergeClient
          )
        )
        .toZIO
        .mapError(FundDataValidationError(_))
      investorsTags <- fundDataTagService.getTagsByObjectsSameTagType(
        params.firmId,
        params.investorIds
      )
      investors <- ZIOUtils.foreachParN(4)(params.investorIds) { investorId =>
        for {
          investorModel <- FDBRecordDatabase.transact(FundDataInvestorStoreOperations.Production)(
            _.getInvestor(investorId)
          )
          currentRequestedDocuments <- fundDataInvestmentEntityDocumentRequestService
            .getCurrentlyRequestedDocuments(investorId)
          currentProfileUpdateConflicts <- fundDataProfileConflictService
            .getProfileConflictModelsByInvestorUnsafe(investorId)
          hasCompletedDocumentRequests <- fundDataInvestmentEntityDocumentRequestService
            .checkHasFinishedDocumentRequests(investorId)
        } yield investorModel
          .into[GetInvestorsToMergeResponse.InvestorToMerge]
          .transform(
            Field.const(_.tags, investorsTags.getOrElse(investorId, Seq.empty)),
            Field.const(_.hasPendingDocumentRequests, currentRequestedDocuments.nonEmpty),
            Field.const(_.hasPendingProfileUpdateConflicts, currentProfileUpdateConflicts.nonEmpty),
            Field.const(_.hasCompletedDocumentRequests, hasCompletedDocumentRequests)
          )
      }
    } yield GetInvestorsToMergeResponse(
      investors = investors
    )
  }

  private[investor] def mergeInvestors(params: MergeInvestorsParams, actor: UserId) = {
    for {
      _ <- ZIO.logInfo(s"$actor merges investor ${params.srcInvestorId} into investor ${params.destInvestorId}")
      _ <- ZIOUtils.validate(params.srcInvestorId != params.destInvestorId) {
        FundDataValidationError(s"Cannot merge investor ${params.srcInvestorId} into itself")
      }
      _ <- fundDataPermissionService.validateUserCanEditClient(params.srcInvestorId, actor)
      _ <- fundDataPermissionService.validateUserCanEditClient(params.destInvestorId, actor)

      // move investment entities to dest investor
      ieModels <- FDBRecordDatabase.transact(InvestmentEntityStoreOperations.Production)(
        _.get(params.srcInvestorId)
      )
      _ <- ZIOUtils.foreachParN(4)(ieModels) { ieModel =>
        fundDataInvestmentEntityService.moveInvestmentEntity(
          params = MoveInvestmentEntityParams(ieModel.investmentEntityId, params.destInvestorId),
          actor = actor
        )
      }

      // delete src investor
      _ <- deleteInvestorUnsafe(params.srcInvestorId, actor)
    } yield ()
  }

  def batchMergeInvestors(params: BatchMergeInvestorsParams, actor: UserId): Task[BatchMergeInvestorsResp] = {
    for {
      _ <- ZIO.logInfo(
        s"$actor start manually merging ${params.srcInvestorIds.length} investor into investor ${params.destInvestorId}"
      )
      _ <- Validation
        .validate(
          FundDataValidationUtils.minSize("srcInvestorIds", params.srcInvestorIds),
          Validation.fromPredicateWith(s"srcInvestorIds ${params.srcInvestorIds} should be distinct")(
            params.srcInvestorIds.distinct.length == params.srcInvestorIds.length
          )(identity),
          Validation.fromPredicateWith(
            s"srcInvestorIds ${params.srcInvestorIds} should not include destInvestorId ${params.destInvestorId}"
          )(
            !params.srcInvestorIds.contains(params.destInvestorId)
          )(identity)
        )
        .toZIO
        .mapError(FundDataValidationError(_))
      _ <- fundDataPermissionService.validateHasPermissionOnMultipleClients(
        actor,
        ClientPermission.CanEdit,
        params.srcInvestorIds :+ params.destInvestorId
      )

      // validate no profile conflicts and no pending doc requests
      _ <- fundDataProfileConflictService.validateInvestorsHaveNoProfileConflict(
        params.firmId,
        params.srcInvestorIds :+ params.destInvestorId
      )
      _ <-
        fundDataInvestmentEntityDocumentService.fundDataInvestmentEntityDocumentRequestService
          .validateInvestorsHaveNoUnfinishedDocumentRequest(params.srcInvestorIds :+ params.destInvestorId)

      // start batch action
      batchActionId <- batchActionService.startBatchActionInternal(
        parent = params.firmId,
        actor = actor,
        actionType = BatchActionType.FundDataMergeInvestors,
        batchActionItemsData = params.srcInvestorIds.map { srcInvestorId =>
          MergeInvestorsParams(srcInvestorId, params.destInvestorId).asJson
        },
        frontendTracking = BatchActionFrontendTracking.ACTOR_TRACKING,
        startWorkflow = workflowParams => {
          FundDataMergeInvestorsWorkflowImpl.instance
            .getWorkflowStub()
            .provideEnvironment(temporalEnvironment.workflowClient)
            .flatMap(workflowStub => ZWorkflowStub.start(workflowStub.execute(workflowParams)))
        }
      )
    } yield BatchMergeInvestorsResp(batchActionId)
  }

}

object FundDataInvestorService {

  final case class CheckDuplicatedCustomIdParams(
    customId: String,
    ignoreInvestorIdOpt: Option[FundDataInvestorId] = None
  )

  object InvestorCommitment {

    final case class InvestmentEntityCommitment(
      investmentEntityId: FundDataInvestmentEntityId,
      fundCommitments: List[FundCommitment]
    )

    final case class FundCommitment(
      fundId: FundDataFundId,
      commitments: List[Money]
    )

  }

}
