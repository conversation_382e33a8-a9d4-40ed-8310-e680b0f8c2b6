//  Copyright (C) 2014-2024 Anduin Transactions Inc.

package anduin.gondor.server.funddata.contact

import sttp.tapir.server.armeria.zio.ArmeriaZioServerInterpreter

import anduin.funddata.contact.note.FundDataContactNoteService
import anduin.funddata.contact.{FundDataContactExportService, FundDataContactService}
import anduin.funddata.endpoint.contact.FundDataContactEndpoints.*
import anduin.funddata.endpoint.contact.note.FundDataContactNoteEndpoints.*
import anduin.funddata.permission.{FundDataEndpointServer, FundDataPermissionService}
import anduin.tapir.server.AuthenticatedValidationEndpointServer
import anduin.tapir.server.EndpointServer.TapirServerService
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.authorization.AuthorizationService

final case class FundDataContactEndpointServer(
  protected val backendConfig: GondorBackendConfig,
  fundDataPermissionService: FundDataPermissionService,
  fundDataContactService: FundDataContactService,
  fundDataContactExportService: FundDataContactExportService,
  fundDataContactNoteService: FundDataContactNoteService,
  override protected val interpreter: ArmeriaZioServerInterpreter[Any]
)(
  using val authorizationService: AuthorizationService
) extends AuthenticatedValidationEndpointServer
    with FundDataEndpointServer {

  private[server] val createContactService =
    validateRouteCatchError(createContact, validateCanManageFirmContact) { (params, ctx) =>
      fundDataContactService.createContact(params, ctx.actor.userId)
    }

  private[server] val checkDuplicatedContactInfoService =
    validateRouteCatchError(checkDuplicatedContactInfo, validateCanManageFirmContact) { (params, ctx) =>
      fundDataContactService.checkDuplicatedContactInfo(params, ctx.actor.userId)
    }

  private[server] val editContactService =
    validateRouteCatchError(editContactInfo, validateCanManageFirmContact) { (params, ctx) =>
      fundDataContactService.editContact(params, ctx.actor.userId)
    }

  private[server] val getFirmContactsService =
    validateRouteCatchError(getFirmContacts, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactService.getFirmContacts(params, ctx.actor.userId)
    }

  private[server] val getContactService =
    validateRouteCatchError(getContact, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactService.getContact(params, ctx.actor.userId)
    }

  private[server] val getFirmContactsInfoService =
    validateRouteCatchError(getFirmContactsInfo, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactService.getFirmContactsInfo(
        params,
        ctx.actor.userId
      )
    }

  private[server] val getInvestmentEntityContactsInfoService =
    validateRouteCatchError(getInvestmentEntityContactsInfo, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactService.getInvestmentEntityContactsInfo(
        params,
        ctx.actor.userId
      )
    }

  private[server] val getInvestmentEntitiesContactsInfoService =
    validateRouteCatchError(getInvestmentEntitiesContactsInfo, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactService.getInvestmentEntitiesContactsInfo(
        params,
        ctx.actor.userId
      )
    }

  private[server] val deleteContactService =
    validateRouteCatchError(deleteContact, validateCanDeleteFirmContact) { (params, ctx) =>
      fundDataContactService.deleteContact(params, ctx.actor.userId)
    }

  private val generalContactServices = List(
    createContactService,
    editContactService,
    getFirmContactsService,
    getContactService,
    deleteContactService,
    getFirmContactsInfoService,
    getInvestmentEntityContactsInfoService,
    getInvestmentEntitiesContactsInfoService,
    checkDuplicatedContactInfoService
  )

  private[server] val getContactAccessService =
    validateRouteCatchError(getContactAccess, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactService.getContactAccess(params, ctx.actor.userId)
    }

  private val contactAccessServices = List(getContactAccessService)

  private[server] val assignClientsAndInvestmentEntitiesService =
    validateRouteCatchError(
      assignClientsAndInvestmentEntities,
      validateCanManageFirmContact && validateCanEditClient && validateFirmAndClientsRelation
    ) { (params, ctx) =>
      fundDataContactService.assignClientsAndInvestmentEntities(params, ctx.actor.userId)
    }

  private[server] val getContactRelationService =
    validateRouteCatchError(getContactRelation, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactService.getContactRelation(params, ctx.actor.userId)
    }

  private val contactRelationServices = List(assignClientsAndInvestmentEntitiesService, getContactRelationService)

  private[server] val getContactMatrixService =
    validateRouteCatchError(getContactMatrix, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactService.getContactMatrix(
        params,
        ctx.actor.userId
      )
    }

  private[server] val updateContactMatrixService =
    validateRouteCatchError(
      updateContactMatrix,
      validateCanManageFirmContact && validateCanEditClient && validateFirmAndClientsRelation
    ) { (params, ctx) =>
      fundDataContactService.updateContactMatrix(
        params,
        ctx.actor.userId
      )
    }

  private[server] val getContactMatrixByInvestmentEntityService =
    validateRouteCatchError(
      getContactMatrixByInvestmentEntity,
      validateCanManageFirmContact && validateCanViewContact
    ) { (params, ctx) =>
      fundDataContactService.getContactMatrixByInvestmentEntity(
        params,
        ctx.actor.userId
      )
    }

  private val contactMatrixServices = List(
    getContactMatrixService,
    updateContactMatrixService,
    getContactMatrixByInvestmentEntityService
  )

  private[server] val exportContactsService =
    validateRouteCatchError(exportContacts, validateCanManageFirmContact) { (params, ctx) =>
      fundDataContactExportService.exportContacts(params, ctx.actor.userId)
    }

  private val contactExportServices = List(exportContactsService)

  private[server] val getContactNoteService =
    validateRouteCatchError(getContactNote, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactNoteService.getContactNote(params, ctx.actor.userId)
    }

  private[server] val updateContactNoteService =
    validateRouteCatchError(updateContactNote, validateCanManageFirmContact) { (params, ctx) =>
      fundDataContactNoteService.updateContactNote(params, ctx.actor.userId)
    }

  private[server] val deleteContactNoteService =
    validateRouteCatchError(deleteContactNote, validateCanManageFirmContact) { (params, ctx) =>
      fundDataContactNoteService.deleteContactNote(params, ctx.actor.userId)
    }

  private val contactNoteServices = List(
    getContactNoteService,
    updateContactNoteService,
    deleteContactNoteService
  )

  val services: List[TapirServerService] =
    List(
      generalContactServices,
      contactAccessServices,
      contactRelationServices,
      contactMatrixServices,
      contactExportServices,
      contactNoteServices
    ).flatten

}
