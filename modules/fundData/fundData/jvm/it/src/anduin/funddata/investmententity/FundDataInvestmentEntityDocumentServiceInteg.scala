// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.investmententity

import anduin.funddata.endpoint.investmententity.{
  AddDocumentsManuallyParams,
  GetDocumentsForInvestorsParams,
  GetInvestorDocumentsDownloadUrlParams
}
import anduin.funddata.permission.FundDataPermissionUtils
import anduin.funddata.utils.FundDataIntegUtils
import anduin.id.funddata.{
  FundDataFirmId,
  FundDataInvestmentEntityDocumentId,
  FundDataInvestmentEntityId,
  FundDataInvestorId
}
import anduin.model.common.user.UserId
import anduin.model.id.FileId
import anduin.testing.GondorCoreIntegUtils
import anduin.testing.FundDataBaseInteg
import zio.test.*

object FundDataInvestmentEntityDocumentServiceInteg extends FundDataBaseInteg with GondorCoreIntegUtils {
  // scalafix:off DisableSyntax.var
  var testFirmId: FundDataFirmId = scala.compiletime.uninitialized
  var testInvestorId: FundDataInvestorId = scala.compiletime.uninitialized
  var testInvestmentEntityId: FundDataInvestmentEntityId = scala.compiletime.uninitialized
  var testDocumentId: FundDataInvestmentEntityDocumentId = scala.compiletime.uninitialized
  var userId: UserId = scala.compiletime.uninitialized
  var testFileId: FileId = scala.compiletime.uninitialized
  // scalafix:on

  override def spec = suite("FundDataInvestmentEntityDocumentServiceInteg")(
    test("Setup firm, investor and investment entity") {
      for {
        resp <- FundDataIntegUtils.createFullFDM(
          createMemberParams = List(
            FundDataIntegUtils.CreateMemberParams(userCCEmail)
          ),
          createInvestorParams = List(FundDataIntegUtils.CreateInvestorParams()),
          createIEParams = List(FundDataIntegUtils.CreateIEParams())
        )
      } yield {
        testFirmId = resp.firmId
        testInvestorId = resp.investorIds.head
        testInvestmentEntityId = resp.investmentEntityIds.head
        userId = resp.memberIds.head
        assertCompletes
      }
    },
    test("User should be able to get documents for investors ") {
      for {
        fileIds <- uploadDemoFilesToChannel(
          actor = userId,
          fileCount = 1,
          folderIdOpt = Option(
            FundDataPermissionUtils
              .investmentEntityFolderId(testInvestmentEntityId, FundDataPermissionUtils.FolderType.DocumentFolder)
          )
        )
        documentId <- fundDataInvestmentEntityDocumentService
          .addDocumentsManually(
            investmentEntityId = testInvestmentEntityId,
            documents = fileIds.map { fileId =>
              AddDocumentsManuallyParams.Document(
                fileId = fileId
              )
            }.toList,
            actor = userId
          )
          .map(_.documentTextractResps.head.documentId)
        resp <- fundDataInvestmentEntityDocumentService.getDocumentsForInvestors(
          params = GetDocumentsForInvestorsParams(
            firmId = testFirmId,
            investorIds = List(testInvestorId)
          ),
          actor = userId
        )
      } yield {
        testDocumentId = documentId
        testFileId = fileIds.head
        assertTrue(
          resp.investorsWithDocuments.keys.toList.contains(testInvestorId),
          resp.investorsWithDocuments.values.toList.flatten.map(_.documentId).contains(documentId)
        )
      }
    },
    test("User should be able to get url download for document") {
      for {
        resp <- fundDataInvestmentEntityDocumentService.getInvestorDocumentsDownloadUrl(
          params = GetInvestorDocumentsDownloadUrlParams(
            firmId = testFirmId,
            documentIds = List(testDocumentId),
            namingConventions = List.empty,
            zipName = "zip name"
          ),
          actor = userId,
          httpContext = None
        )
      } yield {
        assertTrue(
          resp.taskIdOpt.isDefined
        )
      }
    }
  ) @@ TestAspect.sequential

}
