//  Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.contact.detail

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.menu.laminar.{MenuItemL, MenuL}
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.progress.CircleIndicator
import design.anduin.components.progress.laminar.{BlockIndicatorL, CircleIndicatorL}
import design.anduin.components.text.laminar.TruncateL
import design.anduin.style.tw.*

import anduin.funddata.api.contact.WithContact
import anduin.funddata.common.contactmatrix.ContactMatrixLegend
import anduin.funddata.common.icon.UserInfoBackgroundIcon
import anduin.funddata.common.layout.ObjectLayout
import anduin.funddata.common.tag.IdTag
import anduin.funddata.contact.ContactPageParams
import anduin.funddata.contact.add.{AssignClientsModal, EditContactsModal}
import anduin.funddata.contact.detail.contactaccess.ContactAccessView
import anduin.funddata.contact.detail.contactmatrix.{ContactMatrixView, EditContactMatrixModal}
import anduin.funddata.contact.detail.contactrelation.ContactRelationView
import anduin.funddata.contact.detail.note.ContactNoteView
import anduin.funddata.endpoint.contact.FundDataContact
import anduin.id.contact.ContactId
import anduin.id.funddata.FundDataFirmId
import com.anduin.stargazer.client.localstorage.{SessionStorage, SessionStorageKey}
import stargazer.component.routing.laminar.WithReactRouterL
import stargazer.model.routing.DynamicAuthPage.FundData

private[funddata] final case class ContactDetailPageContent(
  firmId: FundDataFirmId,
  contactId: ContactId,
  pageParamsSignal: Signal[ContactDetailPageParams],
  onSetPageParams: Observer[ContactDetailPageParams]
) {

  private val pageParamsUpdateEventBus = new EventBus[ContactDetailPageParams.ParamsUpdate]

  private val refetchContactInfoEventBus = new EventBus[Boolean]
  private val refetchContactRelationEventBus = new EventBus[Boolean]
  private val refetchContactMatrixEventBus = new EventBus[Boolean]

  private def renderEditContactDetail(closePopover: Observer[Unit]) = {
    ModalL(
      renderTitle = _ => "Edit contact details",
      renderTarget = openModal =>
        MenuItemL(
          color = MenuItemL.ColorGray,
          onClick = openModal
        )("Edit contact details"),
      renderContent = closeModal =>
        EditContactsModal(
          firmId = firmId,
          contactId = contactId,
          onCancel = closeModal,
          onDone = Observer.combine(
            closeModal,
            closePopover,
            Observer { _ => refetchContactInfoEventBus.emit(true) }
          )
        )(),
      isClosable = None,
      size = ModalL.Size(width = ModalL.Width.Px600, height = ModalL.Height.Content)
    )()

  }

  private def renderEditContactRelation(
    contactInfoSignal: Signal[Option[FundDataContact.Contact]],
    closePopover: Observer[Unit]
  ) = {
    ModalL(
      renderTitle = _ => "Manage clients and investment entities",
      renderTarget = openModal =>
        MenuItemL(
          color = MenuItemL.ColorGray,
          onClick = openModal
        )("Manage clients and investment entities"),
      renderContent = closeModal =>
        AssignClientsModal(
          firmId = firmId,
          contactId = contactId,
          contactInfoSignal = contactInfoSignal.map(_.map(_.contactInfo)),
          onCancel = closeModal,
          onDone = Observer.combine(
            closeModal,
            closePopover,
            Observer { _ =>
              refetchContactRelationEventBus.emit(true)
              refetchContactMatrixEventBus.emit(true)
            }
          )
        )(),
      isClosable = None,
      size = ModalL.Size(ModalL.Width.Px1160, ModalL.Height.Custom(85))
    )()
  }

  private def renderEditContactMatrix(
    contactInfoSignal: Signal[Option[FundDataContact.Contact]],
    closePopover: Observer[Unit]
  ) = {
    ModalL(
      renderTarget = openModal =>
        MenuItemL(
          color = MenuItemL.ColorGray,
          onClick = openModal
        )("Edit communication matrix"),
      renderContent = closeModal =>
        EditContactMatrixModal(
          firmId = firmId,
          contactId = contactId,
          contactNameSignal = contactInfoSignal.map(_.map(_.contactInfo.fullName).getOrElse("")),
          onCancel = closeModal,
          onDone = Observer.combine(
            closeModal,
            closePopover,
            Observer { _ => refetchContactMatrixEventBus.emit(true) }
          )
        )(),
      isClosable = None,
      size = ModalL.Size(ModalL.Width.Full, ModalL.Height.Full)
    )()
  }

  private def renderActions(
    contactInfoSignal: Signal[Option[FundDataContact.Contact]],
    closePopover: Observer[Unit]
  ) = {
    MenuL(
      Seq(
        renderEditContactDetail(closePopover),
        renderEditContactRelation(contactInfoSignal, closePopover),
        renderEditContactMatrix(contactInfoSignal, closePopover)
      )
    )
  }

  private def renderNavigation(contactInfoSignal: Signal[Option[FundDataContact.Contact]]) = {
    val contactPage = {
      val params = SessionStorage
        .get[ContactPageParams](SessionStorageKey.FundDataContactPageParams)
        .map(ContactPageParams.toUrlParams)
        .getOrElse(Map.empty)
      FundData.FundDataContactPage(firmId, params)
    }
    div(
      tw.bgOpacity0.flex.itemsCenter.justifyBetween,
      WithReactRouterL { router =>
        val url = router.urlFor(contactPage).value
        ButtonL(
          hasChildren = true,
          tpe = ButtonL.Tpe.Link(href = url, target = ButtonL.Target.Self),
          style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.ArrowLeft), color = ButtonL.Color.Primary)
        )("All contacts")
      },
      PopoverL(
        position = PortalPosition.BottomLeft,
        renderTarget = (toggle, isActive) =>
          ButtonL(
            style = ButtonL.Style.Ghost(
              isSelected = isActive,
              color = ButtonL.Color.Primary,
              endIcon = Some(Icon.Glyph.CaretDown)
            ),
            onClick = toggle.contramap(_ => ())
          )("Actions"),
        renderContent = closePopover => renderActions(contactInfoSignal, closePopover)
      )()
    )
  }

  private def renderInformationItem(name: String, value: String, lineClamp: Int = 1) = {
    div(
      tw.flex.flexCol.itemsStart.gap2,
      div(tw.textGray7.text11.fontMedium.leading32.uppercase, name),
      TruncateL(
        lineClamp = Some(lineClamp),
        target = div(tw.wPc100, if (value.nonEmpty) value else "--")
      )()
    )
  }

  private def renderContactInformation(renderProps: WithContact.RenderProps) =
    div(
      tw.flex.itemsCenter.relative.overflowHidden.px20.flexNone,
      borderRadius.px(20),
      background(
        "linear-gradient(90deg, rgba(233, 244, 255, 0.70) 0.26%, rgba(243, 248, 253, 0.70) 50.13%, rgba(233, 244, 255, 0.70) 100%), #FFF"
      ),
      height.px(94),
      UserInfoBackgroundIcon()(),
      child <-- renderProps.isGettingSignal.splitBoolean(
        whenTrue = _ =>
          div(
            tw.wPc100.flex.itemsCenter.justifyCenter,
            BlockIndicatorL(
              title = Val(Some("Loading contact...")),
              indicator = CircleIndicatorL(size = CircleIndicator.Size.Px16)()
            )().amend(height.px(52))
          ),
        whenFalse = _ =>
          div(
            child.maybe <-- renderProps.contactSignal.map {
              _.map { contact =>
                div(
                  refetchContactInfoEventBus --> Observer[Boolean] { shouldShowLoadingState =>
                    renderProps.refetch.onNext(shouldShowLoadingState)
                  },
                  tw.flex.itemsCenter,
                  gap.px(48),
                  div(
                    tw.flexNone.flex.itemsCenter.gap12,
                    div(
                      tw.roundedFull.flexCenter.textPrimary4,
                      width.px(52),
                      height.px(52),
                      background := "rgba(16, 117, 220, 0.10)",
                      IconL(name = Val(Icon.Glyph.UserSingle), size = Icon.Size.Px24)()
                    ),
                    div(
                      TruncateL(
                        target = h2(
                          tw.fontSemiBold.leading32.text20,
                          maxWidth.px(500),
                          contact.contactInfo.fullName
                        )
                      )(),
                      IdTag(contact.contactInfo.customId)()
                    )
                  ),
                  div(
                    gap.px(48),
                    tw.flexFill.flex.itemsStart,
                    renderInformationItem("Email", contact.contactInfo.email).amend(maxWidth.px(280)),
                    renderInformationItem("Phone", contact.contactInfo.phone).amend(maxWidth.px(120)),
                    renderInformationItem("Company", contact.contactInfo.company, lineClamp = 2).amend(maxWidth.px(280)),
                    renderInformationItem("Title", contact.contactInfo.title, lineClamp = 2).amend(maxWidth.px(180)),
                    renderInformationItem("Address", contact.contactInfo.address, lineClamp = 2).amend(tw.flexFill)
                  )
                )
              }
            }
          )
      )
    )

  private def renderDetailViewLayout(header: String, description: String, child: Node) = {
    div(
      div(tw.text20.fontSemiBold.leading32, header),
      div(tw.textGray7, description),
      div(tw.mt16, child)
    )
  }

  private def renderContactDetail(contactInfoSignal: Signal[Option[FundDataContact.Contact]]) = {
    div(
      tw.spaceY16,
      ObjectLayout(
        tabLayout = ObjectLayout.TabLayout.Horizontal,
        tabs = ContactDetailPageContent.TabList,
        onClickTab =
          pageParamsUpdateEventBus.writer.contramap(tab => ContactDetailPageParams.ParamsUpdate(tab = Some(tab))),
        currentTabSignal = pageParamsSignal.map(_.tab).distinct
      )(),
      child <-- pageParamsSignal.map(_.tab).map {
        case ContactDetailPageContent.CommunicationTab =>
          div(
            div(tw.text20.fontSemiBold.leading32, "Communication"),
            div(
              tw.flex.itemsCenter.justifyBetween.pb16,
              div(tw.textGray7, "Manage the type of communication this contact is authorized to receive"),
              ContactMatrixLegend()()
            ),
            ContactMatrixView(
              firmId,
              contactId,
              contactInfoSignal.map(_.map(_.contactInfo.fullName).getOrElse("")),
              refetchContactMatrixEventBus
            )()
          )
        case ContactDetailPageContent.ClientRelationTab =>
          renderDetailViewLayout(
            header = "Clients relation",
            description = "List of clients and investment entities assigned to this contact",
            child = ContactRelationView(
              firmId,
              contactId,
              contactInfoSignal.map(_.map(_.contactInfo)),
              refetchContactRelationEventBus
            )()
          )
        case ContactDetailPageContent.ContactAccessTab =>
          renderDetailViewLayout(
            header = "Products access",
            description = "List of products this contact has access to",
            child = ContactAccessView(firmId, contactId)()
          )
        case ContactDetailPageContent.ContactNoteTab =>
          ContactNoteView(contactId)()
        case _ => emptyNode
      }
    )
  }

  def apply(): HtmlElement = {
    div(
      tw.bgGray0.hPc100.wPc100.px32.pt16.pb32.flex.flexCol.overflowYAuto,
      pageParamsUpdateEventBus.events.withCurrentValueOf(pageParamsSignal)
        --> Observer[(ContactDetailPageParams.ParamsUpdate, ContactDetailPageParams)] { case (update, params) =>
          onSetPageParams.onNext(params.applyUpdate(update))
        },
      WithContact(
        firmId = firmId,
        contactId = contactId,
        render = renderProps => {
          div(
            renderNavigation(renderProps.contactSignal).amend(tw.mb16),
            renderContactInformation(renderProps).amend(tw.mb12),
            renderContactDetail(renderProps.contactSignal)
          )
        }
      )()
    )
  }

}

object ContactDetailPageContent {

  val CommunicationTab: ObjectLayout.TabItem = ObjectLayout.TabItem(
    name = "Communication",
    key = "communication",
    icon = Some(Icon.Glyph.Envelope)
  )

  val ClientRelationTab: ObjectLayout.TabItem = ObjectLayout.TabItem(
    name = "Client relation",
    key = "client-relation",
    icon = Some(Icon.Glyph.Toolbox)
  )

  val ContactAccessTab: ObjectLayout.TabItem = ObjectLayout.TabItem(
    name = "Contact access",
    key = "contact-access",
    icon = Some(Icon.Glyph.ViewGrid)
  )

  val ContactNoteTab: ObjectLayout.TabItem = ObjectLayout.TabItem(
    name = "Note",
    key = "note",
    icon = Some(Icon.Glyph.Comment)
  )

  val TabList: List[ObjectLayout.TabItem] = List(CommunicationTab, ClientRelationTab, ContactAccessTab, ContactNoteTab)

}
