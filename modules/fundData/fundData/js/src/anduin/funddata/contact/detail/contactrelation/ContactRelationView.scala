//  Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.contact.detail.contactrelation

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.collapse.Collapse
import design.anduin.components.collapse.laminar.CollapseL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.responsive.ScreenWidth
import design.anduin.components.responsive.laminar.WithScreenWidthL
import design.anduin.style.tw.*

import anduin.funddata.api.contact.WithContactRelation
import anduin.funddata.contact.add.AssignClientsModal
import anduin.funddata.endpoint.contact.FundDataContact
import anduin.id.contact.ContactId
import anduin.id.funddata.FundDataFirmId

private[detail] case class ContactRelationView(
  firmId: FundDataFirmId,
  contactId: ContactId,
  contactInfoSignal: Signal[Option[FundDataContact.ContactInfo]],
  refetchContactRelationEventBus: EventBus[Boolean]
) {

  private def renderCards(
    investor: FundDataContact.InvestorInfo,
    investmentEntities: List[FundDataContact.InvestmentEntityInfo]
  ) = {
    val isDisabled = investmentEntities.isEmpty
    CollapseL(
      direction = Collapse.Direction.TopToBottom,
      defaultIsOpened = !isDisabled,
      renderTarget = render => {
        div(
          tw.flex.itemsCenter.spaceX8,
          when(!isDisabled)(tw.cursorPointer),
          when(!isDisabled)(onClick.mapToUnit --> render.onToggle),
          IconL(
            Val(render.currentStatus match {
              case Collapse.Status.Open  => Icon.Glyph.CaretDown
              case Collapse.Status.Close => Icon.Glyph.CaretRight
            })
          )().amend(when(isDisabled)(tw.textGray6)),
          div(tw.textGray7, "Client:"),
          div(tw.heading3, investor.name)
        )
      }
    )(
      WithScreenWidthL { screenWidthSignal =>
        div(
          tw.mt16,
          tw.grid.gap12,
          cls <-- screenWidthSignal.map {
            case ScreenWidth.Tiny | ScreenWidth.Small => tw.gridCols1.css
            case ScreenWidth.Medium                   => tw.gridCols2.css
            case _                                    => tw.gridCols3.css
          },
          investmentEntities.map(investmentEntityInfo => ContactInvestmentEntityCard(investmentEntityInfo)())
        )
      }()
    )
  }

  private def renderEmptyState() = {
    div(
      height := "calc(100vh - 400px)",
      tw.flex.flexCol.itemsCenter.justifyCenter,
      NonIdealStateL(
        icon = img(src := "/web/gondor/images/funddata/organizations.svg"),
        title = "There is no client relations",
        description = "There are no clients and investment entities assigned to this contact",
        action = ModalL(
          renderTitle = _ => "Manage clients and investment entities",
          renderTarget = openModal =>
            ButtonL(
              style = ButtonL.Style.Full(
                color = ButtonL.Color.Primary,
                icon = Option(Icon.Glyph.Plus)
              ),
              onClick = openModal.contramap(_ => ())
            )("Manage clients and investment entities"),
          renderContent = closeModal =>
            AssignClientsModal(
              firmId = firmId,
              contactId = contactId,
              contactInfoSignal = contactInfoSignal,
              onCancel = closeModal,
              onDone = Observer.combine(
                closeModal,
                Observer { _ =>
                  refetchContactRelationEventBus.emit(true)
                }
              )
            )(),
          isClosable = None,
          size = ModalL.Size(ModalL.Width.Px1160, ModalL.Height.Custom(85))
        )()
      )()
    )
  }

  def apply(): HtmlElement = {
    div(
      tw.spaceY16,
      WithContactRelation(
        firmId = firmId,
        contactId = contactId,
        render = renderProps => {
          div(
            child <-- renderProps.isGettingSignal.splitBoolean(
              whenTrue = _ => BlockIndicatorL(title = Val(Some("Loading...")))(),
              whenFalse = _ =>
                div(
                  child.maybe <-- renderProps.contactRelationInfoSignal.map {
                    _.map { contactRelationInfo =>
                      div(
                        refetchContactRelationEventBus --> Observer[Boolean] { shouldShowLoadingState =>
                          renderProps.refetch.onNext(shouldShowLoadingState)
                        },
                        if (contactRelationInfo.investors.isEmpty) {
                          renderEmptyState()
                        } else {
                          div(
                            tw.spaceY16,
                            contactRelationInfo.investors.sortBy(_.name.toLowerCase()).map { investor =>
                              renderCards(
                                investor,
                                contactRelationInfo.investmentEntities
                                  .filter(_.investmentEntityId.parent == investor.investorId)
                                  .sortBy(_.name.toLowerCase)
                              )
                            }
                          )
                        }
                      )
                    }
                  }
                )
            )
          )
        }
      )()
    )
  }

}
