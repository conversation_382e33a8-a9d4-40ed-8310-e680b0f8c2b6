// Copyright (C) 2014-2024 Anduin Transaction Inc.

package anduin.funddata.contact

import scala.annotation.unused

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.menu.laminar.{MenuItemL, MenuL}
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*

import anduin.funddata.api.contact.WithContacts
import anduin.funddata.contact.`export`.ExportContactsModal
import anduin.funddata.contact.add.AddContactsModal
import anduin.funddata.contact.remove.RemoveContactsModal
import anduin.id.contact.ContactId
import anduin.id.funddata.FundDataFirmId
import com.anduin.stargazer.client.localstorage.{SessionStorage, SessionStorageKey}

private[funddata] final case class ContactPageContent(
  firmId: FundDataFirmId,
  pageParamsSignal: Signal[ContactPageParams],
  onSetPageParams: Observer[ContactPageParams]
) {

  private val pageParamsUpdateEventBus = new EventBus[ContactPageParams.ParamsUpdate]

  private val isExportingContactVar = Var(false)
  private val isExportingContactSignal = isExportingContactVar.signal

  private val selectedContactsVar = Var[Set[ContactId]](Set.empty)

  def apply(): HtmlElement = {
    div(
      tw.hPc100.wPc100,
      WithContacts(
        firmId,
        queryParamsSignal = pageParamsSignal.map(_.toQueryParams).distinct,
        onDidQuery = Observer { data =>
          selectedContactsVar.update(_.intersect(data.contactInfoAndRelationIds.map(_.contact.contactId).toSet))
        },
        render = contactsData => {
          div(
            tw.hPc100.flex.flexCol.px32.py24,
            child <-- contactsData.isGettingSignal.splitBoolean(
              whenTrue = _ => BlockIndicatorL(title = Val(Some("Loading contacts...")))(),
              whenFalse = _ =>
                div(
                  tw.hPc100,
                  child <-- contactsData.dataSignal.map(_.totalContacts).distinct.map { totalContacts =>
                    if (totalContacts == 0) {
                      renderNoInvestor(contactsData)
                    } else {
                      div(
                        tw.flex.flexCol.wPc100.hPc100.spaceY16,
                        renderHeader(contactsData),
                        renderContacts(contactsData).amend(tw.flexFill)
                      )
                    }
                  }
                )
            )
          )
        }
      )().amend(
        pageParamsUpdateEventBus.events
          .withCurrentValueOf(pageParamsSignal) --> Observer[(ContactPageParams.ParamsUpdate, ContactPageParams)] {
          case (update, params) =>
            val updatedParams = params.applyUpdate(update)
            onSetPageParams.onNext(updatedParams)
            SessionStorage.set(SessionStorageKey.FundDataContactPageParams, updatedParams)
        }
      )
    )
  }

  private def renderNoInvestor(contactData: WithContacts.RenderProps) = {
    NonIdealStateL(
      icon = img(src := "/web/gondor/images/funddata/magnifier-person.svg"),
      title = div(
        tw.textGray8.textBodyLarge.fontSemiBold,
        "There are no contacts"
      ),
      description = div(
        tw.textGray7.textBody,
        "Create contacts to easily manage them and keep track of their data"
      ),
      action = renderAdd(contactData)
    )().amend(height.px(650))
  }

  private def renderHeader(contactData: WithContacts.RenderProps) = {
    div(
      tw.flex.itemsCenter.justifyBetween,
      div(tw.heading1.fontSemiBold, "Contacts"),
      div(
        tw.flex.itemsCenter.spaceX8,
        renderBatch(contactData),
        renderBatchExportContact(),
        renderAdd(contactData)
      )
    )
  }

  private def renderBatch(contactData: WithContacts.RenderProps) = {
    PopoverL(
      position = PortalPosition.BottomLeft,
      renderTarget = (open, _) =>
        TooltipL(
          isDisabled = selectedContactsVar.signal.map(_.nonEmpty).distinct,
          renderContent = _.amend("Please select contacts first"),
          renderTarget = ButtonL(
            style = ButtonL.Style.Full(
              icon = Some(Icon.Glyph.MultiSelect),
              endIcon = Some(Icon.Glyph.CaretDown)
            ),
            onClick = open.contramap(_ => ()),
            isDisabled = selectedContactsVar.signal.map(_.isEmpty).distinct
          )("Batch")
        )(),
      renderContent = onCloseMenu =>
        MenuL(
          Seq(
            renderBatchRemoveContact(onCloseMenu, contactData.refetch)
          )
        )
    )()
  }

  private def renderBatchExportContact() = {
    ModalL(
      renderTitle = _ => "Export contacts",
      renderTarget = openModal =>
        TooltipL(
          isDisabled = selectedContactsVar.signal.map(_.nonEmpty).distinct,
          renderContent = _.amend("Please select contacts first"),
          renderTarget = ButtonL(
            style = ButtonL.Style.Full(
              icon = Some(Icon.Glyph.FileExport)
            ),
            onClick = openModal.contramap(_ => ()),
            isDisabled = selectedContactsVar.signal.map(_.isEmpty).distinct
          )("Export")
        )(),
      renderContent = closeModal =>
        ExportContactsModal(
          firmId = firmId,
          exportModeSignal = selectedContactsVar.signal.map(ExportContactsModal.Mode.Batch(_)),
          onCancel = closeModal,
          onDone = Observer.combine(
            closeModal,
            Observer { _ =>
              selectedContactsVar.set(Set.empty)
            }
          )
        )(),
      isClosable = None
    )()
  }

  private def renderBatchRemoveContact(onCloseMenu: Observer[Unit], refetchContacts: Observer[Unit]) = {
    ModalL(
      renderTitle = _ => "Delete contacts",
      afterUserClose = onCloseMenu,
      renderTarget = openModal =>
        MenuItemL(
          color = MenuItemL.ColorDanger,
          icon = Some(Icon.Glyph.Trash),
          onClick = openModal.contramap(_ => ())
        )("Delete contacts"),
      renderContent = closeModal =>
        RemoveContactsModal(
          firmId = firmId,
          removeModeSignal = selectedContactsVar.signal.map(RemoveContactsModal.Mode.Batch(_)),
          onCancel = closeModal,
          onDone = Observer.combine(
            closeModal,
            Observer { _ =>
              selectedContactsVar.set(Set.empty)
              refetchContacts.onNext(())
            }
          )
        )(),
      isClosable = None
    )()
  }

  private def renderAdd(
    contactData: WithContacts.RenderProps
  ) = {
    ModalL(
      renderTitle = _ => "Create contact",
      renderTarget = openModal =>
        ButtonL(
          style = ButtonL.Style.Full(
            color = ButtonL.Color.Primary,
            icon = Some(Icon.Glyph.Plus)
          ),
          onClick = openModal.contramap(_ => ())
        )("Create contact"),
      renderContent = closeModal =>
        AddContactsModal(
          firmId,
          onCancel = closeModal,
          onDone = Observer.combine(closeModal, contactData.refetch)
        )(),
      isClosable = None,
      size = ModalL.Size(width = ModalL.Width.Px600, height = ModalL.Height.Content)
    )()
  }

  @unused private def renderSearchAndFilter = {
    ContactSearchAndFilter(
      firmId,
      pageParamsSignal = pageParamsSignal,
      onUpdatePageParams = pageParamsUpdateEventBus.writer
    )()
  }

  private def renderContacts(contactData: WithContacts.RenderProps) = {
    ContactDashboard(
      firmId = firmId,
      contactData = contactData,
      selectedContactsSignal = selectedContactsVar.signal,
      onUpdateSelectedContacts = selectedContactsVar.writer,
      pageParamsSignal = pageParamsSignal,
      onUpdatePageParams = pageParamsUpdateEventBus.writer
    )()
  }

}
