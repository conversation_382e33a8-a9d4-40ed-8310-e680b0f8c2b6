// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.common.batchaction

import anduin.batchaction.BatchActionProvider.RemoveBatchActionFrontendTrackingProps
import anduin.batchaction.endpoint.BatchActionInfo
import anduin.batchaction.{BatchActionProvider, BatchActionType}
import anduin.funddata.fund.detail.CreateOfflineSubscriptionsTrackingModal
import anduin.funddata.fund.transaction.CreateOfflineTransactionsTrackingModal
import anduin.funddata.investmententity.assessment.importbyspreadsheet.ImportAssessmentsBySpreadsheetTrackingModal
import anduin.funddata.investmententity.contact.importbyspreadsheet.ImportContactsBySpreadsheetTrackingModal
import anduin.funddata.investmententity.document.importbyspreadsheet.ImportDocumentBySpreadsheetTrackingModal
import anduin.funddata.investmententity.importbyspreadsheetv2.trackingimport.ImportInvestmentEntitiesBySpreadsheetTrackingModal
import anduin.funddata.investmententity.profile.importbyspreadsheetv2.ui.tabs.utils.ImportProfileBySpreadsheetTrackingModal
import anduin.funddata.investor.ExportInvestmentEntitiesToSpreadsheetTrackingModal
import anduin.funddata.investor.add.ImportFromSubscriptionProgressTracking
import anduin.funddata.investor.add.importbyspreadsheetv2.trackingimport.ImportInvestorsBySpreadsheetTrackingModal
import anduin.funddata.investor.batchaction.batchrequest.BatchDocumentRequestProgressTracking
import anduin.funddata.landingpage.access.add.InviteGuestProgressTracking
import anduin.funddata.landingpage.access.modify.ModifyGuestsAccessToOpportunityPagesProgressTracking
import anduin.funddata.landingpage.access.notify.NotifyGuestsProgressTracking
import anduin.funddata.landingpage.access.remove.RemoveGuestsProgressTracking
import anduin.funddata.permission.clientgroup.BatchAssignInvestorsToClientGroupProgressTracking
import anduin.funddata.permission.member.BatchInviteMembersProgressTracking
import anduin.id.funddata.FundDataFirmId
import com.raquo.laminar.api.L.*
import design.anduin.components.wrapper.react.ReactiveWrapperR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import org.scalajs.dom

private[common] final case class FundDataBatchActionLayer(
  firmId: FundDataFirmId,
  batchActionProps: BatchActionProvider.RenderProps
) {
  def apply(): VdomElement = FundDataBatchActionLayer.component(this)
}

private[common] object FundDataBatchActionLayer {

  private type Props = FundDataBatchActionLayer

  private val reactiveWrapper = (new ReactiveWrapperR[List[BatchActionInfo]])()

  private def render(props: Props): VdomNode = {
    ReactPortal(
      <.div(
        tw.fixed.z999,
        ^.bottom := 24.px,
        ^.right := 24.px,
        renderActions(props)
      ),
      dom.document.body
    )
  }

  private def renderActions(props: Props) = {
    reactiveWrapper(
      value = Some(props.batchActionProps.batchActionInfos),
      renderNode = batchActionsOptSignal => {
        val batchActionsList = batchActionsOptSignal.map(_.getOrElse(List.empty))

        div(
          tw.flex.flexCol.justifyEnd,
          children <-- batchActionsList.split(_.batchActionId) { case (_, _, batchActionSignal) =>
            div(
              tw.spaceY8,
              child <-- batchActionSignal.map(_.actionType).distinct.map {
                case BatchActionType.FundDataImportInvestorFromSubscription =>
                  ImportFromSubscriptionProgressTracking(
                    batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case BatchActionType.FundDataImportInvestorsBySpreadsheet =>
                  ImportInvestorsBySpreadsheetTrackingModal(
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case BatchActionType.FundDataImportInvestmentEntitiesBySpreadsheet =>
                  ImportInvestmentEntitiesBySpreadsheetTrackingModal(
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case BatchActionType.FundDataImportContactsBySpreadsheet =>
                  ImportContactsBySpreadsheetTrackingModal(
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case BatchActionType.FundDataImportDocumentBySpreadsheet =>
                  ImportDocumentBySpreadsheetTrackingModal(
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case BatchActionType.FundDataImportProfilesFromSpreadsheet =>
                  ImportProfileBySpreadsheetTrackingModal(
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case BatchActionType.FundDataExportInvestmentEntitiesToSpreadsheet =>
                  ExportInvestmentEntitiesToSpreadsheetTrackingModal(
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case BatchActionType.FundDataImportRiskAssessmentsBySpreadsheet =>
                  ImportAssessmentsBySpreadsheetTrackingModal(
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case BatchActionType.FundDataBatchDocumentRequest =>
                  BatchDocumentRequestProgressTracking(
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case BatchActionType.FundDataInviteMembers =>
                  BatchInviteMembersProgressTracking(
                    firmId = props.firmId,
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case BatchActionType.FundDataAssignInvestorsToClientGroup =>
                  BatchAssignInvestorsToClientGroupProgressTracking(
                    firmId = props.firmId,
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props),
                    context = BatchAssignInvestorsToClientGroupProgressTracking.Context.AssignInvestors
                  )()
                case BatchActionType.FundDataDeleteClientGroup =>
                  BatchAssignInvestorsToClientGroupProgressTracking(
                    firmId = props.firmId,
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props),
                    context = BatchAssignInvestorsToClientGroupProgressTracking.Context.DeleteClientGroup
                  )()
                case BatchActionType.FundDataRemoveGuests =>
                  RemoveGuestsProgressTracking(
                    firmId = props.firmId,
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case BatchActionType.FundDataNotifyGuests =>
                  NotifyGuestsProgressTracking(
                    firmId = props.firmId,
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case BatchActionType.FundDataCreateOfflineSubscriptions =>
                  CreateOfflineSubscriptionsTrackingModal(
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case BatchActionType.FundDataCreateOfflineTransactions =>
                  CreateOfflineTransactionsTrackingModal(
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case BatchActionType.FundDataInviteGuests =>
                  InviteGuestProgressTracking(
                    firmId = props.firmId,
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case BatchActionType.FundDataModifyGuestsAccessToOpportunityPages =>
                  ModifyGuestsAccessToOpportunityPagesProgressTracking(
                    firmId = props.firmId,
                    batchActionInfoSignal = batchActionSignal,
                    removeBatchAction = removeBatchAction(props)
                  )()
                case _ => emptyNode
              }
            )
          }
        )
      }
    )()
  }

  private def removeBatchAction(props: Props) =
    Observer[RemoveBatchActionFrontendTrackingProps](props.batchActionProps.removeBatchAction(_).runNow())

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
