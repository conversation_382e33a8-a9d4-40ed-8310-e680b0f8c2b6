syntax = "proto3";

package anduin.protobuf.funddata.communication.firm;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "anduin/communication/id.proto";

option (scalapb.options) = {
	package_name: "anduin.protobuf.funddata.communication.firm"
	single_file: true
	import: "anduin.id.communication.CommunicationTypeId"
	import: "anduin.communication.CommunicationTypeIdTypeMapper.given"
	import: "java.time.Instant"
	import: "anduin.model.common.user.UserId"
	import: "anduin.model.id.TeamId"
};

message FundDataFirmCommunicationModel {
	anduin.communication.CommunicationTypeIdMessage communication_type_id = 1 [(scalapb.field).type = "CommunicationTypeId", (scalapb.field).no_box = true];
	string name = 2;
	bool is_default = 3;
	InstantMessage created_at = 4 [(scalapb.field).type = "Instant"];
	string created_by = 5 [(scalapb.field).type = "UserId"];
	string team_id = 6 [(scalapb.field).type = "TeamId"];
};

message RecordTypeUnion {
	FundDataFirmCommunicationModel _FundDataFirmCommunicationModel = 1;
}