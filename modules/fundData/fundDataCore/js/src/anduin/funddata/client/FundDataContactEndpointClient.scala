// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.client

import zio.Task

import anduin.funddata.endpoint.contact.*
import anduin.funddata.endpoint.contact.note.*
import anduin.funddata.endpoint.note.FundDataNote
import anduin.id.funddata.note.FundDataNoteId
import anduin.service.GeneralServiceException
import anduin.tapir.client.AuthenticatedEndpointClient

object FundDataContactEndpointClient extends AuthenticatedEndpointClient {

  val createContact: CreateContactParams => Task[Either[GeneralServiceException, CreateContactResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.createContact)

  val checkDuplicatedContactInfo
    : CheckDuplicatedContactInfoParams => Task[Either[GeneralServiceException, CheckDuplicatedContactInfoResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.checkDuplicatedContactInfo)

  val editContactInfo: EditContactParams => Task[Either[GeneralServiceException, EditContactResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.editContactInfo)

  val getFirmContacts: GetFirmContactsParams => Task[Either[GeneralServiceException, GetFirmContactsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getFirmContacts)

  val getContact: GetContactParams => Task[Either[GeneralServiceException, GetContactResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getContact)

  val deleteContact: DeleteContactParams => Task[Either[GeneralServiceException, DeleteContactResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.deleteContact)

  val getContactAccess: GetContactAccessParams => Task[Either[GeneralServiceException, GetContactAccessResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getContactAccess)

  val assignClientsAndInvestmentEntities: AssignClientAndInvestmentEntitiesParams => Task[
    Either[GeneralServiceException, AssignClientAndInvestmentEntitiesResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.assignClientsAndInvestmentEntities)

  val getContactRelation: GetContactRelationParams => Task[
    Either[GeneralServiceException, GetContactRelationResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getContactRelation)

  val getContactMatrix: GetContactMatrixParams => Task[Either[GeneralServiceException, GetContactMatrixResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getContactMatrix)

  val updateContactMatrix
    : UpdateContactMatrixParams => Task[Either[GeneralServiceException, UpdateContactMatrixResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.updateContactMatrix)

  val getFirmContactsInfo
    : GetFirmContactsInfoParams => Task[Either[GeneralServiceException, GetFirmContactsInfoResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getFirmContactsInfo)

  val getInvestmentEntityContactsInfo: GetInvestmentEntityContactsInfoParams => Task[
    Either[GeneralServiceException, GetInvestmentEntityContactsInfoResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getInvestmentEntityContactsInfo)

  val getInvestmentEntitiesContactsInfo: GetInvestmentEntitiesContactsInfoParams => Task[
    Either[GeneralServiceException, GetInvestmentEntitiesContactsInfoResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getInvestmentEntitiesContactsInfo)

  val getContactMatrixByInvestmentEntity: GetContactMatrixByInvestmentEntityParams => Task[
    Either[GeneralServiceException, GetContactMatrixByInvestmentEntityResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getContactMatrixByInvestmentEntity)

  val exportContacts: ExportContactsParams => Task[Either[GeneralServiceException, ExportContactsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.exportContacts)

  /** Note
    */

  val getContactNote: GetContactNoteParams => Task[Either[GeneralServiceException, Option[FundDataNote]]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactNoteEndpoints.getContactNote)

  val updateContactNote: UpdateContactNoteParams => Task[Either[GeneralServiceException, FundDataNoteId]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactNoteEndpoints.updateContactNote)

  val deleteContactNote: DeleteContactNoteParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactNoteEndpoints.deleteContactNote)

}
