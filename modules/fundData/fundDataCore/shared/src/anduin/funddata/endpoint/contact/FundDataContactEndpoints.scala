// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.endpoint.contact

import sttp.tapir.*

import anduin.service.GeneralServiceException
import anduin.tapir.AuthenticatedEndpoints
import anduin.tapir.AuthenticatedEndpoints.BaseAuthenticatedEndpoint

object FundDataContactEndpoints extends AuthenticatedEndpoints {
  private val Path = "funddata" / "contact"

  val createContact: BaseAuthenticatedEndpoint[CreateContactParams, GeneralServiceException, CreateContactResponse] = {
    authEndpoint[CreateContactParams, GeneralServiceException, CreateContactResponse](
      Path / "createContact"
    )
  }

  val checkDuplicatedContactInfo: BaseAuthenticatedEndpoint[
    CheckDuplicatedContactInfoParams,
    GeneralServiceException,
    CheckDuplicatedContactInfoResponse
  ] = {
    authEndpoint[
      CheckDuplicatedContactInfoParams,
      GeneralServiceException,
      CheckDuplicatedContactInfoResponse
    ](
      Path / "checkDuplicatedContactInfo"
    )
  }

  val editContactInfo: BaseAuthenticatedEndpoint[EditContactParams, GeneralServiceException, EditContactResponse] = {
    authEndpoint[EditContactParams, GeneralServiceException, EditContactResponse](
      Path / "editContactInfo"
    )
  }

  val getFirmContacts: BaseAuthenticatedEndpoint[
    GetFirmContactsParams,
    GeneralServiceException,
    GetFirmContactsResponse
  ] = {
    authEndpoint[
      GetFirmContactsParams,
      GeneralServiceException,
      GetFirmContactsResponse
    ](
      Path / "getFirmContacts"
    )
  }

  val getContact: BaseAuthenticatedEndpoint[GetContactParams, GeneralServiceException, GetContactResponse] = {
    authEndpoint[GetContactParams, GeneralServiceException, GetContactResponse](
      Path / "getContact"
    )
  }

  val getFirmContactsInfo: BaseAuthenticatedEndpoint[
    GetFirmContactsInfoParams,
    GeneralServiceException,
    GetFirmContactsInfoResponse
  ] = {
    authEndpoint[
      GetFirmContactsInfoParams,
      GeneralServiceException,
      GetFirmContactsInfoResponse
    ](
      Path / "getFirmContactsInfo"
    )
  }

  val getInvestmentEntityContactsInfo: BaseAuthenticatedEndpoint[
    GetInvestmentEntityContactsInfoParams,
    GeneralServiceException,
    GetInvestmentEntityContactsInfoResponse
  ] = {
    authEndpoint[
      GetInvestmentEntityContactsInfoParams,
      GeneralServiceException,
      GetInvestmentEntityContactsInfoResponse
    ](
      Path / "getInvestmentEntityContactsInfo"
    )
  }

  val getInvestmentEntitiesContactsInfo: BaseAuthenticatedEndpoint[
    GetInvestmentEntitiesContactsInfoParams,
    GeneralServiceException,
    GetInvestmentEntitiesContactsInfoResponse
  ] = {
    authEndpoint[
      GetInvestmentEntitiesContactsInfoParams,
      GeneralServiceException,
      GetInvestmentEntitiesContactsInfoResponse
    ](
      Path / "getInvestmentEntitiesContactsInfo"
    )
  }

  val deleteContact: BaseAuthenticatedEndpoint[DeleteContactParams, GeneralServiceException, DeleteContactResponse] = {
    authEndpoint[DeleteContactParams, GeneralServiceException, DeleteContactResponse](
      Path / "deleteContact"
    )
  }

  private val PathContactAccess = Path / "access"

  val getContactAccess: BaseAuthenticatedEndpoint[
    GetContactAccessParams,
    GeneralServiceException,
    GetContactAccessResponse
  ] = {
    authEndpoint[
      GetContactAccessParams,
      GeneralServiceException,
      GetContactAccessResponse
    ](
      PathContactAccess / "getContactAccess"
    )
  }

  private val PathRelation = Path / "relation"

  val assignClientsAndInvestmentEntities: BaseAuthenticatedEndpoint[
    AssignClientAndInvestmentEntitiesParams,
    GeneralServiceException,
    AssignClientAndInvestmentEntitiesResponse
  ] = {
    authEndpoint[
      AssignClientAndInvestmentEntitiesParams,
      GeneralServiceException,
      AssignClientAndInvestmentEntitiesResponse
    ](
      PathRelation / "assignClientsAndInvestmentEntities"
    )
  }

  val getContactRelation: BaseAuthenticatedEndpoint[
    GetContactRelationParams,
    GeneralServiceException,
    GetContactRelationResponse
  ] = {
    authEndpoint[
      GetContactRelationParams,
      GeneralServiceException,
      GetContactRelationResponse
    ](
      PathRelation / "getContactRelation"
    )
  }

  private val PathContactMatrix = Path / "contact-matrix"

  val getContactMatrix: BaseAuthenticatedEndpoint[
    GetContactMatrixParams,
    GeneralServiceException,
    GetContactMatrixResponse
  ] = {
    authEndpoint[
      GetContactMatrixParams,
      GeneralServiceException,
      GetContactMatrixResponse
    ](
      PathContactMatrix / "getContactMatrix"
    )
  }

  val updateContactMatrix: BaseAuthenticatedEndpoint[
    UpdateContactMatrixParams,
    GeneralServiceException,
    UpdateContactMatrixResponse
  ] = {
    authEndpoint[
      UpdateContactMatrixParams,
      GeneralServiceException,
      UpdateContactMatrixResponse
    ](
      PathContactMatrix / "updateContactMatrix"
    )
  }

  val getContactMatrixByInvestmentEntity: BaseAuthenticatedEndpoint[
    GetContactMatrixByInvestmentEntityParams,
    GeneralServiceException,
    GetContactMatrixByInvestmentEntityResponse
  ] = {
    authEndpoint[
      GetContactMatrixByInvestmentEntityParams,
      GeneralServiceException,
      GetContactMatrixByInvestmentEntityResponse
    ](
      Path / "getContactMatrixByInvestmentEntity"
    )
  }

  val exportContacts: BaseAuthenticatedEndpoint[
    ExportContactsParams,
    GeneralServiceException,
    ExportContactsResponse
  ] = {
    authEndpoint[
      ExportContactsParams,
      GeneralServiceException,
      ExportContactsResponse
    ](
      Path / "exportContacts"
    )
  }

}
