// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.endpoint.contact

import anduin.circe.generic.semiauto.CirceCodec
import anduin.funddata.endpoint.contact.FundDataContact.{
  ContactQueryParams,
  InvestmentEntityRelation,
  InvestmentEntityRelationUpdate
}
import anduin.funddata.endpoint.landingpage.LandingPageType
import anduin.funddata.endpoint.permission.{
  FundDataFirmValidationParams,
  FundDataInvestmentEntitiesValidationParams,
  FundDataInvestorsWithinFirmValidationParams
}
import anduin.funddata.endpoint.{SortOrder, UpdateProperty}
import anduin.id.communication.CommunicationTypeId
import anduin.id.contact.ContactId
import anduin.id.funddata.fund.FundLegalEntityId
import anduin.id.funddata.{FundDataFirmId, FundDataInvestmentEntityId, FundDataInvestorId}
import anduin.id.fundsub.FundSubId
import anduin.id.tag.TagItemId
import anduin.model.codec.MapCodecs.given
import anduin.model.common.user.UserInfo
import anduin.model.id.FileId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.utils.endpoint.{DashboardPaginationData, GetDashboardDataParams, GetDashboardDataResponse}

object FundDataContact {

  final case class ContactInfo(
    firstName: String,
    lastName: String,
    email: String,
    customId: String = "",
    phone: String = "",
    company: String = "",
    title: String = "",
    country: String = "",
    numberAndStreet: String = "",
    city: String = "",
    state: String = "",
    zipCode: String = ""
  ) derives CanEqual,
        CirceCodec.WithDefaultsAndTypeName { self =>
    val fullName: String = s"${firstName.trim} ${lastName.trim}".trim

    val address: String = {
      val address = s"${numberAndStreet.trim}, ${city.trim} ${state.trim} ${zipCode.trim}".trim
      if (address == ",") "" else address
    }

    def trimAll: ContactInfo =
      ContactInfo(
        firstName.trim,
        lastName.trim,
        email.trim,
        customId.trim,
        phone.trim,
        company.trim,
        title.trim,
        country.trim,
        numberAndStreet.trim,
        city.trim,
        state.trim,
        zipCode.trim
      )

  }

  object ContactInfo {

    def fromUserInfo(userInfo: UserInfo): ContactInfo = {
      ContactInfo(
        firstName = userInfo.firstName,
        lastName = userInfo.lastName,
        email = userInfo.emailAddressStr,
        phone = userInfo.phone,
        title = userInfo.jobTitle,
        country = userInfo.country,
        numberAndStreet = userInfo.address,
        city = userInfo.city,
        state = userInfo.state
      )
    }

  }

  final case class InvestmentEntityContactMatrixValueUpdate(
    fundLegalEntityId: FundLegalEntityId,
    communicationTypeId: CommunicationTypeId,
    communicationRoleUpdate: UpdateProperty[CommunicationRole] = UpdateProperty()
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class InvestmentEntityRelationWithMatrixUpdate(
    investmentEntityId: FundDataInvestmentEntityId,
    fundSubCommunicationTypeUpdate: UpdateProperty[FundSubCommunicationType] = UpdateProperty(),
    documentRequestCommunicationTypeUpdate: UpdateProperty[DocumentRequestCommunicationType] = UpdateProperty(),
    contactMatrixValueUpdates: UpdateProperty[List[InvestmentEntityContactMatrixValueUpdate]] = UpdateProperty()
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class ContactWithMatrixUpdate(
    contactInfo: ContactInfo,
    investmentEntityRelations: List[InvestmentEntityRelationWithMatrixUpdate]
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class Contact(
    contactId: ContactId,
    contactInfo: ContactInfo
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class InvestmentEntityContact(
    contactId: ContactId,
    contactInfo: ContactInfo,
    fundSubCommunicationType: FundSubCommunicationType = FundSubCommunicationType.Empty,
    documentRequestCommunicationType: DocumentRequestCommunicationType = DocumentRequestCommunicationType.NonReceiver
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual {
    def toContact: Contact = Contact(contactId, contactInfo)
  }

  final case class ContactQueryParams(
    searchText: String = "",
    filterByFundLegalEntities: Set[FundLegalEntityId] = Set.empty,
    filterByInvestmentEntities: Set[FundDataInvestmentEntityId] = Set.empty,
    filterByCommunicationTypes: Set[CommunicationTypeId] = Set.empty,
    filterByContactsOpt: Option[Set[ContactId]] = None,
    offset: Int = 0,
    limit: Int = 20,
    sortBy: ContactQueryParams.SortBy = ContactQueryParams.SortBy.Name,
    sortOrder: SortOrder = SortOrder.Ascending
  ) derives CirceCodec.WithDefaultsAndTypeName

  object ContactQueryParams {
    sealed trait SortBy derives CanEqual, CirceCodec.WithDefaultsAndTypeName

    object SortBy {
      case object Name extends SortBy derives CirceCodec.WithDefaultsAndTypeName

      case object Phone extends SortBy derives CirceCodec.WithDefaultsAndTypeName

      case object Company extends SortBy derives CirceCodec.WithDefaultsAndTypeName

      case object Title extends SortBy derives CirceCodec.WithDefaultsAndTypeName

      case object Address extends SortBy derives CirceCodec.WithDefaultsAndTypeName

    }

  }

  final case class InvestorInfo(investorId: FundDataInvestorId, name: String, customId: String)
      derives CirceCodec.WithDefaultsAndTypeName

  enum FundSubCommunicationType derives CirceCodec.Enum {
    case MainInvestor
    case Empty
  }

  enum DocumentRequestCommunicationType derives CirceCodec.Enum {
    case NonReceiver
    case Receiver
  }

  final case class InvestmentEntityInfo(
    investmentEntityId: FundDataInvestmentEntityId,
    name: String,
    customId: String,
    jurisdictionType: Option[TagItemId],
    investorType: Option[TagItemId],
    riskAssessment: Option[TagItemId]
  ) derives CirceCodec.WithDefaultsAndTypeName

  final case class ContactRelationInfo(
    investors: List[InvestorInfo],
    investmentEntities: List[InvestmentEntityInfo]
  ) derives CirceCodec.WithDefaultsAndTypeName

  final case class InvestmentEntityRelation(
    investmentEntityId: FundDataInvestmentEntityId,
    fundSubCommunicationType: FundSubCommunicationType = FundSubCommunicationType.Empty,
    documentRequestCommunicationType: DocumentRequestCommunicationType = DocumentRequestCommunicationType.NonReceiver
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class ContactRelation(
    contactId: ContactId,
    fundSubCommunicationType: FundSubCommunicationType = FundSubCommunicationType.Empty,
    documentRequestCommunicationType: DocumentRequestCommunicationType = DocumentRequestCommunicationType.NonReceiver
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class InvestmentEntityRelationUpdate(
    investmentEntityId: FundDataInvestmentEntityId,
    fundSubCommunicationTypeUpdate: UpdateProperty[FundSubCommunicationType],
    documentRequestCommunicationTypeUpdate: UpdateProperty[DocumentRequestCommunicationType]
  ) derives CirceCodec.WithDefaultsAndTypeName

  sealed trait ContactAccessInfo derives CirceCodec.WithDefaults

  final case class FundSubscriptionInfo(
    id: FundSubId,
    name: String,
    canAccess: Boolean
  ) extends ContactAccessInfo derives CirceCodec.WithDefaultsAndTypeName

  final case class DataRoomInfo(
    id: DataRoomWorkflowId,
    name: String,
    canAccess: Boolean
  ) extends ContactAccessInfo derives CirceCodec.WithDefaultsAndTypeName

  final case class LandingPageInfo(
    landingPageType: LandingPageType,
    pageName: String,
    customUrl: Option[String]
  ) extends ContactAccessInfo derives CirceCodec.WithDefaultsAndTypeName

  final case class PortalInfo(portalName: String) extends ContactAccessInfo derives CirceCodec.WithDefaultsAndTypeName

  final case class ContactAccess(
    contactEmail: String,
    fundSubscriptions: List[FundSubscriptionInfo],
    dataRooms: List[DataRoomInfo],
    landingPages: List[LandingPageInfo],
    portals: List[PortalInfo]
  ) derives CirceCodec.WithDefaultsAndTypeName {
    def isAllEmpty: Boolean = fundSubscriptions.isEmpty && dataRooms.isEmpty && landingPages.isEmpty && portals.isEmpty
  }

  final case class ContactAndRelationIds(
    contact: Contact,
    investmentEntityRelations: List[InvestmentEntityRelation],
    investorIds: List[FundDataInvestorId]
  ) derives CirceCodec.WithDefaultsAndTypeName {
    lazy val investmentEntityIds: List[FundDataInvestmentEntityId] = investmentEntityRelations.map(_.investmentEntityId)
  }

  enum CommunicationRole derives CirceCodec.Enum {
    case Primary
    case Secondary
  }

  final case class ContactMatrixKey(
    investmentEntityId: FundDataInvestmentEntityId,
    fundLegalEntityId: FundLegalEntityId,
    communicationTypeId: CommunicationTypeId
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class ContactMatrixValue(
    contactMatrixKey: ContactMatrixKey,
    communicationRole: CommunicationRole
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class ContactMatrixValueUpdate(
    contactMatrixKey: ContactMatrixKey,
    communicationRoleUpdate: UpdateProperty[CommunicationRole]
  ) derives CirceCodec.WithDefaultsAndTypeName

  final case class ContactMatrixKeyByInvestmentEntity(
    fundLegalEntityId: FundLegalEntityId,
    communicationTypeId: CommunicationTypeId,
    contactId: ContactId
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class ContactMatrixValueByInvestmentEntity(
    contactMatrixKey: ContactMatrixKeyByInvestmentEntity,
    communicationRole: CommunicationRole
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

}

final case class CreateContactParams(
  firmId: FundDataFirmId,
  createdContacts: List[FundDataContact.ContactInfo],
  clientIdsList: List[List[FundDataInvestorId]],
  investmentEntityIdsList: List[List[FundDataInvestmentEntityId]]
) extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class CreateContactResponse(contactIdMap: Map[String, ContactId]) derives CirceCodec.WithDefaultsAndTypeName

final case class CheckDuplicatedContactInfoParams(firmId: FundDataFirmId, emails: Set[String], customIds: Set[String])
    extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class CheckDuplicatedContactInfoResponse(duplicatedEmails: Set[String], duplicatedCustomIds: Set[String])
    derives CirceCodec.WithDefaultsAndTypeName

final case class GetContactParams(firmId: FundDataFirmId, contactId: ContactId) extends FundDataFirmValidationParams
    derives CirceCodec.WithDefaultsAndTypeName

final case class GetContactResponse(contact: FundDataContact.Contact) derives CirceCodec.WithDefaultsAndTypeName

final case class GetFirmContactsParams(
  firmId: FundDataFirmId,
  mode: GetFirmContactsParams.Mode = GetFirmContactsParams.Mode.AllContacts,
  queryParams: ContactQueryParams = ContactQueryParams()
) extends FundDataFirmValidationParams,
      GetDashboardDataParams[ContactId, FundDataContact.ContactAndRelationIds]
    derives CirceCodec.WithDefaultsAndTypeName

object GetFirmContactsParams {

  sealed trait Mode derives CanEqual, CirceCodec.WithDefaultsAndTypeName

  object Mode {
    case object AllContacts extends Mode
    case class SpecificInvestmentEntities(investmentEntityIds: Set[FundDataInvestmentEntityId]) extends Mode

    case class SpecificInvestmentEntitiesInFundLegalEntities(
      fundLegalEntityIds: Set[FundLegalEntityId],
      investmentEntityIds: Set[FundDataInvestmentEntityId],
      communicationTypeIds: Set[CommunicationTypeId]
    ) extends Mode

  }

}

final case class GetFirmContactsResponse(
  data: List[(ContactId, FundDataContact.ContactAndRelationIds)],
  filterKeys: Set[ContactId],
  pagination: DashboardPaginationData,
  contactRelations: FundDataContact.ContactRelationInfo,
  contactAccesses: List[FundDataContact.ContactAccess],
  contactCommunicationMatrix: Map[ContactId, List[FundDataContact.ContactMatrixValue]]
) extends GetDashboardDataResponse[ContactId, FundDataContact.ContactAndRelationIds]
    derives CirceCodec.WithDefaultsAndTypeName

final case class GetFirmContactsInfoParams(
  firmId: FundDataFirmId,
  filterByContacts: Option[Set[ContactId]] = None
) extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class GetFirmContactsInfoResponse(data: List[FundDataContact.Contact])
    derives CirceCodec.WithDefaultsAndTypeName

final case class GetInvestmentEntityContactsInfoParams(investmentEntityId: FundDataInvestmentEntityId)
    extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName {
  override def firmId: FundDataFirmId = investmentEntityId.firmId
}

final case class GetInvestmentEntityContactsInfoResponse(data: List[FundDataContact.InvestmentEntityContact])
    derives CirceCodec.WithDefaultsAndTypeName

final case class GetInvestmentEntitiesContactsInfoParams(
  investmentEntityIds: List[FundDataInvestmentEntityId]
) extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName {
  override def firmId: FundDataFirmId = investmentEntityIds.headOption.fold(FundDataFirmId.defaultValue.get)(_.firmId)
}

final case class GetInvestmentEntitiesContactsInfoResponse(
  data: Map[FundDataInvestmentEntityId, List[FundDataContact.InvestmentEntityContact]]
) derives CirceCodec.WithDefaultsAndTypeName

final case class EditContactParams(
  firmId: FundDataFirmId,
  contactId: ContactId,
  firstNameUpdate: UpdateProperty[String] = UpdateProperty(),
  lastNameUpdate: UpdateProperty[String] = UpdateProperty(),
  customIdUpdate: UpdateProperty[String] = UpdateProperty(),
  phoneUpdate: UpdateProperty[String] = UpdateProperty(),
  companyUpdate: UpdateProperty[String] = UpdateProperty(),
  titleUpdate: UpdateProperty[String] = UpdateProperty(),
  countryUpdate: UpdateProperty[String] = UpdateProperty(),
  numberAndStreetUpdate: UpdateProperty[String] = UpdateProperty(),
  cityUpdate: UpdateProperty[String] = UpdateProperty(),
  stateUpdate: UpdateProperty[String] = UpdateProperty(),
  zipCodeUpdate: UpdateProperty[String] = UpdateProperty()
) extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class EditContactResponse() derives CirceCodec.WithDefaultsAndTypeName

final case class DeleteContactParams(firmId: FundDataFirmId, contactIds: Set[ContactId])
    extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class DeleteContactResponse() derives CirceCodec.WithDefaultsAndTypeName

// Contact Access
final case class GetContactAccessParams(firmId: FundDataFirmId, contactId: ContactId)
    extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class GetContactAccessResponse(contactAccess: FundDataContact.ContactAccess)
    derives CirceCodec.WithDefaultsAndTypeName

// Contact IE/Clients Relations

final case class AssignClientAndInvestmentEntitiesParams(
  firmId: FundDataFirmId,
  contactId: ContactId,
  toAddClients: Set[FundDataInvestorId],
  toRemoveClients: Set[FundDataInvestorId],
  toAddInvestmentEntities: List[InvestmentEntityRelation],
  toUpdateInvestmentEntities: List[InvestmentEntityRelationUpdate],
  toRemoveInvestmentEntities: Set[FundDataInvestmentEntityId]
) extends FundDataFirmValidationParams
    with FundDataInvestorsWithinFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName {

  override def investorIds: List[FundDataInvestorId] =
    (toAddInvestmentEntities.map(_.investmentEntityId.parent) ++ toUpdateInvestmentEntities.map(
      _.investmentEntityId.parent
    ) ++ toRemoveInvestmentEntities.map(_.parent).toList).distinct

}

final case class AssignClientAndInvestmentEntitiesResponse() derives CirceCodec.WithDefaultsAndTypeName

final case class GetContactRelationParams(firmId: FundDataFirmId, contactId: ContactId)
    extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class GetContactRelationResponse(
  investmentEntityRelations: List[InvestmentEntityRelation],
  contactRelationInfos: FundDataContact.ContactRelationInfo
) derives CirceCodec.WithDefaultsAndTypeName

final case class GetInvestmentEntityRelationParams() derives CirceCodec.WithDefaultsAndTypeName

final case class GetInvestmentEntityRelationResponse() derives CirceCodec.WithDefaultsAndTypeName
// Communication Matrix

final case class GetContactMatrixParams(firmId: FundDataFirmId, contactId: ContactId)
    extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class GetContactMatrixResponse(
  contactMatrixValue: List[FundDataContact.ContactMatrixValue]
) derives CirceCodec.WithDefaultsAndTypeName

final case class UpdateContactMatrixParams(
  firmId: FundDataFirmId,
  contactId: ContactId,
  toAddInvestmentEntities: List[FundDataContact.InvestmentEntityRelation] = List.empty,
  toUpdateInvestmentEntities: List[FundDataContact.InvestmentEntityRelationUpdate] = List.empty,
  toRemoveInvestmentEntities: Set[FundDataInvestmentEntityId] = Set.empty,
  toAddMatrixValues: List[FundDataContact.ContactMatrixValue] = List.empty,
  toUpdateMatrixValues: List[FundDataContact.ContactMatrixValueUpdate] = List.empty,
  toRemoveMatrixValues: Set[FundDataContact.ContactMatrixKey] = Set.empty
) extends FundDataFirmValidationParams
    with FundDataInvestorsWithinFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName {

  override def investorIds: List[FundDataInvestorId] =
    (toAddInvestmentEntities.map(_.investmentEntityId.parent) ++ toUpdateInvestmentEntities.map(
      _.investmentEntityId.parent
    ) ++ toRemoveInvestmentEntities.map(_.parent).toList).distinct

}

final case class UpdateContactMatrixResponse() derives CirceCodec.WithDefaultsAndTypeName

final case class GetContactMatrixByInvestmentEntityParams(investmentEntityId: FundDataInvestmentEntityId)
    extends FundDataFirmValidationParams
    with FundDataInvestmentEntitiesValidationParams derives CirceCodec.WithDefaultsAndTypeName {
  lazy val firmId: FundDataFirmId = investmentEntityId.firmId
  lazy val investmentEntityIds: List[FundDataInvestmentEntityId] = List(investmentEntityId)
}

final case class GetContactMatrixByInvestmentEntityResponse(
  contactMatrixValues: List[FundDataContact.ContactMatrixValueByInvestmentEntity]
) derives CirceCodec.WithDefaultsAndTypeName

final case class ExportContactsParams(firmId: FundDataFirmId, contactIds: Set[ContactId])
    extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class ExportContactsResponse(fileId: FileId) derives CirceCodec.WithDefaultsAndTypeName
