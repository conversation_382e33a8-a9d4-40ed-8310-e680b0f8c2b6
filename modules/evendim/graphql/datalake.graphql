#######################
# Input Schema
#######################

"""The umbrella under which the additional elements and objects for fund will be housed. You may have one or more FundSubscriptions on the platform"""
type FundSubscription @auth(query: {or:[{rule:"{$role: { eq: \"ADMIN\" } }"},{rule:"query($userId: String!) {\n    queryFundSubscription {\n    admins(filter: {id: {eq: $userId}}) {\n    id\n    }\n    }\n    }"}]}, add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {aggregate:false}) {
	"""A system generated value provided at the time the FundSubscription is created."""
	id: String! @id
	"""A custom ID that references a fund."""
	customFundId: String
	"""The name of the Fund."""
	name: String
	"""Users in the FundSubscription who have the role of admin."""
	admins(filter: UserFilter, order: UserOrder, first: Int, offset: Int): [User!]!
	"""The currency in which the fund and corresponding order will be transacted in."""
	currency: String!
	"""The planned completion of the fund raise by which investors must have all documentation completed."""
	closes(filter: CloseFilter, order: CloseOrder, first: Int, offset: Int): [Close!]! @hasInverse(field: fund)
	"""Supplemental textual elements. This can be used as a filtering category in advanced dashboard."""
	tags(filter: TagFilter, order: TagOrder, first: Int, offset: Int): [Tag!]! @hasInverse(field: fund)
	"""The available templates for this fund."""
	templates(filter: TemplateFilter, order: TemplateOrder, first: Int, offset: Int): [Template!]!
	"""the uploaded documents associated with the fundSubscription."""
	referenceDocs(filter: FileFilter, order: FileOrder, first: Int, offset: Int): [File!]!
	"""The sub-funds of the fund"""
	subFunds(filter: SubFundFilter, order: SubFundOrder, first: Int, offset: Int): [SubFund!] @hasInverse(field: masterFund)
	"""The orders contained within the fund"""
	orders(filter: OrderFilter, order: OrderOrder, first: Int, offset: Int): [Order]! @hasInverse(field: fund)
	supportingDocReviewEnabled: Boolean
	"""A system generated timestamp upon the creation of the fund."""
	createdAt: DateTime
	"""WIP"""
	inactiveNotificationEnabled: Boolean!
	"""WIP"""
	inactiveNotificationDuration: Int!
	"""A timestamp of the last update to the fund."""
	lastUpdatedAt: DateTime
	"""The investor groups of the fund."""
	investorGroups(filter: InvestorGroupFilter, order: InvestorGroupOrder, first: Int, offset: Int): [InvestorGroup!]! @hasInverse(field: fund)
	"""The advisor entities linked to the fund."""
	advisorEntities(filter: AdvisorEntityFilter, order: AdvisorEntityOrder, first: Int, offset: Int): [AdvisorEntity!]! @hasInverse(field: fund)
	"""Advisor tags for categorizing orders by advisor characteristics."""
	advisorTags(filter: AdvisorTagFilter, order: AdvisorTagOrder, first: Int, offset: Int): [AdvisorTag!]! @hasInverse(field: fund)
	adminsAggregate(filter: UserFilter): UserAggregateResult
	closesAggregate(filter: CloseFilter): CloseAggregateResult
	tagsAggregate(filter: TagFilter): TagAggregateResult
	templatesAggregate(filter: TemplateFilter): TemplateAggregateResult
	referenceDocsAggregate(filter: FileFilter): FileAggregateResult
	subFundsAggregate(filter: SubFundFilter): SubFundAggregateResult
	ordersAggregate(filter: OrderFilter): OrderAggregateResult
	investorGroupsAggregate(filter: InvestorGroupFilter): InvestorGroupAggregateResult
	advisorEntitiesAggregate(filter: AdvisorEntityFilter): AdvisorEntityAggregateResult
	advisorTagsAggregate(filter: AdvisorTagFilter): AdvisorTagAggregateResult
}

"""A single subscription for a single investment entity investment entity. An investment entity or user may be involved with multiple orders."""
type Order @auth(query: {or:[{rule:"{$role: { eq: \"ADMIN\" } }"},{rule:"query($userId: String!) {\n    queryOrder {\n    fund {\n    admins(filter: {id: {eq: $userId}}) {\n    id\n    }\n    }\n    }\n    }"}]}, add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {aggregate:false}) {
	"""A system generated value provided at the time the Order is created."""
	id: String! @id
	"""A customID that may be injected at the time of creation of the order.  Typically a value associated the investment entity sourced from the customer CRM or other customer internal repositories."""
	customId: String @search(by: [hash])
	"""The associated FundSubscription."""
	fund(filter: FundSubscriptionFilter): FundSubscription @hasInverse(field: orders)
	"""Indicates the type of subscription, either a normal or offline subscription, where offline would indicate documents have been completed external to the platform."""
	orderType: OrderType!
	"""An user associated as the "primary" or "owner" of a given investment entity."""
	mainContact(filter: UserFilter): User! @dgraph(pred: "Order.mainLp")
	"""an array of the User(s) associated with the given order."""
	contacts(filter: UserFilter, order: UserOrder, first: Int, offset: Int): [User!]! @dgraph(pred: "Order.collaborators")
	"""The users who have not yet accepted their invitation to the platform."""
	pendingContacts(filter: UserFilter, order: UserOrder, first: Int, offset: Int): [User!]! @dgraph(pred: "Order.pendingMembers")
	"""The name of the individual or entity who will own the given security."""
	investmentEntity: String!
	"""A system generated timestamp upon the creation of the order."""
	createdAt: DateTime
	"""A timestamp of the last update to the order."""
	lastUpdatedAt: DateTime
	"""WIP"""
	lastActivityAt: DateTime
	"""A timestamp of the last reminder sent to user(s)."""
	lastReminderSentAt: DateTime
	"""A timestamp of the last re-invitation sent to user(s)."""
	lastReinvitationSentAt: DateTime
	"""A timestamp of when the order was submitted."""
	submissionDate: DateTime
	"""WIP"""
	seenByUsers(filter: UserFilter, order: UserOrder, first: Int, offset: Int): [User!]!
	"""An array of tags associated with the order."""
	tags(filter: TagFilter, order: TagOrder, first: Int, offset: Int): [Tag!]! @hasInverse(field: orders)
	"""The close to which the order is associated."""
	close(filter: CloseFilter): Close
	"""The status of the order."""
	status: OrderStatus! @search
	"""An optional pre-emptive value provided by the fund based upon previous conversations or expressed interest amount from the data room or other means."""
	estimatedCommitmentAmount: Float @dgraph(pred: "Order.expectedCommitment")
	"""The commitment amount identified in the submitted subscription document."""
	submittedCommitmentAmount: Float @dgraph(pred: "Order.submittedCommitment")
	"""The commitment amount accepted by the Fund post review."""
	acceptedCommitmentAmount: Float @dgraph(pred: "Order.acceptedCommitment")
	"""The array of commitment amounts of this order. A order can have multiple commitment amount if the fund contain sub funds."""
	commitmentAmounts(filter: SubFundCommitmentAmountFilter, order: SubFundCommitmentAmountOrder, first: Int, offset: Int): [SubFundCommitmentAmount!]
	"""The array of reference documents provided by either the MainLp or collaborators."""
	referenceDocs(filter: FileFilter, order: FileOrder, first: Int, offset: Int): [File!]!
	"""Custom elements added to the fund side dashboard that may provide supplemental data about a given order."""
	customData(filter: CustomDataFilter, order: CustomDataOrder, first: Int, offset: Int): [CustomData!]! @hasInverse(field: order)
	"""The values of some form questions set up to be tracked on the fund side dashboard."""
	formValues(filter: FormQuestionDataFilter, order: FormQuestionDataOrder, first: Int, offset: Int): [FormQuestionData!]!
	"""The completion progress of the investor based upon which fields in the subscription form are required."""
	formFillingProgress: Float!
	"""Required fields from the subscription form that have not yet been completed."""
	missingRequiredFields: Int!
	"""Recommended fields from the subscription form that have not yet been completed."""
	missingRecommendedFields: Int!
	"""The array of supporting documents for the Subscription."""
	supportingDocs(filter: SupportingDocumentFilter, order: SupportingDocumentOrder, first: Int, offset: Int): [SupportingDocument!]! @hasInverse(field: order) @dgraph(pred: "Order.requiredDocs")
	"""WIP"""
	supportingDocSignatureRequests(filter: SignatureRequestFilter, order: SignatureRequestOrder, first: Int, offset: Int): [SignatureRequest!]!
	"""The versions of completed subscription form that have been signed and submitted."""
	submittedDocs(filter: OrderDocumentFilter, first: Int, offset: Int): [OrderDocument!]!
	"""Documents that have been countersigned and uploaded to the platform but have NOT YET distributed to the investors."""
	uploadedCountersignedDocs(filter: OrderDocumentFilter, first: Int, offset: Int): [OrderDocument!]!
	"""Countersigned documents that have been distributed to the investors side."""
	countersignedDocs(filter: OrderDocumentFilter, first: Int, offset: Int): [OrderDocument!]!
	"""WIP"""
	countersignSignatureRequests(filter: SignatureRequestFilter, order: SignatureRequestOrder, first: Int, offset: Int): [SignatureRequest!]!
	"""Users whose email addresses returned undeliverable from invitations."""
	emailBouncedContacts(filter: UserFilter, order: UserOrder, first: Int, offset: Int): [User!]! @dgraph(pred: "Order.emailBouncedUsers")
	amlKycReviews(filter: AmlKycReviewFilter, order: AmlKycReviewOrder, first: Int, offset: Int): [AmlKycReview!]! @hasInverse(field: order)
	unsignedSubscriptionReview(filter: SubscriptionDocReviewInfoFilter): SubscriptionDocReviewInfo
	signedSubscriptionReview(filter: SubscriptionDocReviewInfoFilter): SubscriptionDocReviewInfo
	"""AML/KYC documents that this investor provided offline"""
	amlKycDocsProvidedOffline: [String!]
	"""Metadata attached to this order"""
	metadata(filter: MetadataFieldFilter, order: MetadataFieldOrder, first: Int, offset: Int): [MetadataField!]
	"""The clients to which the order is associated (only with fund integrated with Investor Data Management)"""
	clients(filter: ClientFilter, order: ClientOrder, first: Int, offset: Int): [Client!]
	"""The data extraction request of the submitted documents (feature is in development)"""
	subDocDataExtractionRequest(filter: DataExtractionRequestFilter): DataExtractionRequest
	"""Aml checks for investor and related personas"""
	amlCheck(filter: AmlCheckFilter, order: AmlCheckOrder, first: Int, offset: Int): [AmlCheck!]
	"""Investor wet signed the subscription doc and uploaded the documents."""
	investorSignedOnPaper: Boolean
	"""The master profile comparison result when an order is associated with a client (not for public API usage)"""
	clientFormCompareData(filter: ClientFormCompareDataFilter, order: ClientFormCompareDataOrder, first: Int, offset: Int): [ClientFormCompareData!]
	"""The investor group associated with each order"""
	investorGroup(filter: InvestorGroupFilter): InvestorGroup
	"""The advisor entity associated with each order"""
	advisorEntity(filter: AdvisorEntityFilter): AdvisorEntity
	"""The status of the side letter (feature is in development)"""
	sideLetterStatus: SideLetterStatus
	contactsAggregate(filter: UserFilter): UserAggregateResult
	pendingContactsAggregate(filter: UserFilter): UserAggregateResult
	seenByUsersAggregate(filter: UserFilter): UserAggregateResult
	tagsAggregate(filter: TagFilter): TagAggregateResult
	commitmentAmountsAggregate(filter: SubFundCommitmentAmountFilter): SubFundCommitmentAmountAggregateResult
	referenceDocsAggregate(filter: FileFilter): FileAggregateResult
	customDataAggregate(filter: CustomDataFilter): CustomDataAggregateResult
	formValuesAggregate(filter: FormQuestionDataFilter): FormQuestionDataAggregateResult
	supportingDocsAggregate(filter: SupportingDocumentFilter): SupportingDocumentAggregateResult
	supportingDocSignatureRequestsAggregate(filter: SignatureRequestFilter): SignatureRequestAggregateResult
	submittedDocsAggregate(filter: OrderDocumentFilter): OrderDocumentAggregateResult
	uploadedCountersignedDocsAggregate(filter: OrderDocumentFilter): OrderDocumentAggregateResult
	countersignedDocsAggregate(filter: OrderDocumentFilter): OrderDocumentAggregateResult
	countersignSignatureRequestsAggregate(filter: SignatureRequestFilter): SignatureRequestAggregateResult
	emailBouncedContactsAggregate(filter: UserFilter): UserAggregateResult
	amlKycReviewsAggregate(filter: AmlKycReviewFilter): AmlKycReviewAggregateResult
	metadataAggregate(filter: MetadataFieldFilter): MetadataFieldAggregateResult
	clientsAggregate(filter: ClientFilter): ClientAggregateResult
	amlCheckAggregate(filter: AmlCheckFilter): AmlCheckAggregateResult
	clientFormCompareDataAggregate(filter: ClientFormCompareDataFilter): ClientFormCompareDataAggregateResult
}

"""Stores the data about a particular user of the platform."""
type User @auth(add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {query:false,aggregate:false}) {
	"""A system generated value provided at the time the user is created."""
	id: String! @id
	"""The email address associated with a given user profile"""
	email: String! @id
	"""The first name of the user on the profile"""
	firstName: String!
	"""The first name of the user on the profile"""
	lastName: String!
}

"""Determine whether the order contacts have been invited."""
enum OrderType {
	NORMAL
	OFFLINE
}

"""A textual descriptive element often used to enrich the dashboards or workflow by providing supplemental information."""
type Tag @auth(query: {or:[{rule:"{$role: { eq: \"ADMIN\" } }"},{rule:"query($userId: String!) {\n    queryTag {\n    fund {\n    admins(filter: {id: {eq: $userId}}) {\n    id\n    }\n    }\n    }\n    }"}]}, add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {query:true,aggregate:false}) {
	"""A system generated value provided at the time the Tag is created."""
	id: String! @id
	"""The FundSubscription to which the Tag is associated."""
	fund(filter: FundSubscriptionFilter): FundSubscription @hasInverse(field: tags)
	"""The array of orders where this Tag has been applied."""
	orders(filter: OrderFilter, order: OrderOrder, first: Int, offset: Int): [Order]! @hasInverse(field: tags)
	"""The label of the Tag applied upon creation. This is the value actually illustrated on the platform."""
	name: String! @search(by: [hash])
	"""The user who created the Tag."""
	creator(filter: UserFilter): User
	"""The time at which the Tag was created."""
	createdAt: DateTime
	ordersAggregate(filter: OrderFilter): OrderAggregateResult
}

"""Houses the information pertaining to the planned close(s) of the fund subscription."""
type Close @auth(add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {query:false,aggregate:false}) {
	"""A system generated value provided at the time the Close is created."""
	id: String! @id
	"""The FundSubscription associated with a given close."""
	fund(filter: FundSubscriptionFilter): FundSubscription @hasInverse(field: closes)
	"""The descriptive name of the close provide upon creating the Close."""
	name: String! @search(by: [hash])
	"""A custom id assigned to the Close upon creating the Close."""
	customCloseId: String
	"""The planned completion of the Close assigned upon creation of the Close."""
	targetDate: DateTime
}

"""Used to represent possible statuses of an order."""
enum OrderStatus {
	"""The contacts for the order have been invited though they have not started editing the form."""
	NOT_STARTED
	"""The form is being edited."""
	IN_PROGRESS
	"""The form is being edited after the GP request to update it."""
	CHANGE_IN_PROGRESS
	"""The unsigned form is waiting to be reviewed by the fund."""
	PENDING_UNSIGNED_REVIEW
	"""Deprecated status."""
	FILLED_FORM
	"""The form is waiting to be signed."""
	REQUESTED_SIGNATURE
	"""Deprecated status."""
	SIGNED_FORM
	"""The form is waiting to be submitted."""
	PENDING_SUBMISSION
	"""The signed form is waiting to be approved by the fund."""
	PENDING_REVIEW
	"""The signed form is approved by the fund"""
	FORM_REVIEWED
	"""The signed form has been submitted to the fund by the investor."""
	SUBMITTED
	"""The form has been countersigned but not yet shared with the order."""
	COUNTERSIGNED
	"""The countersigned form has been shared with order."""
	COMPLETED
	"""The order has been removed from the fund subscription."""
	REMOVED
}

"""Containing the supporting documents for a Subscription."""
type SupportingDocument @dgraph(type: "RequiredDoc") @auth(add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {get:true,query:false,aggregate:false}, mutation: {add:true,update:true,delete:true}) {
	"""A system generated value provided at the time the Document is indicated as required."""
	id: ID!
	"""The order to which the document has been associated."""
	order(filter: OrderFilter): Order @hasInverse(field: supportingDocs)
	"""The name of the document requested per the corresponding FormQuestionData."""
	name: String!
	"""An indicator field, indicating the relevant of a given document requirement.  Marking the document as NA, removes it from the requirement list for a given order."""
	markedAsNa: Boolean!
	"""A flag to quickly check it this document is submitted i.e. submittedDocs is non-empty."""
	submitted: Boolean!
	"""The array of submitted documents."""
	submittedDocs(filter: OrderDocumentFilter, first: Int, offset: Int): [OrderDocument!]!
	submittedDocsAggregate(filter: OrderDocumentFilter): OrderDocumentAggregateResult
}

type File @auth(add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {get:true,query:false,aggregate:false}, mutation: {add:true,update:true,delete:true}) {
	"""A system generated value provided at the time the File is created."""
	id: String! @id
	"""The name of the file that has been created."""
	name: String!
	"""File uploader"""
	uploader(filter: UserFilter): User
	"""Upload time"""
	uploadedAt: DateTime
}

enum SignatureRequestStatus {
	SENT
	COMPLETED
	CANCELLED
}

type SignatureRequest @auth(add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {get:true,query:false,aggregate:false}, mutation: {add:true,update:true,delete:true}) {
	id: String! @id
	status: SignatureRequestStatus!
	requesterOpt(filter: UserFilter): User
	requestedAtOpt: DateTime
}

type CustomData @auth(add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {get:false,query:false,aggregate:false}, mutation: {add:true,update:true,delete:true}) {
	id: ID!
	order(filter: OrderFilter): Order @hasInverse(field: customData)
	dataColumnId: String! @search(by: [hash])
	columnName: String
	customValue(filter: CustomValueObjFilter): CustomValueObj!
	lastEditedByOpt(filter: UserFilter): User
	lastEditedAtOpt: DateTime
}

type FormQuestionData @auth(add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {get:false,query:false,aggregate:false}, mutation: {add:true,update:true,delete:true}) {
	id: ID!
	namespace: String! @search(by: [exact])
	alias: String! @search(by: [exact])
	value: String!
}

enum FundSubDocType {
	"""Main subscription document has been completed by investors but not yet signed."""
	FilledForm
	"""Main subscription document has been completed and signed by investors."""
	LpSignedDoc
	"""Anduin signature certificate for e-signed documents."""
	SigningCertificate
	"""Deprecated doc type."""
	TaxForm
	"""Form has been countersigned by member of the fund side."""
	CountersignedDoc
	"""Supporting documents submitted by investors."""
	SupportingDoc
	"""Other type of documents."""
	Others
}

enum DocType {
	"""Main subscription document has been completed by investors but not yet signed."""
	FilledForm
	"""Main subscription document has been completed and signed by investors."""
	InvestorSignedDoc
	"""Anduin signature certificate for e-signed documents."""
	SigningCertificate
	"""Form has been countersigned by member of the fund side."""
	CountersignedDoc
	"""Supporting documents submitted by investors."""
	SupportingDoc
	"""Other type of documents."""
	Others
}

"""Contains a file with its doc type (in a context of a Subscription)."""
type OrderDocument @dgraph(type: "LpDocument") @auth(query: {or:[{rule:"{$role: { eq: \"ADMIN\" } }"},{rule:"query($userId: String!) {\n    queryOrderDocument {\n    order {\n    fund {\n    admins(filter: {id: {eq: $userId}}) {\n    id\n    }\n    }\n    }\n    }\n    }"}]}, add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {get:true,query:true,aggregate:false}, mutation: {add:true,update:true,delete:true}) {
	"""A system generated value provided at the time the OrderDocument is created."""
	id: ID!
	"""The file for which the LpDocument is associated."""
	file(filter: FileFilter): File
	"""The type of document."""
	docType: FundSubDocType
	"""The type of document."""
	documentType: DocType
	"""The order this document belongs to."""
	order(filter: OrderFilter): Order
}

enum TemplateType {
	IMPORT
	EXPORT
}

"""Template for import/export form data."""
type Template @generate(query: {get:true,query:false,aggregate:false}) {
	id: String! @id
	"""Name of the template."""
	name: String!
	"""Description about the last update."""
	templateType: TemplateType
	"""Description about the last update."""
	lastChangeDescription: String!
	"""The sample spreadsheet file."""
	spreadsheetFile(filter: FileFilter): File
	"""List of columns of the spreadsheet."""
	columns(filter: TemplateColumnFilter, order: TemplateColumnOrder, first: Int, offset: Int): [TemplateColumn!]!
	"""The user who created the template."""
	createdBy(filter: UserFilter): User
	"""A system generated timestamp upon the creation of the template."""
	createdAt: DateTime
	"""A timestamp of the last update to the template."""
	lastUpdatedAt: DateTime
	columnsAggregate(filter: TemplateColumnFilter): TemplateColumnAggregateResult
}

"""Template column"""
type TemplateColumn @generate(query: {get:false,query:false,aggregate:false}) {
	id: ID!
	"""Order of this column in the spreadsheet"""
	index: Int!
	"""The column title."""
	title: String!
}

type DateTimeObj @generate(query: {get:false,query:false,aggregate:false}, mutation: {add:false,update:false,delete:false}) {
	datetimeValue: DateTime!
}

type CurrencyObj @generate(query: {get:false,query:false,aggregate:false}, mutation: {add:false,update:false,delete:false}) {
	currency: String!
	amount: Float!
}

type SingleValueObj @generate(query: {get:false,query:false,aggregate:false}, mutation: {add:false,update:false,delete:false}) {
	singleValue: String!
}

type MultipleValueObj @generate(query: {get:false,query:false,aggregate:false}, mutation: {add:false,update:false,delete:false}) {
	multipleValue: [String]!
}

type StringObj @generate(query: {get:false,query:false,aggregate:false}, mutation: {add:false,update:false,delete:false}) {
	stringValue: String!
}

type ChecklistObj @generate(query: {get:false,query:false,aggregate:false}, mutation: {add:false,update:false,delete:false}) {
	checkedOptions: [String]!
}

union CustomValueUnion = DateTimeObj | CurrencyObj | SingleValueObj | MultipleValueObj | StringObj | ChecklistObj

type CustomValueObj @generate(query: {get:false,query:false,aggregate:false}, mutation: {add:false,update:false,delete:false}) {
	customValue(filter: CustomValueUnionFilter): CustomValueUnion
}

enum ReviewStatus {
	NOT_STARTED
	PENDING_REVIEW
	CHANGES_REQUESTED
	APPROVED
}

type AmlKycReview @auth(add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {get:true,query:false,aggregate:false}, mutation: {add:true,update:true,delete:true}) {
	id: ID!
	docName: String!
	order(filter: OrderFilter): Order @hasInverse(field: amlKycReviews)
	status: ReviewStatus
	updatedBy(filter: UserFilter): User
	updatedAt: DateTime
}

type SubscriptionDocReviewInfo @auth(add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {query:false,aggregate:false}) {
	id: String! @id
	versionIndex: Int!
	status: ReviewStatus
	"""For internal use only!"""
	currentPendingStepId: String
}

type MetadataField @auth(add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {query:false,aggregate:false}) {
	id: ID!
	fieldName: String
	value: String
}

type Client @auth(query: {rule:"{$role: { eq: \"ADMIN\" } }"}, add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {query:false,aggregate:false}) {
	"""A system generated value provided at the time the Client is created."""
	id: String! @id
	name: String!
	customId: String
}

enum DataExtractionRequestStatus {
	IN_PROGRESS
	READY_FOR_REVIEW
	APPROVED
}

type DataExtractionRequest @auth(query: {rule:"{$role: { eq: \"ADMIN\" } }"}, add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {query:false,aggregate:false}, mutation: {delete:true}) {
	id: String! @id
	status: DataExtractionRequestStatus!
	missingRequiredFields: Int!
	missingRecommendedFields: Int!
}

enum AmlCheckStatus {
	NOT_STARTED
	RESULT_AVAILABLE
}

type AmlCheck @auth(query: {rule:"{$role: { eq: \"ADMIN\" } }"}, add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {query:false,aggregate:false}) {
	id: String! @id
	status: AmlCheckStatus!
}

type SubFund @auth(query: {or:[{rule:"{$role: { eq: \"ADMIN\" } }"},{rule:"query($userId: String!) {\n    querySubFund {\n    masterFund {\n    admins(filter: {id: {eq: $userId}}) {\n    id\n    }\n    }\n    }\n    }"}]}, add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {query:true,aggregate:false}) {
	id: String! @id
	customId: String
	masterFund(filter: FundSubscriptionFilter): FundSubscription @hasInverse(field: subFunds)
	currency: String
	name: String
}

type SubFundCommitmentAmount @auth(add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {query:false,aggregate:false}) {
	id: ID!
	subFund(filter: SubFundFilter): SubFund
	"""An optional pre-emptive value provided by the fund based upon previous conversations or expressed interest amount from the data room or other means."""
	estimatedCommitmentAmount: Float
	"""The commitment amount identified in the submitted subscription document."""
	submittedCommitmentAmount: Float
	"""The commitment amount accepted by the Fund post review."""
	acceptedCommitmentAmount: Float
}

type ClientFormCompareData @auth(query: {rule:"{$role: { eq: \"ADMIN\" } }"}, add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {query:false,aggregate:false}) {
	id: ID!
	clientId: String!
	numOfDifferences: Int!
	comparedAt: DateTime
}

type InvestorGroup @auth(add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {query:false,aggregate:false}) {
	id: String! @id
	name: String! @search(by: [hash])
	fund(filter: FundSubscriptionFilter): FundSubscription @hasInverse(field: investorGroups)
}

type AdvisorEntity @auth(add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {query:false,aggregate:false}) {
	id: String! @id
	name: String! @search(by: [hash])
	fund(filter: FundSubscriptionFilter): FundSubscription @hasInverse(field: advisorEntities)
}

"""A textual descriptive element used to categorize orders by advisor characteristics."""
type AdvisorTag @auth(add: {rule:"{$role: { eq: \"ADMIN\" } }"}, delete: {rule:"{$role: { eq: \"ADMIN\" } }"}, update: {rule:"{$role: { eq: \"ADMIN\" } }"}) @generate(query: {query:false,aggregate:false}) {
	"""A system generated value provided at the time the AdvisorTag is created."""
	id: String! @id
	"""The FundSubscription to which the AdvisorTag is associated."""
	fund(filter: FundSubscriptionFilter): FundSubscription @hasInverse(field: advisorTags)
	"""The label of the AdvisorTag applied upon creation. This is the value actually illustrated on the platform."""
	name: String! @search(by: [hash])
	"""The user who created the AdvisorTag."""
	creator(filter: UserFilter): User
	"""The time at which the AdvisorTag was created."""
	createdAt: DateTime
}

enum SideLetterStatus {
	NOT_STARTED
	IN_NEGOTIATION
	AGREED
	PENDING_SIGNATURE
	SIGNED
	COMPLETED
}

#######################
# Extended Definitions
#######################

"""
The Int64 scalar type represents a signed 64‐bit numeric non‐fractional value.
Int64 can represent values in range [-(2^63),(2^63 - 1)].
"""
scalar Int64

"""
The DateTime scalar type represents date and time as a string in RFC3339 format.
For example: "1985-04-12T23:20:50.52Z" represents 20 mins 50.52 secs after the 23rd hour of Apr 12th 1985 in UTC.
"""
scalar DateTime

input IntRange{
	min: Int!
	max: Int!
}

input FloatRange{
	min: Float!
	max: Float!
}

input Int64Range{
	min: Int64!
	max: Int64!
}

input DateTimeRange{
	min: DateTime!
	max: DateTime!
}

input StringRange{
	min: String!
	max: String!
}

enum DgraphIndex {
	int
	int64
	float
	bool
	hash
	exact
	term
	fulltext
	trigram
	regexp
	year
	month
	day
	hour
	geo
}

input AuthRule {
	and: [AuthRule]
	or: [AuthRule]
	not: AuthRule
	rule: String
}

enum HTTPMethod {
	GET
	POST
	PUT
	PATCH
	DELETE
}

enum Mode {
	BATCH
	SINGLE
}

input CustomHTTP {
	url: String!
	method: HTTPMethod!
	body: String
	graphql: String
	mode: Mode
	forwardHeaders: [String!]
	secretHeaders: [String!]
	introspectionHeaders: [String!]
	skipIntrospection: Boolean
}

type Point {
	longitude: Float!
	latitude: Float!
}

input PointRef {
	longitude: Float!
	latitude: Float!
}

input NearFilter {
	distance: Float!
	coordinate: PointRef!
}

input PointGeoFilter {
	near: NearFilter
	within: WithinFilter
}

type PointList {
	points: [Point!]!
}

input PointListRef {
	points: [PointRef!]!
}

type Polygon {
	coordinates: [PointList!]!
}

input PolygonRef {
	coordinates: [PointListRef!]!
}

type MultiPolygon {
	polygons: [Polygon!]!
}

input MultiPolygonRef {
	polygons: [PolygonRef!]!
}

input WithinFilter {
	polygon: PolygonRef!
}

input ContainsFilter {
	point: PointRef
	polygon: PolygonRef
}

input IntersectsFilter {
	polygon: PolygonRef
	multiPolygon: MultiPolygonRef
}

input PolygonGeoFilter {
	near: NearFilter
	within: WithinFilter
	contains: ContainsFilter
	intersects: IntersectsFilter
}

input GenerateQueryParams {
	get: Boolean
	query: Boolean
	password: Boolean
	aggregate: Boolean
}

input GenerateMutationParams {
	add: Boolean
	update: Boolean
	delete: Boolean
}

directive @hasInverse(field: String!) on FIELD_DEFINITION
directive @search(by: [DgraphIndex!]) on FIELD_DEFINITION
directive @dgraph(type: String, pred: String) on OBJECT | INTERFACE | FIELD_DEFINITION
directive @id on FIELD_DEFINITION
directive @withSubscription on OBJECT | INTERFACE | FIELD_DEFINITION
directive @secret(field: String!, pred: String) on OBJECT | INTERFACE
directive @auth(
	password: AuthRule
	query: AuthRule,
	add: AuthRule,
	update: AuthRule,
	delete: AuthRule) on OBJECT | INTERFACE
directive @custom(http: CustomHTTP, dql: String) on FIELD_DEFINITION
directive @remote on OBJECT | INTERFACE | UNION | INPUT_OBJECT | ENUM
directive @remoteResponse(name: String) on FIELD_DEFINITION
directive @cascade(fields: [String]) on FIELD
directive @lambda on FIELD_DEFINITION
directive @lambdaOnMutate(add: Boolean, update: Boolean, delete: Boolean) on OBJECT | INTERFACE
directive @cacheControl(maxAge: Int!) on QUERY
directive @generate(
	query: GenerateQueryParams,
	mutation: GenerateMutationParams,
	subscription: Boolean) on OBJECT | INTERFACE

input IntFilter {
	eq: Int
	in: [Int]
	le: Int
	lt: Int
	ge: Int
	gt: Int
	between: IntRange
}

input Int64Filter {
	eq: Int64
	in: [Int64]
	le: Int64
	lt: Int64
	ge: Int64
	gt: Int64
	between: Int64Range
}

input FloatFilter {
	eq: Float
	in: [Float]
	le: Float
	lt: Float
	ge: Float
	gt: Float
	between: FloatRange
}

input DateTimeFilter {
	eq: DateTime
	in: [DateTime]
	le: DateTime
	lt: DateTime
	ge: DateTime
	gt: DateTime
	between: DateTimeRange
}

input StringTermFilter {
	allofterms: String
	anyofterms: String
}

input StringRegExpFilter {
	regexp: String
}

input StringFullTextFilter {
	alloftext: String
	anyoftext: String
}

input StringExactFilter {
	eq: String
	in: [String]
	le: String
	lt: String
	ge: String
	gt: String
	between: StringRange
}

input StringHashFilter {
	eq: String
	in: [String]
}

#######################
# Generated Types
#######################

type AddAdvisorEntityPayload {
	advisorEntity(filter: AdvisorEntityFilter, order: AdvisorEntityOrder, first: Int, offset: Int): [AdvisorEntity]
	numUids: Int
}

type AddAdvisorTagPayload {
	advisorTag(filter: AdvisorTagFilter, order: AdvisorTagOrder, first: Int, offset: Int): [AdvisorTag]
	numUids: Int
}

type AddAmlCheckPayload {
	amlCheck(filter: AmlCheckFilter, order: AmlCheckOrder, first: Int, offset: Int): [AmlCheck]
	numUids: Int
}

type AddAmlKycReviewPayload {
	amlKycReview(filter: AmlKycReviewFilter, order: AmlKycReviewOrder, first: Int, offset: Int): [AmlKycReview]
	numUids: Int
}

type AddClientFormCompareDataPayload {
	clientFormCompareData(filter: ClientFormCompareDataFilter, order: ClientFormCompareDataOrder, first: Int, offset: Int): [ClientFormCompareData]
	numUids: Int
}

type AddClientPayload {
	client(filter: ClientFilter, order: ClientOrder, first: Int, offset: Int): [Client]
	numUids: Int
}

type AddClosePayload {
	close(filter: CloseFilter, order: CloseOrder, first: Int, offset: Int): [Close]
	numUids: Int
}

type AddCustomDataPayload {
	customData(filter: CustomDataFilter, order: CustomDataOrder, first: Int, offset: Int): [CustomData]
	numUids: Int
}

type AddDataExtractionRequestPayload {
	dataExtractionRequest(filter: DataExtractionRequestFilter, order: DataExtractionRequestOrder, first: Int, offset: Int): [DataExtractionRequest]
	numUids: Int
}

type AddFilePayload {
	file(filter: FileFilter, order: FileOrder, first: Int, offset: Int): [File]
	numUids: Int
}

type AddFormQuestionDataPayload {
	formQuestionData(filter: FormQuestionDataFilter, order: FormQuestionDataOrder, first: Int, offset: Int): [FormQuestionData]
	numUids: Int
}

type AddFundSubscriptionPayload {
	fundSubscription(filter: FundSubscriptionFilter, order: FundSubscriptionOrder, first: Int, offset: Int): [FundSubscription]
	numUids: Int
}

type AddInvestorGroupPayload {
	investorGroup(filter: InvestorGroupFilter, order: InvestorGroupOrder, first: Int, offset: Int): [InvestorGroup]
	numUids: Int
}

type AddMetadataFieldPayload {
	metadataField(filter: MetadataFieldFilter, order: MetadataFieldOrder, first: Int, offset: Int): [MetadataField]
	numUids: Int
}

type AddOrderDocumentPayload {
	orderDocument(filter: OrderDocumentFilter, first: Int, offset: Int): [OrderDocument]
	numUids: Int
}

type AddOrderPayload {
	order(filter: OrderFilter, order: OrderOrder, first: Int, offset: Int): [Order]
	numUids: Int
}

type AddSignatureRequestPayload {
	signatureRequest(filter: SignatureRequestFilter, order: SignatureRequestOrder, first: Int, offset: Int): [SignatureRequest]
	numUids: Int
}

type AddSubFundCommitmentAmountPayload {
	subFundCommitmentAmount(filter: SubFundCommitmentAmountFilter, order: SubFundCommitmentAmountOrder, first: Int, offset: Int): [SubFundCommitmentAmount]
	numUids: Int
}

type AddSubFundPayload {
	subFund(filter: SubFundFilter, order: SubFundOrder, first: Int, offset: Int): [SubFund]
	numUids: Int
}

type AddSubscriptionDocReviewInfoPayload {
	subscriptionDocReviewInfo(filter: SubscriptionDocReviewInfoFilter, order: SubscriptionDocReviewInfoOrder, first: Int, offset: Int): [SubscriptionDocReviewInfo]
	numUids: Int
}

type AddSupportingDocumentPayload {
	supportingDocument(filter: SupportingDocumentFilter, order: SupportingDocumentOrder, first: Int, offset: Int): [SupportingDocument]
	numUids: Int
}

type AddTagPayload {
	tag(filter: TagFilter, order: TagOrder, first: Int, offset: Int): [Tag]
	numUids: Int
}

type AddTemplateColumnPayload {
	templateColumn(filter: TemplateColumnFilter, order: TemplateColumnOrder, first: Int, offset: Int): [TemplateColumn]
	numUids: Int
}

type AddTemplatePayload {
	template(filter: TemplateFilter, order: TemplateOrder, first: Int, offset: Int): [Template]
	numUids: Int
}

type AddUserPayload {
	user(filter: UserFilter, order: UserOrder, first: Int, offset: Int): [User]
	numUids: Int
}

type AdvisorEntityAggregateResult {
	count: Int
	idMin: String
	idMax: String
	nameMin: String
	nameMax: String
}

type AdvisorTagAggregateResult {
	count: Int
	idMin: String
	idMax: String
	nameMin: String
	nameMax: String
	createdAtMin: DateTime
	createdAtMax: DateTime
}

type AmlCheckAggregateResult {
	count: Int
	idMin: String
	idMax: String
}

type AmlKycReviewAggregateResult {
	count: Int
	docNameMin: String
	docNameMax: String
	updatedAtMin: DateTime
	updatedAtMax: DateTime
}

type ChecklistObjAggregateResult {
	count: Int
}

type ClientAggregateResult {
	count: Int
	idMin: String
	idMax: String
	nameMin: String
	nameMax: String
	customIdMin: String
	customIdMax: String
}

type ClientFormCompareDataAggregateResult {
	count: Int
	clientIdMin: String
	clientIdMax: String
	numOfDifferencesMin: Int
	numOfDifferencesMax: Int
	numOfDifferencesSum: Int
	numOfDifferencesAvg: Float
	comparedAtMin: DateTime
	comparedAtMax: DateTime
}

type CloseAggregateResult {
	count: Int
	idMin: String
	idMax: String
	nameMin: String
	nameMax: String
	customCloseIdMin: String
	customCloseIdMax: String
	targetDateMin: DateTime
	targetDateMax: DateTime
}

type CurrencyObjAggregateResult {
	count: Int
	currencyMin: String
	currencyMax: String
	amountMin: Float
	amountMax: Float
	amountSum: Float
	amountAvg: Float
}

type CustomDataAggregateResult {
	count: Int
	dataColumnIdMin: String
	dataColumnIdMax: String
	columnNameMin: String
	columnNameMax: String
	lastEditedAtOptMin: DateTime
	lastEditedAtOptMax: DateTime
}

type CustomValueObjAggregateResult {
	count: Int
}

type DataExtractionRequestAggregateResult {
	count: Int
	idMin: String
	idMax: String
	missingRequiredFieldsMin: Int
	missingRequiredFieldsMax: Int
	missingRequiredFieldsSum: Int
	missingRequiredFieldsAvg: Float
	missingRecommendedFieldsMin: Int
	missingRecommendedFieldsMax: Int
	missingRecommendedFieldsSum: Int
	missingRecommendedFieldsAvg: Float
}

type DateTimeObjAggregateResult {
	count: Int
	datetimeValueMin: DateTime
	datetimeValueMax: DateTime
}

type DeleteAdvisorEntityPayload {
	advisorEntity(filter: AdvisorEntityFilter, order: AdvisorEntityOrder, first: Int, offset: Int): [AdvisorEntity]
	msg: String
	numUids: Int
}

type DeleteAdvisorTagPayload {
	advisorTag(filter: AdvisorTagFilter, order: AdvisorTagOrder, first: Int, offset: Int): [AdvisorTag]
	msg: String
	numUids: Int
}

type DeleteAmlCheckPayload {
	amlCheck(filter: AmlCheckFilter, order: AmlCheckOrder, first: Int, offset: Int): [AmlCheck]
	msg: String
	numUids: Int
}

type DeleteAmlKycReviewPayload {
	amlKycReview(filter: AmlKycReviewFilter, order: AmlKycReviewOrder, first: Int, offset: Int): [AmlKycReview]
	msg: String
	numUids: Int
}

type DeleteClientFormCompareDataPayload {
	clientFormCompareData(filter: ClientFormCompareDataFilter, order: ClientFormCompareDataOrder, first: Int, offset: Int): [ClientFormCompareData]
	msg: String
	numUids: Int
}

type DeleteClientPayload {
	client(filter: ClientFilter, order: ClientOrder, first: Int, offset: Int): [Client]
	msg: String
	numUids: Int
}

type DeleteClosePayload {
	close(filter: CloseFilter, order: CloseOrder, first: Int, offset: Int): [Close]
	msg: String
	numUids: Int
}

type DeleteCustomDataPayload {
	customData(filter: CustomDataFilter, order: CustomDataOrder, first: Int, offset: Int): [CustomData]
	msg: String
	numUids: Int
}

type DeleteDataExtractionRequestPayload {
	dataExtractionRequest(filter: DataExtractionRequestFilter, order: DataExtractionRequestOrder, first: Int, offset: Int): [DataExtractionRequest]
	msg: String
	numUids: Int
}

type DeleteFilePayload {
	file(filter: FileFilter, order: FileOrder, first: Int, offset: Int): [File]
	msg: String
	numUids: Int
}

type DeleteFormQuestionDataPayload {
	formQuestionData(filter: FormQuestionDataFilter, order: FormQuestionDataOrder, first: Int, offset: Int): [FormQuestionData]
	msg: String
	numUids: Int
}

type DeleteFundSubscriptionPayload {
	fundSubscription(filter: FundSubscriptionFilter, order: FundSubscriptionOrder, first: Int, offset: Int): [FundSubscription]
	msg: String
	numUids: Int
}

type DeleteInvestorGroupPayload {
	investorGroup(filter: InvestorGroupFilter, order: InvestorGroupOrder, first: Int, offset: Int): [InvestorGroup]
	msg: String
	numUids: Int
}

type DeleteMetadataFieldPayload {
	metadataField(filter: MetadataFieldFilter, order: MetadataFieldOrder, first: Int, offset: Int): [MetadataField]
	msg: String
	numUids: Int
}

type DeleteOrderDocumentPayload {
	orderDocument(filter: OrderDocumentFilter, first: Int, offset: Int): [OrderDocument]
	msg: String
	numUids: Int
}

type DeleteOrderPayload {
	order(filter: OrderFilter, order: OrderOrder, first: Int, offset: Int): [Order]
	msg: String
	numUids: Int
}

type DeleteSignatureRequestPayload {
	signatureRequest(filter: SignatureRequestFilter, order: SignatureRequestOrder, first: Int, offset: Int): [SignatureRequest]
	msg: String
	numUids: Int
}

type DeleteSubFundCommitmentAmountPayload {
	subFundCommitmentAmount(filter: SubFundCommitmentAmountFilter, order: SubFundCommitmentAmountOrder, first: Int, offset: Int): [SubFundCommitmentAmount]
	msg: String
	numUids: Int
}

type DeleteSubFundPayload {
	subFund(filter: SubFundFilter, order: SubFundOrder, first: Int, offset: Int): [SubFund]
	msg: String
	numUids: Int
}

type DeleteSubscriptionDocReviewInfoPayload {
	subscriptionDocReviewInfo(filter: SubscriptionDocReviewInfoFilter, order: SubscriptionDocReviewInfoOrder, first: Int, offset: Int): [SubscriptionDocReviewInfo]
	msg: String
	numUids: Int
}

type DeleteSupportingDocumentPayload {
	supportingDocument(filter: SupportingDocumentFilter, order: SupportingDocumentOrder, first: Int, offset: Int): [SupportingDocument]
	msg: String
	numUids: Int
}

type DeleteTagPayload {
	tag(filter: TagFilter, order: TagOrder, first: Int, offset: Int): [Tag]
	msg: String
	numUids: Int
}

type DeleteTemplateColumnPayload {
	templateColumn(filter: TemplateColumnFilter, order: TemplateColumnOrder, first: Int, offset: Int): [TemplateColumn]
	msg: String
	numUids: Int
}

type DeleteTemplatePayload {
	template(filter: TemplateFilter, order: TemplateOrder, first: Int, offset: Int): [Template]
	msg: String
	numUids: Int
}

type DeleteUserPayload {
	user(filter: UserFilter, order: UserOrder, first: Int, offset: Int): [User]
	msg: String
	numUids: Int
}

type FileAggregateResult {
	count: Int
	idMin: String
	idMax: String
	nameMin: String
	nameMax: String
	uploadedAtMin: DateTime
	uploadedAtMax: DateTime
}

type FormQuestionDataAggregateResult {
	count: Int
	namespaceMin: String
	namespaceMax: String
	aliasMin: String
	aliasMax: String
	valueMin: String
	valueMax: String
}

type FundSubscriptionAggregateResult {
	count: Int
	idMin: String
	idMax: String
	customFundIdMin: String
	customFundIdMax: String
	nameMin: String
	nameMax: String
	currencyMin: String
	currencyMax: String
	createdAtMin: DateTime
	createdAtMax: DateTime
	inactiveNotificationDurationMin: Int
	inactiveNotificationDurationMax: Int
	inactiveNotificationDurationSum: Int
	inactiveNotificationDurationAvg: Float
	lastUpdatedAtMin: DateTime
	lastUpdatedAtMax: DateTime
}

type InvestorGroupAggregateResult {
	count: Int
	idMin: String
	idMax: String
	nameMin: String
	nameMax: String
}

type MetadataFieldAggregateResult {
	count: Int
	fieldNameMin: String
	fieldNameMax: String
	valueMin: String
	valueMax: String
}

type MultipleValueObjAggregateResult {
	count: Int
}

type OrderAggregateResult {
	count: Int
	idMin: String
	idMax: String
	customIdMin: String
	customIdMax: String
	investmentEntityMin: String
	investmentEntityMax: String
	createdAtMin: DateTime
	createdAtMax: DateTime
	lastUpdatedAtMin: DateTime
	lastUpdatedAtMax: DateTime
	lastActivityAtMin: DateTime
	lastActivityAtMax: DateTime
	lastReminderSentAtMin: DateTime
	lastReminderSentAtMax: DateTime
	lastReinvitationSentAtMin: DateTime
	lastReinvitationSentAtMax: DateTime
	submissionDateMin: DateTime
	submissionDateMax: DateTime
	estimatedCommitmentAmountMin: Float
	estimatedCommitmentAmountMax: Float
	estimatedCommitmentAmountSum: Float
	estimatedCommitmentAmountAvg: Float
	submittedCommitmentAmountMin: Float
	submittedCommitmentAmountMax: Float
	submittedCommitmentAmountSum: Float
	submittedCommitmentAmountAvg: Float
	acceptedCommitmentAmountMin: Float
	acceptedCommitmentAmountMax: Float
	acceptedCommitmentAmountSum: Float
	acceptedCommitmentAmountAvg: Float
	formFillingProgressMin: Float
	formFillingProgressMax: Float
	formFillingProgressSum: Float
	formFillingProgressAvg: Float
	missingRequiredFieldsMin: Int
	missingRequiredFieldsMax: Int
	missingRequiredFieldsSum: Int
	missingRequiredFieldsAvg: Float
	missingRecommendedFieldsMin: Int
	missingRecommendedFieldsMax: Int
	missingRecommendedFieldsSum: Int
	missingRecommendedFieldsAvg: Float
}

type OrderDocumentAggregateResult {
	count: Int
}

type SignatureRequestAggregateResult {
	count: Int
	idMin: String
	idMax: String
	requestedAtOptMin: DateTime
	requestedAtOptMax: DateTime
}

type SingleValueObjAggregateResult {
	count: Int
	singleValueMin: String
	singleValueMax: String
}

type StringObjAggregateResult {
	count: Int
	stringValueMin: String
	stringValueMax: String
}

type SubFundAggregateResult {
	count: Int
	idMin: String
	idMax: String
	customIdMin: String
	customIdMax: String
	currencyMin: String
	currencyMax: String
	nameMin: String
	nameMax: String
}

type SubFundCommitmentAmountAggregateResult {
	count: Int
	estimatedCommitmentAmountMin: Float
	estimatedCommitmentAmountMax: Float
	estimatedCommitmentAmountSum: Float
	estimatedCommitmentAmountAvg: Float
	submittedCommitmentAmountMin: Float
	submittedCommitmentAmountMax: Float
	submittedCommitmentAmountSum: Float
	submittedCommitmentAmountAvg: Float
	acceptedCommitmentAmountMin: Float
	acceptedCommitmentAmountMax: Float
	acceptedCommitmentAmountSum: Float
	acceptedCommitmentAmountAvg: Float
}

type SubscriptionDocReviewInfoAggregateResult {
	count: Int
	idMin: String
	idMax: String
	versionIndexMin: Int
	versionIndexMax: Int
	versionIndexSum: Int
	versionIndexAvg: Float
	currentPendingStepIdMin: String
	currentPendingStepIdMax: String
}

type SupportingDocumentAggregateResult {
	count: Int
	nameMin: String
	nameMax: String
}

type TagAggregateResult {
	count: Int
	idMin: String
	idMax: String
	nameMin: String
	nameMax: String
	createdAtMin: DateTime
	createdAtMax: DateTime
}

type TemplateAggregateResult {
	count: Int
	idMin: String
	idMax: String
	nameMin: String
	nameMax: String
	lastChangeDescriptionMin: String
	lastChangeDescriptionMax: String
	createdAtMin: DateTime
	createdAtMax: DateTime
	lastUpdatedAtMin: DateTime
	lastUpdatedAtMax: DateTime
}

type TemplateColumnAggregateResult {
	count: Int
	indexMin: Int
	indexMax: Int
	indexSum: Int
	indexAvg: Float
	titleMin: String
	titleMax: String
}

type UpdateAdvisorEntityPayload {
	advisorEntity(filter: AdvisorEntityFilter, order: AdvisorEntityOrder, first: Int, offset: Int): [AdvisorEntity]
	numUids: Int
}

type UpdateAmlCheckPayload {
	amlCheck(filter: AmlCheckFilter, order: AmlCheckOrder, first: Int, offset: Int): [AmlCheck]
	numUids: Int
}

type UpdateAmlKycReviewPayload {
	amlKycReview(filter: AmlKycReviewFilter, order: AmlKycReviewOrder, first: Int, offset: Int): [AmlKycReview]
	numUids: Int
}

type UpdateClientFormCompareDataPayload {
	clientFormCompareData(filter: ClientFormCompareDataFilter, order: ClientFormCompareDataOrder, first: Int, offset: Int): [ClientFormCompareData]
	numUids: Int
}

type UpdateClientPayload {
	client(filter: ClientFilter, order: ClientOrder, first: Int, offset: Int): [Client]
	numUids: Int
}

type UpdateClosePayload {
	close(filter: CloseFilter, order: CloseOrder, first: Int, offset: Int): [Close]
	numUids: Int
}

type UpdateCustomDataPayload {
	customData(filter: CustomDataFilter, order: CustomDataOrder, first: Int, offset: Int): [CustomData]
	numUids: Int
}

type UpdateDataExtractionRequestPayload {
	dataExtractionRequest(filter: DataExtractionRequestFilter, order: DataExtractionRequestOrder, first: Int, offset: Int): [DataExtractionRequest]
	numUids: Int
}

type UpdateFilePayload {
	file(filter: FileFilter, order: FileOrder, first: Int, offset: Int): [File]
	numUids: Int
}

type UpdateFormQuestionDataPayload {
	formQuestionData(filter: FormQuestionDataFilter, order: FormQuestionDataOrder, first: Int, offset: Int): [FormQuestionData]
	numUids: Int
}

type UpdateFundSubscriptionPayload {
	fundSubscription(filter: FundSubscriptionFilter, order: FundSubscriptionOrder, first: Int, offset: Int): [FundSubscription]
	numUids: Int
}

type UpdateInvestorGroupPayload {
	investorGroup(filter: InvestorGroupFilter, order: InvestorGroupOrder, first: Int, offset: Int): [InvestorGroup]
	numUids: Int
}

type UpdateMetadataFieldPayload {
	metadataField(filter: MetadataFieldFilter, order: MetadataFieldOrder, first: Int, offset: Int): [MetadataField]
	numUids: Int
}

type UpdateOrderDocumentPayload {
	orderDocument(filter: OrderDocumentFilter, first: Int, offset: Int): [OrderDocument]
	numUids: Int
}

type UpdateOrderPayload {
	order(filter: OrderFilter, order: OrderOrder, first: Int, offset: Int): [Order]
	numUids: Int
}

type UpdateSignatureRequestPayload {
	signatureRequest(filter: SignatureRequestFilter, order: SignatureRequestOrder, first: Int, offset: Int): [SignatureRequest]
	numUids: Int
}

type UpdateSubFundCommitmentAmountPayload {
	subFundCommitmentAmount(filter: SubFundCommitmentAmountFilter, order: SubFundCommitmentAmountOrder, first: Int, offset: Int): [SubFundCommitmentAmount]
	numUids: Int
}

type UpdateSubFundPayload {
	subFund(filter: SubFundFilter, order: SubFundOrder, first: Int, offset: Int): [SubFund]
	numUids: Int
}

type UpdateSubscriptionDocReviewInfoPayload {
	subscriptionDocReviewInfo(filter: SubscriptionDocReviewInfoFilter, order: SubscriptionDocReviewInfoOrder, first: Int, offset: Int): [SubscriptionDocReviewInfo]
	numUids: Int
}

type UpdateSupportingDocumentPayload {
	supportingDocument(filter: SupportingDocumentFilter, order: SupportingDocumentOrder, first: Int, offset: Int): [SupportingDocument]
	numUids: Int
}

type UpdateTagPayload {
	tag(filter: TagFilter, order: TagOrder, first: Int, offset: Int): [Tag]
	numUids: Int
}

type UpdateTemplateColumnPayload {
	templateColumn(filter: TemplateColumnFilter, order: TemplateColumnOrder, first: Int, offset: Int): [TemplateColumn]
	numUids: Int
}

type UpdateTemplatePayload {
	template(filter: TemplateFilter, order: TemplateOrder, first: Int, offset: Int): [Template]
	numUids: Int
}

type UpdateUserPayload {
	user(filter: UserFilter, order: UserOrder, first: Int, offset: Int): [User]
	numUids: Int
}

type UserAggregateResult {
	count: Int
	idMin: String
	idMax: String
	emailMin: String
	emailMax: String
	firstNameMin: String
	firstNameMax: String
	lastNameMin: String
	lastNameMax: String
}

#######################
# Generated Enums
#######################

enum AdvisorEntityHasFilter {
	id
	name
	fund
}

enum AdvisorEntityOrderable {
	id
	name
}

enum AdvisorTagHasFilter {
	id
	fund
	name
	creator
	createdAt
}

enum AdvisorTagOrderable {
	id
	name
	createdAt
}

enum AmlCheckHasFilter {
	id
	status
}

enum AmlCheckOrderable {
	id
}

enum AmlKycReviewHasFilter {
	docName
	order
	status
	updatedBy
	updatedAt
}

enum AmlKycReviewOrderable {
	docName
	updatedAt
}

enum ChecklistObjHasFilter {
	checkedOptions
}

enum ClientFormCompareDataHasFilter {
	clientId
	numOfDifferences
	comparedAt
}

enum ClientFormCompareDataOrderable {
	clientId
	numOfDifferences
	comparedAt
}

enum ClientHasFilter {
	id
	name
	customId
}

enum ClientOrderable {
	id
	name
	customId
}

enum CloseHasFilter {
	id
	fund
	name
	customCloseId
	targetDate
}

enum CloseOrderable {
	id
	name
	customCloseId
	targetDate
}

enum CurrencyObjHasFilter {
	currency
	amount
}

enum CurrencyObjOrderable {
	currency
	amount
}

enum CustomDataHasFilter {
	order
	dataColumnId
	columnName
	customValue
	lastEditedByOpt
	lastEditedAtOpt
}

enum CustomDataOrderable {
	dataColumnId
	columnName
	lastEditedAtOpt
}

enum CustomValueObjHasFilter {
	customValue
}

enum CustomValueUnionType {
	DateTimeObj
	CurrencyObj
	SingleValueObj
	MultipleValueObj
	StringObj
	ChecklistObj
}

enum DataExtractionRequestHasFilter {
	id
	status
	missingRequiredFields
	missingRecommendedFields
}

enum DataExtractionRequestOrderable {
	id
	missingRequiredFields
	missingRecommendedFields
}

enum DateTimeObjHasFilter {
	datetimeValue
}

enum DateTimeObjOrderable {
	datetimeValue
}

enum FileHasFilter {
	id
	name
	uploader
	uploadedAt
}

enum FileOrderable {
	id
	name
	uploadedAt
}

enum FormQuestionDataHasFilter {
	namespace
	alias
	value
}

enum FormQuestionDataOrderable {
	namespace
	alias
	value
}

enum FundSubscriptionHasFilter {
	id
	customFundId
	name
	admins
	currency
	closes
	tags
	templates
	referenceDocs
	subFunds
	orders
	supportingDocReviewEnabled
	createdAt
	inactiveNotificationEnabled
	inactiveNotificationDuration
	lastUpdatedAt
	investorGroups
	advisorEntities
}

enum FundSubscriptionOrderable {
	id
	customFundId
	name
	currency
	createdAt
	inactiveNotificationDuration
	lastUpdatedAt
}

enum InvestorGroupHasFilter {
	id
	name
	fund
}

enum InvestorGroupOrderable {
	id
	name
}

enum MetadataFieldHasFilter {
	fieldName
	value
}

enum MetadataFieldOrderable {
	fieldName
	value
}

enum MultipleValueObjHasFilter {
	multipleValue
}

enum OrderDocumentHasFilter {
	file
	docType
	documentType
	order
}

enum OrderHasFilter {
	id
	customId
	fund
	orderType
	mainContact
	contacts
	pendingContacts
	investmentEntity
	createdAt
	lastUpdatedAt
	lastActivityAt
	lastReminderSentAt
	lastReinvitationSentAt
	submissionDate
	seenByUsers
	tags
	close
	status
	estimatedCommitmentAmount
	submittedCommitmentAmount
	acceptedCommitmentAmount
	commitmentAmounts
	referenceDocs
	customData
	formValues
	formFillingProgress
	missingRequiredFields
	missingRecommendedFields
	supportingDocs
	supportingDocSignatureRequests
	submittedDocs
	uploadedCountersignedDocs
	countersignedDocs
	countersignSignatureRequests
	emailBouncedContacts
	amlKycReviews
	unsignedSubscriptionReview
	signedSubscriptionReview
	amlKycDocsProvidedOffline
	metadata
	clients
	subDocDataExtractionRequest
	amlCheck
	investorSignedOnPaper
	clientFormCompareData
	investorGroup
	advisorEntity
	sideLetterStatus
}

enum OrderOrderable {
	id
	customId
	investmentEntity
	createdAt
	lastUpdatedAt
	lastActivityAt
	lastReminderSentAt
	lastReinvitationSentAt
	submissionDate
	estimatedCommitmentAmount
	submittedCommitmentAmount
	acceptedCommitmentAmount
	formFillingProgress
	missingRequiredFields
	missingRecommendedFields
}

enum SignatureRequestHasFilter {
	id
	status
	requesterOpt
	requestedAtOpt
}

enum SignatureRequestOrderable {
	id
	requestedAtOpt
}

enum SingleValueObjHasFilter {
	singleValue
}

enum SingleValueObjOrderable {
	singleValue
}

enum StringObjHasFilter {
	stringValue
}

enum StringObjOrderable {
	stringValue
}

enum SubFundCommitmentAmountHasFilter {
	subFund
	estimatedCommitmentAmount
	submittedCommitmentAmount
	acceptedCommitmentAmount
}

enum SubFundCommitmentAmountOrderable {
	estimatedCommitmentAmount
	submittedCommitmentAmount
	acceptedCommitmentAmount
}

enum SubFundHasFilter {
	id
	customId
	masterFund
	currency
	name
}

enum SubFundOrderable {
	id
	customId
	currency
	name
}

enum SubscriptionDocReviewInfoHasFilter {
	id
	versionIndex
	status
	currentPendingStepId
}

enum SubscriptionDocReviewInfoOrderable {
	id
	versionIndex
	currentPendingStepId
}

enum SupportingDocumentHasFilter {
	order
	name
	markedAsNa
	submitted
	submittedDocs
}

enum SupportingDocumentOrderable {
	name
}

enum TagHasFilter {
	id
	fund
	orders
	name
	creator
	createdAt
}

enum TagOrderable {
	id
	name
	createdAt
}

enum TemplateColumnHasFilter {
	index
	title
}

enum TemplateColumnOrderable {
	index
	title
}

enum TemplateHasFilter {
	id
	name
	templateType
	lastChangeDescription
	spreadsheetFile
	columns
	createdBy
	createdAt
	lastUpdatedAt
}

enum TemplateOrderable {
	id
	name
	lastChangeDescription
	createdAt
	lastUpdatedAt
}

enum UserHasFilter {
	id
	email
	firstName
	lastName
}

enum UserOrderable {
	id
	email
	firstName
	lastName
}

#######################
# Generated Inputs
#######################

input AddAdvisorEntityInput {
	id: String!
	name: String!
	fund: FundSubscriptionRef
}

input AddAdvisorTagInput {
	"""A system generated value provided at the time the AdvisorTag is created."""
	id: String!
	fund: FundSubscriptionRef
	"""The label of the AdvisorTag applied upon creation. This is the value actually illustrated on the platform."""
	name: String!
	creator: UserRef
	"""The time at which the AdvisorTag was created."""
	createdAt: DateTime
}

input AddAmlCheckInput {
	id: String!
	status: AmlCheckStatus!
}

input AddAmlKycReviewInput {
	docName: String!
	order: OrderRef
	status: ReviewStatus
	updatedBy: UserRef
	updatedAt: DateTime
}

input AddClientFormCompareDataInput {
	clientId: String!
	numOfDifferences: Int!
	comparedAt: DateTime
}

input AddClientInput {
	"""A system generated value provided at the time the Client is created."""
	id: String!
	name: String!
	customId: String
}

input AddCloseInput {
	"""A system generated value provided at the time the Close is created."""
	id: String!
	fund: FundSubscriptionRef
	"""The descriptive name of the close provide upon creating the Close."""
	name: String!
	"""A custom id assigned to the Close upon creating the Close."""
	customCloseId: String
	"""The planned completion of the Close assigned upon creation of the Close."""
	targetDate: DateTime
}

input AddCustomDataInput {
	order: OrderRef
	dataColumnId: String!
	columnName: String
	customValue: CustomValueObjRef!
	lastEditedByOpt: UserRef
	lastEditedAtOpt: DateTime
}

input AddDataExtractionRequestInput {
	id: String!
	status: DataExtractionRequestStatus!
	missingRequiredFields: Int!
	missingRecommendedFields: Int!
}

input AddFileInput {
	"""A system generated value provided at the time the File is created."""
	id: String!
	"""The name of the file that has been created."""
	name: String!
	uploader: UserRef
	"""Upload time"""
	uploadedAt: DateTime
}

input AddFormQuestionDataInput {
	namespace: String!
	alias: String!
	value: String!
}

input AddFundSubscriptionInput {
	"""A system generated value provided at the time the FundSubscription is created."""
	id: String!
	"""A custom ID that references a fund."""
	customFundId: String
	"""The name of the Fund."""
	name: String
	admins: [UserRef!]!
	"""The currency in which the fund and corresponding order will be transacted in."""
	currency: String!
	closes: [CloseRef!]!
	tags: [TagRef!]!
	templates: [TemplateRef!]!
	referenceDocs: [FileRef!]!
	subFunds: [SubFundRef!]
	orders: [OrderRef]!
	supportingDocReviewEnabled: Boolean
	"""A system generated timestamp upon the creation of the fund."""
	createdAt: DateTime
	"""WIP"""
	inactiveNotificationEnabled: Boolean!
	"""WIP"""
	inactiveNotificationDuration: Int!
	"""A timestamp of the last update to the fund."""
	lastUpdatedAt: DateTime
	investorGroups: [InvestorGroupRef!]!
	advisorEntities: [AdvisorEntityRef!]!
}

input AddInvestorGroupInput {
	id: String!
	name: String!
	fund: FundSubscriptionRef
}

input AddMetadataFieldInput {
	fieldName: String
	value: String
}

input AddOrderDocumentInput {
	file: FileRef
	"""The type of document."""
	docType: FundSubDocType
	"""The type of document."""
	documentType: DocType
	order: OrderRef
}

input AddOrderInput {
	"""A system generated value provided at the time the Order is created."""
	id: String!
	"""A customID that may be injected at the time of creation of the order.  Typically a value associated the investment entity sourced from the customer CRM or other customer internal repositories."""
	customId: String
	fund: FundSubscriptionRef
	"""Indicates the type of subscription, either a normal or offline subscription, where offline would indicate documents have been completed external to the platform."""
	orderType: OrderType!
	mainContact: UserRef!
	contacts: [UserRef!]!
	pendingContacts: [UserRef!]!
	"""The name of the individual or entity who will own the given security."""
	investmentEntity: String!
	"""A system generated timestamp upon the creation of the order."""
	createdAt: DateTime
	"""A timestamp of the last update to the order."""
	lastUpdatedAt: DateTime
	"""WIP"""
	lastActivityAt: DateTime
	"""A timestamp of the last reminder sent to user(s)."""
	lastReminderSentAt: DateTime
	"""A timestamp of the last re-invitation sent to user(s)."""
	lastReinvitationSentAt: DateTime
	"""A timestamp of when the order was submitted."""
	submissionDate: DateTime
	seenByUsers: [UserRef!]!
	tags: [TagRef!]!
	close: CloseRef
	"""The status of the order."""
	status: OrderStatus!
	"""An optional pre-emptive value provided by the fund based upon previous conversations or expressed interest amount from the data room or other means."""
	estimatedCommitmentAmount: Float
	"""The commitment amount identified in the submitted subscription document."""
	submittedCommitmentAmount: Float
	"""The commitment amount accepted by the Fund post review."""
	acceptedCommitmentAmount: Float
	commitmentAmounts: [SubFundCommitmentAmountRef!]
	referenceDocs: [FileRef!]!
	customData: [CustomDataRef!]!
	formValues: [FormQuestionDataRef!]!
	"""The completion progress of the investor based upon which fields in the subscription form are required."""
	formFillingProgress: Float!
	"""Required fields from the subscription form that have not yet been completed."""
	missingRequiredFields: Int!
	"""Recommended fields from the subscription form that have not yet been completed."""
	missingRecommendedFields: Int!
	supportingDocs: [SupportingDocumentRef!]!
	supportingDocSignatureRequests: [SignatureRequestRef!]!
	submittedDocs: [OrderDocumentRef!]!
	uploadedCountersignedDocs: [OrderDocumentRef!]!
	countersignedDocs: [OrderDocumentRef!]!
	countersignSignatureRequests: [SignatureRequestRef!]!
	emailBouncedContacts: [UserRef!]!
	amlKycReviews: [AmlKycReviewRef!]!
	unsignedSubscriptionReview: SubscriptionDocReviewInfoRef
	signedSubscriptionReview: SubscriptionDocReviewInfoRef
	"""AML/KYC documents that this investor provided offline"""
	amlKycDocsProvidedOffline: [String!]
	metadata: [MetadataFieldRef!]
	clients: [ClientRef!]
	subDocDataExtractionRequest: DataExtractionRequestRef
	amlCheck: [AmlCheckRef!]
	"""Investor wet signed the subscription doc and uploaded the documents."""
	investorSignedOnPaper: Boolean
	clientFormCompareData: [ClientFormCompareDataRef!]
	investorGroup: InvestorGroupRef
	advisorEntity: AdvisorEntityRef
	"""The status of the side letter (feature is in development)"""
	sideLetterStatus: SideLetterStatus
}

input AddSignatureRequestInput {
	id: String!
	status: SignatureRequestStatus!
	requesterOpt: UserRef
	requestedAtOpt: DateTime
}

input AddSubFundCommitmentAmountInput {
	subFund: SubFundRef
	"""An optional pre-emptive value provided by the fund based upon previous conversations or expressed interest amount from the data room or other means."""
	estimatedCommitmentAmount: Float
	"""The commitment amount identified in the submitted subscription document."""
	submittedCommitmentAmount: Float
	"""The commitment amount accepted by the Fund post review."""
	acceptedCommitmentAmount: Float
}

input AddSubFundInput {
	id: String!
	customId: String
	masterFund: FundSubscriptionRef
	currency: String
	name: String
}

input AddSubscriptionDocReviewInfoInput {
	id: String!
	versionIndex: Int!
	status: ReviewStatus
	"""For internal use only!"""
	currentPendingStepId: String
}

input AddSupportingDocumentInput {
	order: OrderRef
	"""The name of the document requested per the corresponding FormQuestionData."""
	name: String!
	"""An indicator field, indicating the relevant of a given document requirement.  Marking the document as NA, removes it from the requirement list for a given order."""
	markedAsNa: Boolean!
	"""A flag to quickly check it this document is submitted i.e. submittedDocs is non-empty."""
	submitted: Boolean!
	submittedDocs: [OrderDocumentRef!]!
}

input AddTagInput {
	"""A system generated value provided at the time the Tag is created."""
	id: String!
	fund: FundSubscriptionRef
	orders: [OrderRef]!
	"""The label of the Tag applied upon creation. This is the value actually illustrated on the platform."""
	name: String!
	creator: UserRef
	"""The time at which the Tag was created."""
	createdAt: DateTime
}

input AddTemplateColumnInput {
	"""Order of this column in the spreadsheet"""
	index: Int!
	"""The column title."""
	title: String!
}

input AddTemplateInput {
	id: String!
	"""Name of the template."""
	name: String!
	"""Description about the last update."""
	templateType: TemplateType
	"""Description about the last update."""
	lastChangeDescription: String!
	spreadsheetFile: FileRef
	columns: [TemplateColumnRef!]!
	createdBy: UserRef
	"""A system generated timestamp upon the creation of the template."""
	createdAt: DateTime
	"""A timestamp of the last update to the template."""
	lastUpdatedAt: DateTime
}

input AddUserInput {
	"""A system generated value provided at the time the user is created."""
	id: String!
	"""The email address associated with a given user profile"""
	email: String!
	"""The first name of the user on the profile"""
	firstName: String!
	"""The first name of the user on the profile"""
	lastName: String!
}

input AdvisorEntityFilter {
	id: StringHashFilter
	name: StringHashFilter
	has: [AdvisorEntityHasFilter]
	and: [AdvisorEntityFilter]
	or: [AdvisorEntityFilter]
	not: AdvisorEntityFilter
}

input AdvisorEntityOrder {
	asc: AdvisorEntityOrderable
	desc: AdvisorEntityOrderable
	then: AdvisorEntityOrder
}

input AdvisorEntityPatch {
	name: String
	fund: FundSubscriptionRef
}

input AdvisorEntityRef {
	id: String
	name: String
	fund: FundSubscriptionRef
}

input AdvisorTagFilter {
	id: StringHashFilter
	name: StringHashFilter
	has: [AdvisorTagHasFilter]
	and: [AdvisorTagFilter]
	or: [AdvisorTagFilter]
	not: AdvisorTagFilter
}

input AdvisorTagOrder {
	asc: AdvisorTagOrderable
	desc: AdvisorTagOrderable
	then: AdvisorTagOrder
}

input AdvisorTagPatch {
	fund: FundSubscriptionRef
	name: String
	creator: UserRef
	createdAt: DateTime
}

input AdvisorTagRef {
	"""A system generated value provided at the time the AdvisorTag is created."""
	id: String
	fund: FundSubscriptionRef
	"""The label of the AdvisorTag applied upon creation. This is the value actually illustrated on the platform."""
	name: String
	creator: UserRef
	"""The time at which the AdvisorTag was created."""
	createdAt: DateTime
}

input AmlCheckFilter {
	id: StringHashFilter
	has: [AmlCheckHasFilter]
	and: [AmlCheckFilter]
	or: [AmlCheckFilter]
	not: AmlCheckFilter
}

input AmlCheckOrder {
	asc: AmlCheckOrderable
	desc: AmlCheckOrderable
	then: AmlCheckOrder
}

input AmlCheckPatch {
	status: AmlCheckStatus
}

input AmlCheckRef {
	id: String
	status: AmlCheckStatus
}

input AmlKycReviewFilter {
	id: [ID!]
	has: [AmlKycReviewHasFilter]
	and: [AmlKycReviewFilter]
	or: [AmlKycReviewFilter]
	not: AmlKycReviewFilter
}

input AmlKycReviewOrder {
	asc: AmlKycReviewOrderable
	desc: AmlKycReviewOrderable
	then: AmlKycReviewOrder
}

input AmlKycReviewPatch {
	docName: String
	order: OrderRef
	status: ReviewStatus
	updatedBy: UserRef
	updatedAt: DateTime
}

input AmlKycReviewRef {
	id: ID
	docName: String
	order: OrderRef
	status: ReviewStatus
	updatedBy: UserRef
	updatedAt: DateTime
}

input ChecklistObjFilter {
	has: [ChecklistObjHasFilter]
	and: [ChecklistObjFilter]
	or: [ChecklistObjFilter]
	not: ChecklistObjFilter
}

input ChecklistObjRef {
	checkedOptions: [String]
}

input ClientFilter {
	id: StringHashFilter
	has: [ClientHasFilter]
	and: [ClientFilter]
	or: [ClientFilter]
	not: ClientFilter
}

input ClientFormCompareDataFilter {
	id: [ID!]
	has: [ClientFormCompareDataHasFilter]
	and: [ClientFormCompareDataFilter]
	or: [ClientFormCompareDataFilter]
	not: ClientFormCompareDataFilter
}

input ClientFormCompareDataOrder {
	asc: ClientFormCompareDataOrderable
	desc: ClientFormCompareDataOrderable
	then: ClientFormCompareDataOrder
}

input ClientFormCompareDataPatch {
	clientId: String
	numOfDifferences: Int
	comparedAt: DateTime
}

input ClientFormCompareDataRef {
	id: ID
	clientId: String
	numOfDifferences: Int
	comparedAt: DateTime
}

input ClientOrder {
	asc: ClientOrderable
	desc: ClientOrderable
	then: ClientOrder
}

input ClientPatch {
	name: String
	customId: String
}

input ClientRef {
	"""A system generated value provided at the time the Client is created."""
	id: String
	name: String
	customId: String
}

input CloseFilter {
	id: StringHashFilter
	name: StringHashFilter
	has: [CloseHasFilter]
	and: [CloseFilter]
	or: [CloseFilter]
	not: CloseFilter
}

input CloseOrder {
	asc: CloseOrderable
	desc: CloseOrderable
	then: CloseOrder
}

input ClosePatch {
	fund: FundSubscriptionRef
	"""The descriptive name of the close provide upon creating the Close."""
	name: String
	"""A custom id assigned to the Close upon creating the Close."""
	customCloseId: String
	"""The planned completion of the Close assigned upon creation of the Close."""
	targetDate: DateTime
}

input CloseRef {
	"""A system generated value provided at the time the Close is created."""
	id: String
	fund: FundSubscriptionRef
	"""The descriptive name of the close provide upon creating the Close."""
	name: String
	"""A custom id assigned to the Close upon creating the Close."""
	customCloseId: String
	"""The planned completion of the Close assigned upon creation of the Close."""
	targetDate: DateTime
}

input CurrencyObjFilter {
	has: [CurrencyObjHasFilter]
	and: [CurrencyObjFilter]
	or: [CurrencyObjFilter]
	not: CurrencyObjFilter
}

input CurrencyObjOrder {
	asc: CurrencyObjOrderable
	desc: CurrencyObjOrderable
	then: CurrencyObjOrder
}

input CurrencyObjRef {
	currency: String
	amount: Float
}

input CustomDataFilter {
	id: [ID!]
	dataColumnId: StringHashFilter
	has: [CustomDataHasFilter]
	and: [CustomDataFilter]
	or: [CustomDataFilter]
	not: CustomDataFilter
}

input CustomDataOrder {
	asc: CustomDataOrderable
	desc: CustomDataOrderable
	then: CustomDataOrder
}

input CustomDataPatch {
	order: OrderRef
	dataColumnId: String
	columnName: String
	customValue: CustomValueObjRef
	lastEditedByOpt: UserRef
	lastEditedAtOpt: DateTime
}

input CustomDataRef {
	id: ID
	order: OrderRef
	dataColumnId: String
	columnName: String
	customValue: CustomValueObjRef
	lastEditedByOpt: UserRef
	lastEditedAtOpt: DateTime
}

input CustomValueObjFilter {
	has: [CustomValueObjHasFilter]
	and: [CustomValueObjFilter]
	or: [CustomValueObjFilter]
	not: CustomValueObjFilter
}

input CustomValueObjRef {
	customValue: CustomValueUnionRef
}

input CustomValueUnionFilter {
	memberTypes: [CustomValueUnionType!]
	dateTimeObjFilter: DateTimeObjFilter
	currencyObjFilter: CurrencyObjFilter
	singleValueObjFilter: SingleValueObjFilter
	multipleValueObjFilter: MultipleValueObjFilter
	stringObjFilter: StringObjFilter
	checklistObjFilter: ChecklistObjFilter
}

input CustomValueUnionRef {
	dateTimeObjRef: DateTimeObjRef
	currencyObjRef: CurrencyObjRef
	singleValueObjRef: SingleValueObjRef
	multipleValueObjRef: MultipleValueObjRef
	stringObjRef: StringObjRef
	checklistObjRef: ChecklistObjRef
}

input DataExtractionRequestFilter {
	id: StringHashFilter
	has: [DataExtractionRequestHasFilter]
	and: [DataExtractionRequestFilter]
	or: [DataExtractionRequestFilter]
	not: DataExtractionRequestFilter
}

input DataExtractionRequestOrder {
	asc: DataExtractionRequestOrderable
	desc: DataExtractionRequestOrderable
	then: DataExtractionRequestOrder
}

input DataExtractionRequestPatch {
	status: DataExtractionRequestStatus
	missingRequiredFields: Int
	missingRecommendedFields: Int
}

input DataExtractionRequestRef {
	id: String
	status: DataExtractionRequestStatus
	missingRequiredFields: Int
	missingRecommendedFields: Int
}

input DateTimeObjFilter {
	has: [DateTimeObjHasFilter]
	and: [DateTimeObjFilter]
	or: [DateTimeObjFilter]
	not: DateTimeObjFilter
}

input DateTimeObjOrder {
	asc: DateTimeObjOrderable
	desc: DateTimeObjOrderable
	then: DateTimeObjOrder
}

input DateTimeObjRef {
	datetimeValue: DateTime
}

input FileFilter {
	id: StringHashFilter
	has: [FileHasFilter]
	and: [FileFilter]
	or: [FileFilter]
	not: FileFilter
}

input FileOrder {
	asc: FileOrderable
	desc: FileOrderable
	then: FileOrder
}

input FilePatch {
	"""The name of the file that has been created."""
	name: String
	uploader: UserRef
	"""Upload time"""
	uploadedAt: DateTime
}

input FileRef {
	"""A system generated value provided at the time the File is created."""
	id: String
	"""The name of the file that has been created."""
	name: String
	uploader: UserRef
	"""Upload time"""
	uploadedAt: DateTime
}

input FormQuestionDataFilter {
	id: [ID!]
	namespace: StringExactFilter
	alias: StringExactFilter
	has: [FormQuestionDataHasFilter]
	and: [FormQuestionDataFilter]
	or: [FormQuestionDataFilter]
	not: FormQuestionDataFilter
}

input FormQuestionDataOrder {
	asc: FormQuestionDataOrderable
	desc: FormQuestionDataOrderable
	then: FormQuestionDataOrder
}

input FormQuestionDataPatch {
	namespace: String
	alias: String
	value: String
}

input FormQuestionDataRef {
	id: ID
	namespace: String
	alias: String
	value: String
}

input FundSubscriptionFilter {
	id: StringHashFilter
	has: [FundSubscriptionHasFilter]
	and: [FundSubscriptionFilter]
	or: [FundSubscriptionFilter]
	not: FundSubscriptionFilter
}

input FundSubscriptionOrder {
	asc: FundSubscriptionOrderable
	desc: FundSubscriptionOrderable
	then: FundSubscriptionOrder
}

input FundSubscriptionPatch {
	"""A custom ID that references a fund."""
	customFundId: String
	"""The name of the Fund."""
	name: String
	admins: [UserRef!]
	"""The currency in which the fund and corresponding order will be transacted in."""
	currency: String
	closes: [CloseRef!]
	tags: [TagRef!]
	templates: [TemplateRef!]
	referenceDocs: [FileRef!]
	subFunds: [SubFundRef!]
	orders: [OrderRef]
	supportingDocReviewEnabled: Boolean
	"""A system generated timestamp upon the creation of the fund."""
	createdAt: DateTime
	"""WIP"""
	inactiveNotificationEnabled: Boolean
	"""WIP"""
	inactiveNotificationDuration: Int
	"""A timestamp of the last update to the fund."""
	lastUpdatedAt: DateTime
	investorGroups: [InvestorGroupRef!]
	advisorEntities: [AdvisorEntityRef!]
}

input FundSubscriptionRef {
	"""A system generated value provided at the time the FundSubscription is created."""
	id: String
	"""A custom ID that references a fund."""
	customFundId: String
	"""The name of the Fund."""
	name: String
	admins: [UserRef!]
	"""The currency in which the fund and corresponding order will be transacted in."""
	currency: String
	closes: [CloseRef!]
	tags: [TagRef!]
	templates: [TemplateRef!]
	referenceDocs: [FileRef!]
	subFunds: [SubFundRef!]
	orders: [OrderRef]
	supportingDocReviewEnabled: Boolean
	"""A system generated timestamp upon the creation of the fund."""
	createdAt: DateTime
	"""WIP"""
	inactiveNotificationEnabled: Boolean
	"""WIP"""
	inactiveNotificationDuration: Int
	"""A timestamp of the last update to the fund."""
	lastUpdatedAt: DateTime
	investorGroups: [InvestorGroupRef!]
	advisorEntities: [AdvisorEntityRef!]
}

input InvestorGroupFilter {
	id: StringHashFilter
	name: StringHashFilter
	has: [InvestorGroupHasFilter]
	and: [InvestorGroupFilter]
	or: [InvestorGroupFilter]
	not: InvestorGroupFilter
}

input InvestorGroupOrder {
	asc: InvestorGroupOrderable
	desc: InvestorGroupOrderable
	then: InvestorGroupOrder
}

input InvestorGroupPatch {
	name: String
	fund: FundSubscriptionRef
}

input InvestorGroupRef {
	id: String
	name: String
	fund: FundSubscriptionRef
}

input MetadataFieldFilter {
	id: [ID!]
	has: [MetadataFieldHasFilter]
	and: [MetadataFieldFilter]
	or: [MetadataFieldFilter]
	not: MetadataFieldFilter
}

input MetadataFieldOrder {
	asc: MetadataFieldOrderable
	desc: MetadataFieldOrderable
	then: MetadataFieldOrder
}

input MetadataFieldPatch {
	fieldName: String
	value: String
}

input MetadataFieldRef {
	id: ID
	fieldName: String
	value: String
}

input MultipleValueObjFilter {
	has: [MultipleValueObjHasFilter]
	and: [MultipleValueObjFilter]
	or: [MultipleValueObjFilter]
	not: MultipleValueObjFilter
}

input MultipleValueObjRef {
	multipleValue: [String]
}

input OrderDocumentFilter {
	id: [ID!]
	has: [OrderDocumentHasFilter]
	and: [OrderDocumentFilter]
	or: [OrderDocumentFilter]
	not: OrderDocumentFilter
}

input OrderDocumentPatch {
	file: FileRef
	"""The type of document."""
	docType: FundSubDocType
	"""The type of document."""
	documentType: DocType
	order: OrderRef
}

input OrderDocumentRef {
	"""A system generated value provided at the time the OrderDocument is created."""
	id: ID
	file: FileRef
	"""The type of document."""
	docType: FundSubDocType
	"""The type of document."""
	documentType: DocType
	order: OrderRef
}

input OrderFilter {
	id: StringHashFilter
	customId: StringHashFilter
	status: OrderStatus_hash
	has: [OrderHasFilter]
	and: [OrderFilter]
	or: [OrderFilter]
	not: OrderFilter
}

input OrderOrder {
	asc: OrderOrderable
	desc: OrderOrderable
	then: OrderOrder
}

input OrderPatch {
	"""A customID that may be injected at the time of creation of the order.  Typically a value associated the investment entity sourced from the customer CRM or other customer internal repositories."""
	customId: String
	fund: FundSubscriptionRef
	"""Indicates the type of subscription, either a normal or offline subscription, where offline would indicate documents have been completed external to the platform."""
	orderType: OrderType
	mainContact: UserRef
	contacts: [UserRef!]
	pendingContacts: [UserRef!]
	"""The name of the individual or entity who will own the given security."""
	investmentEntity: String
	"""A system generated timestamp upon the creation of the order."""
	createdAt: DateTime
	"""A timestamp of the last update to the order."""
	lastUpdatedAt: DateTime
	"""WIP"""
	lastActivityAt: DateTime
	"""A timestamp of the last reminder sent to user(s)."""
	lastReminderSentAt: DateTime
	"""A timestamp of the last re-invitation sent to user(s)."""
	lastReinvitationSentAt: DateTime
	"""A timestamp of when the order was submitted."""
	submissionDate: DateTime
	seenByUsers: [UserRef!]
	tags: [TagRef!]
	close: CloseRef
	"""The status of the order."""
	status: OrderStatus
	"""An optional pre-emptive value provided by the fund based upon previous conversations or expressed interest amount from the data room or other means."""
	estimatedCommitmentAmount: Float
	"""The commitment amount identified in the submitted subscription document."""
	submittedCommitmentAmount: Float
	"""The commitment amount accepted by the Fund post review."""
	acceptedCommitmentAmount: Float
	commitmentAmounts: [SubFundCommitmentAmountRef!]
	referenceDocs: [FileRef!]
	customData: [CustomDataRef!]
	formValues: [FormQuestionDataRef!]
	"""The completion progress of the investor based upon which fields in the subscription form are required."""
	formFillingProgress: Float
	"""Required fields from the subscription form that have not yet been completed."""
	missingRequiredFields: Int
	"""Recommended fields from the subscription form that have not yet been completed."""
	missingRecommendedFields: Int
	supportingDocs: [SupportingDocumentRef!]
	supportingDocSignatureRequests: [SignatureRequestRef!]
	submittedDocs: [OrderDocumentRef!]
	uploadedCountersignedDocs: [OrderDocumentRef!]
	countersignedDocs: [OrderDocumentRef!]
	countersignSignatureRequests: [SignatureRequestRef!]
	emailBouncedContacts: [UserRef!]
	amlKycReviews: [AmlKycReviewRef!]
	unsignedSubscriptionReview: SubscriptionDocReviewInfoRef
	signedSubscriptionReview: SubscriptionDocReviewInfoRef
	"""AML/KYC documents that this investor provided offline"""
	amlKycDocsProvidedOffline: [String!]
	metadata: [MetadataFieldRef!]
	clients: [ClientRef!]
	subDocDataExtractionRequest: DataExtractionRequestRef
	amlCheck: [AmlCheckRef!]
	"""Investor wet signed the subscription doc and uploaded the documents."""
	investorSignedOnPaper: Boolean
	clientFormCompareData: [ClientFormCompareDataRef!]
	investorGroup: InvestorGroupRef
	advisorEntity: AdvisorEntityRef
	"""The status of the side letter (feature is in development)"""
	sideLetterStatus: SideLetterStatus
}

input OrderRef {
	"""A system generated value provided at the time the Order is created."""
	id: String
	"""A customID that may be injected at the time of creation of the order.  Typically a value associated the investment entity sourced from the customer CRM or other customer internal repositories."""
	customId: String
	fund: FundSubscriptionRef
	"""Indicates the type of subscription, either a normal or offline subscription, where offline would indicate documents have been completed external to the platform."""
	orderType: OrderType
	mainContact: UserRef
	contacts: [UserRef!]
	pendingContacts: [UserRef!]
	"""The name of the individual or entity who will own the given security."""
	investmentEntity: String
	"""A system generated timestamp upon the creation of the order."""
	createdAt: DateTime
	"""A timestamp of the last update to the order."""
	lastUpdatedAt: DateTime
	"""WIP"""
	lastActivityAt: DateTime
	"""A timestamp of the last reminder sent to user(s)."""
	lastReminderSentAt: DateTime
	"""A timestamp of the last re-invitation sent to user(s)."""
	lastReinvitationSentAt: DateTime
	"""A timestamp of when the order was submitted."""
	submissionDate: DateTime
	seenByUsers: [UserRef!]
	tags: [TagRef!]
	close: CloseRef
	"""The status of the order."""
	status: OrderStatus
	"""An optional pre-emptive value provided by the fund based upon previous conversations or expressed interest amount from the data room or other means."""
	estimatedCommitmentAmount: Float
	"""The commitment amount identified in the submitted subscription document."""
	submittedCommitmentAmount: Float
	"""The commitment amount accepted by the Fund post review."""
	acceptedCommitmentAmount: Float
	commitmentAmounts: [SubFundCommitmentAmountRef!]
	referenceDocs: [FileRef!]
	customData: [CustomDataRef!]
	formValues: [FormQuestionDataRef!]
	"""The completion progress of the investor based upon which fields in the subscription form are required."""
	formFillingProgress: Float
	"""Required fields from the subscription form that have not yet been completed."""
	missingRequiredFields: Int
	"""Recommended fields from the subscription form that have not yet been completed."""
	missingRecommendedFields: Int
	supportingDocs: [SupportingDocumentRef!]
	supportingDocSignatureRequests: [SignatureRequestRef!]
	submittedDocs: [OrderDocumentRef!]
	uploadedCountersignedDocs: [OrderDocumentRef!]
	countersignedDocs: [OrderDocumentRef!]
	countersignSignatureRequests: [SignatureRequestRef!]
	emailBouncedContacts: [UserRef!]
	amlKycReviews: [AmlKycReviewRef!]
	unsignedSubscriptionReview: SubscriptionDocReviewInfoRef
	signedSubscriptionReview: SubscriptionDocReviewInfoRef
	"""AML/KYC documents that this investor provided offline"""
	amlKycDocsProvidedOffline: [String!]
	metadata: [MetadataFieldRef!]
	clients: [ClientRef!]
	subDocDataExtractionRequest: DataExtractionRequestRef
	amlCheck: [AmlCheckRef!]
	"""Investor wet signed the subscription doc and uploaded the documents."""
	investorSignedOnPaper: Boolean
	clientFormCompareData: [ClientFormCompareDataRef!]
	investorGroup: InvestorGroupRef
	advisorEntity: AdvisorEntityRef
	"""The status of the side letter (feature is in development)"""
	sideLetterStatus: SideLetterStatus
}

input OrderStatus_hash {
	eq: OrderStatus
	in: [OrderStatus]
}

input SignatureRequestFilter {
	id: StringHashFilter
	has: [SignatureRequestHasFilter]
	and: [SignatureRequestFilter]
	or: [SignatureRequestFilter]
	not: SignatureRequestFilter
}

input SignatureRequestOrder {
	asc: SignatureRequestOrderable
	desc: SignatureRequestOrderable
	then: SignatureRequestOrder
}

input SignatureRequestPatch {
	status: SignatureRequestStatus
	requesterOpt: UserRef
	requestedAtOpt: DateTime
}

input SignatureRequestRef {
	id: String
	status: SignatureRequestStatus
	requesterOpt: UserRef
	requestedAtOpt: DateTime
}

input SingleValueObjFilter {
	has: [SingleValueObjHasFilter]
	and: [SingleValueObjFilter]
	or: [SingleValueObjFilter]
	not: SingleValueObjFilter
}

input SingleValueObjOrder {
	asc: SingleValueObjOrderable
	desc: SingleValueObjOrderable
	then: SingleValueObjOrder
}

input SingleValueObjRef {
	singleValue: String
}

input StringObjFilter {
	has: [StringObjHasFilter]
	and: [StringObjFilter]
	or: [StringObjFilter]
	not: StringObjFilter
}

input StringObjOrder {
	asc: StringObjOrderable
	desc: StringObjOrderable
	then: StringObjOrder
}

input StringObjRef {
	stringValue: String
}

input SubFundCommitmentAmountFilter {
	id: [ID!]
	has: [SubFundCommitmentAmountHasFilter]
	and: [SubFundCommitmentAmountFilter]
	or: [SubFundCommitmentAmountFilter]
	not: SubFundCommitmentAmountFilter
}

input SubFundCommitmentAmountOrder {
	asc: SubFundCommitmentAmountOrderable
	desc: SubFundCommitmentAmountOrderable
	then: SubFundCommitmentAmountOrder
}

input SubFundCommitmentAmountPatch {
	subFund: SubFundRef
	"""An optional pre-emptive value provided by the fund based upon previous conversations or expressed interest amount from the data room or other means."""
	estimatedCommitmentAmount: Float
	"""The commitment amount identified in the submitted subscription document."""
	submittedCommitmentAmount: Float
	"""The commitment amount accepted by the Fund post review."""
	acceptedCommitmentAmount: Float
}

input SubFundCommitmentAmountRef {
	id: ID
	subFund: SubFundRef
	"""An optional pre-emptive value provided by the fund based upon previous conversations or expressed interest amount from the data room or other means."""
	estimatedCommitmentAmount: Float
	"""The commitment amount identified in the submitted subscription document."""
	submittedCommitmentAmount: Float
	"""The commitment amount accepted by the Fund post review."""
	acceptedCommitmentAmount: Float
}

input SubFundFilter {
	id: StringHashFilter
	has: [SubFundHasFilter]
	and: [SubFundFilter]
	or: [SubFundFilter]
	not: SubFundFilter
}

input SubFundOrder {
	asc: SubFundOrderable
	desc: SubFundOrderable
	then: SubFundOrder
}

input SubFundPatch {
	customId: String
	masterFund: FundSubscriptionRef
	currency: String
	name: String
}

input SubFundRef {
	id: String
	customId: String
	masterFund: FundSubscriptionRef
	currency: String
	name: String
}

input SubscriptionDocReviewInfoFilter {
	id: StringHashFilter
	has: [SubscriptionDocReviewInfoHasFilter]
	and: [SubscriptionDocReviewInfoFilter]
	or: [SubscriptionDocReviewInfoFilter]
	not: SubscriptionDocReviewInfoFilter
}

input SubscriptionDocReviewInfoOrder {
	asc: SubscriptionDocReviewInfoOrderable
	desc: SubscriptionDocReviewInfoOrderable
	then: SubscriptionDocReviewInfoOrder
}

input SubscriptionDocReviewInfoPatch {
	versionIndex: Int
	status: ReviewStatus
	"""For internal use only!"""
	currentPendingStepId: String
}

input SubscriptionDocReviewInfoRef {
	id: String
	versionIndex: Int
	status: ReviewStatus
	"""For internal use only!"""
	currentPendingStepId: String
}

input SupportingDocumentFilter {
	id: [ID!]
	has: [SupportingDocumentHasFilter]
	and: [SupportingDocumentFilter]
	or: [SupportingDocumentFilter]
	not: SupportingDocumentFilter
}

input SupportingDocumentOrder {
	asc: SupportingDocumentOrderable
	desc: SupportingDocumentOrderable
	then: SupportingDocumentOrder
}

input SupportingDocumentPatch {
	order: OrderRef
	"""The name of the document requested per the corresponding FormQuestionData."""
	name: String
	"""An indicator field, indicating the relevant of a given document requirement.  Marking the document as NA, removes it from the requirement list for a given order."""
	markedAsNa: Boolean
	"""A flag to quickly check it this document is submitted i.e. submittedDocs is non-empty."""
	submitted: Boolean
	submittedDocs: [OrderDocumentRef!]
}

input SupportingDocumentRef {
	"""A system generated value provided at the time the Document is indicated as required."""
	id: ID
	order: OrderRef
	"""The name of the document requested per the corresponding FormQuestionData."""
	name: String
	"""An indicator field, indicating the relevant of a given document requirement.  Marking the document as NA, removes it from the requirement list for a given order."""
	markedAsNa: Boolean
	"""A flag to quickly check it this document is submitted i.e. submittedDocs is non-empty."""
	submitted: Boolean
	submittedDocs: [OrderDocumentRef!]
}

input TagFilter {
	id: StringHashFilter
	name: StringHashFilter
	has: [TagHasFilter]
	and: [TagFilter]
	or: [TagFilter]
	not: TagFilter
}

input TagOrder {
	asc: TagOrderable
	desc: TagOrderable
	then: TagOrder
}

input TagPatch {
	fund: FundSubscriptionRef
	orders: [OrderRef]
	"""The label of the Tag applied upon creation. This is the value actually illustrated on the platform."""
	name: String
	creator: UserRef
	"""The time at which the Tag was created."""
	createdAt: DateTime
}

input TagRef {
	"""A system generated value provided at the time the Tag is created."""
	id: String
	fund: FundSubscriptionRef
	orders: [OrderRef]
	"""The label of the Tag applied upon creation. This is the value actually illustrated on the platform."""
	name: String
	creator: UserRef
	"""The time at which the Tag was created."""
	createdAt: DateTime
}

input TemplateColumnFilter {
	id: [ID!]
	has: [TemplateColumnHasFilter]
	and: [TemplateColumnFilter]
	or: [TemplateColumnFilter]
	not: TemplateColumnFilter
}

input TemplateColumnOrder {
	asc: TemplateColumnOrderable
	desc: TemplateColumnOrderable
	then: TemplateColumnOrder
}

input TemplateColumnPatch {
	"""Order of this column in the spreadsheet"""
	index: Int
	"""The column title."""
	title: String
}

input TemplateColumnRef {
	id: ID
	"""Order of this column in the spreadsheet"""
	index: Int
	"""The column title."""
	title: String
}

input TemplateFilter {
	id: StringHashFilter
	has: [TemplateHasFilter]
	and: [TemplateFilter]
	or: [TemplateFilter]
	not: TemplateFilter
}

input TemplateOrder {
	asc: TemplateOrderable
	desc: TemplateOrderable
	then: TemplateOrder
}

input TemplatePatch {
	"""Name of the template."""
	name: String
	"""Description about the last update."""
	templateType: TemplateType
	"""Description about the last update."""
	lastChangeDescription: String
	spreadsheetFile: FileRef
	columns: [TemplateColumnRef!]
	createdBy: UserRef
	"""A system generated timestamp upon the creation of the template."""
	createdAt: DateTime
	"""A timestamp of the last update to the template."""
	lastUpdatedAt: DateTime
}

input TemplateRef {
	id: String
	"""Name of the template."""
	name: String
	"""Description about the last update."""
	templateType: TemplateType
	"""Description about the last update."""
	lastChangeDescription: String
	spreadsheetFile: FileRef
	columns: [TemplateColumnRef!]
	createdBy: UserRef
	"""A system generated timestamp upon the creation of the template."""
	createdAt: DateTime
	"""A timestamp of the last update to the template."""
	lastUpdatedAt: DateTime
}

input UpdateAdvisorEntityInput {
	filter: AdvisorEntityFilter!
	set: AdvisorEntityPatch
	remove: AdvisorEntityPatch
}

input UpdateAdvisorTagInput {
	filter: AdvisorTagFilter!
	set: AdvisorTagPatch
	remove: AdvisorTagPatch
}

input UpdateAmlCheckInput {
	filter: AmlCheckFilter!
	set: AmlCheckPatch
	remove: AmlCheckPatch
}

input UpdateAmlKycReviewInput {
	filter: AmlKycReviewFilter!
	set: AmlKycReviewPatch
	remove: AmlKycReviewPatch
}

input UpdateClientFormCompareDataInput {
	filter: ClientFormCompareDataFilter!
	set: ClientFormCompareDataPatch
	remove: ClientFormCompareDataPatch
}

input UpdateClientInput {
	filter: ClientFilter!
	set: ClientPatch
	remove: ClientPatch
}

input UpdateCloseInput {
	filter: CloseFilter!
	set: ClosePatch
	remove: ClosePatch
}

input UpdateCustomDataInput {
	filter: CustomDataFilter!
	set: CustomDataPatch
	remove: CustomDataPatch
}

input UpdateDataExtractionRequestInput {
	filter: DataExtractionRequestFilter!
	set: DataExtractionRequestPatch
	remove: DataExtractionRequestPatch
}

input UpdateFileInput {
	filter: FileFilter!
	set: FilePatch
	remove: FilePatch
}

input UpdateFormQuestionDataInput {
	filter: FormQuestionDataFilter!
	set: FormQuestionDataPatch
	remove: FormQuestionDataPatch
}

input UpdateFundSubscriptionInput {
	filter: FundSubscriptionFilter!
	set: FundSubscriptionPatch
	remove: FundSubscriptionPatch
}

input UpdateInvestorGroupInput {
	filter: InvestorGroupFilter!
	set: InvestorGroupPatch
	remove: InvestorGroupPatch
}

input UpdateMetadataFieldInput {
	filter: MetadataFieldFilter!
	set: MetadataFieldPatch
	remove: MetadataFieldPatch
}

input UpdateOrderDocumentInput {
	filter: OrderDocumentFilter!
	set: OrderDocumentPatch
	remove: OrderDocumentPatch
}

input UpdateOrderInput {
	filter: OrderFilter!
	set: OrderPatch
	remove: OrderPatch
}

input UpdateSignatureRequestInput {
	filter: SignatureRequestFilter!
	set: SignatureRequestPatch
	remove: SignatureRequestPatch
}

input UpdateSubFundCommitmentAmountInput {
	filter: SubFundCommitmentAmountFilter!
	set: SubFundCommitmentAmountPatch
	remove: SubFundCommitmentAmountPatch
}

input UpdateSubFundInput {
	filter: SubFundFilter!
	set: SubFundPatch
	remove: SubFundPatch
}

input UpdateSubscriptionDocReviewInfoInput {
	filter: SubscriptionDocReviewInfoFilter!
	set: SubscriptionDocReviewInfoPatch
	remove: SubscriptionDocReviewInfoPatch
}

input UpdateSupportingDocumentInput {
	filter: SupportingDocumentFilter!
	set: SupportingDocumentPatch
	remove: SupportingDocumentPatch
}

input UpdateTagInput {
	filter: TagFilter!
	set: TagPatch
	remove: TagPatch
}

input UpdateTemplateColumnInput {
	filter: TemplateColumnFilter!
	set: TemplateColumnPatch
	remove: TemplateColumnPatch
}

input UpdateTemplateInput {
	filter: TemplateFilter!
	set: TemplatePatch
	remove: TemplatePatch
}

input UpdateUserInput {
	filter: UserFilter!
	set: UserPatch
	remove: UserPatch
}

input UserFilter {
	id: StringHashFilter
	email: StringHashFilter
	has: [UserHasFilter]
	and: [UserFilter]
	or: [UserFilter]
	not: UserFilter
}

input UserOrder {
	asc: UserOrderable
	desc: UserOrderable
	then: UserOrder
}

input UserPatch {
	"""The first name of the user on the profile"""
	firstName: String
	"""The first name of the user on the profile"""
	lastName: String
}

input UserRef {
	"""A system generated value provided at the time the user is created."""
	id: String
	"""The email address associated with a given user profile"""
	email: String
	"""The first name of the user on the profile"""
	firstName: String
	"""The first name of the user on the profile"""
	lastName: String
}

#######################
# Generated Query
#######################

type Query {
	getFundSubscription(id: String!): FundSubscription
	queryFundSubscription(filter: FundSubscriptionFilter, order: FundSubscriptionOrder, first: Int, offset: Int): [FundSubscription]
	getOrder(id: String!): Order
	queryOrder(filter: OrderFilter, order: OrderOrder, first: Int, offset: Int): [Order]
	getUser(id: String, email: String): User
	getTag(id: String!): Tag
	queryTag(filter: TagFilter, order: TagOrder, first: Int, offset: Int): [Tag]
	getClose(id: String!): Close
	getSupportingDocument(id: ID!): SupportingDocument
	getFile(id: String!): File
	getSignatureRequest(id: String!): SignatureRequest
	getOrderDocument(id: ID!): OrderDocument
	queryOrderDocument(filter: OrderDocumentFilter, first: Int, offset: Int): [OrderDocument]
	getTemplate(id: String!): Template
	getAmlKycReview(id: ID!): AmlKycReview
	getSubscriptionDocReviewInfo(id: String!): SubscriptionDocReviewInfo
	getMetadataField(id: ID!): MetadataField
	getClient(id: String!): Client
	getDataExtractionRequest(id: String!): DataExtractionRequest
	getAmlCheck(id: String!): AmlCheck
	getSubFund(id: String!): SubFund
	querySubFund(filter: SubFundFilter, order: SubFundOrder, first: Int, offset: Int): [SubFund]
	getSubFundCommitmentAmount(id: ID!): SubFundCommitmentAmount
	getClientFormCompareData(id: ID!): ClientFormCompareData
	getInvestorGroup(id: String!): InvestorGroup
	getAdvisorEntity(id: String!): AdvisorEntity
}

#######################
# Generated Mutations
#######################

type Mutation {
	addFundSubscription(input: [AddFundSubscriptionInput!]!, upsert: Boolean): AddFundSubscriptionPayload
	updateFundSubscription(input: UpdateFundSubscriptionInput!): UpdateFundSubscriptionPayload
	deleteFundSubscription(filter: FundSubscriptionFilter!): DeleteFundSubscriptionPayload
	addOrder(input: [AddOrderInput!]!, upsert: Boolean): AddOrderPayload
	updateOrder(input: UpdateOrderInput!): UpdateOrderPayload
	deleteOrder(filter: OrderFilter!): DeleteOrderPayload
	addUser(input: [AddUserInput!]!, upsert: Boolean): AddUserPayload
	updateUser(input: UpdateUserInput!): UpdateUserPayload
	deleteUser(filter: UserFilter!): DeleteUserPayload
	addTag(input: [AddTagInput!]!, upsert: Boolean): AddTagPayload
	updateTag(input: UpdateTagInput!): UpdateTagPayload
	deleteTag(filter: TagFilter!): DeleteTagPayload
	addClose(input: [AddCloseInput!]!, upsert: Boolean): AddClosePayload
	updateClose(input: UpdateCloseInput!): UpdateClosePayload
	deleteClose(filter: CloseFilter!): DeleteClosePayload
	addSupportingDocument(input: [AddSupportingDocumentInput!]!): AddSupportingDocumentPayload
	updateSupportingDocument(input: UpdateSupportingDocumentInput!): UpdateSupportingDocumentPayload
	deleteSupportingDocument(filter: SupportingDocumentFilter!): DeleteSupportingDocumentPayload
	addFile(input: [AddFileInput!]!, upsert: Boolean): AddFilePayload
	updateFile(input: UpdateFileInput!): UpdateFilePayload
	deleteFile(filter: FileFilter!): DeleteFilePayload
	addSignatureRequest(input: [AddSignatureRequestInput!]!, upsert: Boolean): AddSignatureRequestPayload
	updateSignatureRequest(input: UpdateSignatureRequestInput!): UpdateSignatureRequestPayload
	deleteSignatureRequest(filter: SignatureRequestFilter!): DeleteSignatureRequestPayload
	addCustomData(input: [AddCustomDataInput!]!): AddCustomDataPayload
	updateCustomData(input: UpdateCustomDataInput!): UpdateCustomDataPayload
	deleteCustomData(filter: CustomDataFilter!): DeleteCustomDataPayload
	addFormQuestionData(input: [AddFormQuestionDataInput!]!): AddFormQuestionDataPayload
	updateFormQuestionData(input: UpdateFormQuestionDataInput!): UpdateFormQuestionDataPayload
	deleteFormQuestionData(filter: FormQuestionDataFilter!): DeleteFormQuestionDataPayload
	addOrderDocument(input: [AddOrderDocumentInput!]!): AddOrderDocumentPayload
	updateOrderDocument(input: UpdateOrderDocumentInput!): UpdateOrderDocumentPayload
	deleteOrderDocument(filter: OrderDocumentFilter!): DeleteOrderDocumentPayload
	addTemplate(input: [AddTemplateInput!]!, upsert: Boolean): AddTemplatePayload
	updateTemplate(input: UpdateTemplateInput!): UpdateTemplatePayload
	deleteTemplate(filter: TemplateFilter!): DeleteTemplatePayload
	addTemplateColumn(input: [AddTemplateColumnInput!]!): AddTemplateColumnPayload
	updateTemplateColumn(input: UpdateTemplateColumnInput!): UpdateTemplateColumnPayload
	deleteTemplateColumn(filter: TemplateColumnFilter!): DeleteTemplateColumnPayload
	addAmlKycReview(input: [AddAmlKycReviewInput!]!): AddAmlKycReviewPayload
	updateAmlKycReview(input: UpdateAmlKycReviewInput!): UpdateAmlKycReviewPayload
	deleteAmlKycReview(filter: AmlKycReviewFilter!): DeleteAmlKycReviewPayload
	addSubscriptionDocReviewInfo(input: [AddSubscriptionDocReviewInfoInput!]!, upsert: Boolean): AddSubscriptionDocReviewInfoPayload
	updateSubscriptionDocReviewInfo(input: UpdateSubscriptionDocReviewInfoInput!): UpdateSubscriptionDocReviewInfoPayload
	deleteSubscriptionDocReviewInfo(filter: SubscriptionDocReviewInfoFilter!): DeleteSubscriptionDocReviewInfoPayload
	addMetadataField(input: [AddMetadataFieldInput!]!): AddMetadataFieldPayload
	updateMetadataField(input: UpdateMetadataFieldInput!): UpdateMetadataFieldPayload
	deleteMetadataField(filter: MetadataFieldFilter!): DeleteMetadataFieldPayload
	addClient(input: [AddClientInput!]!, upsert: Boolean): AddClientPayload
	updateClient(input: UpdateClientInput!): UpdateClientPayload
	deleteClient(filter: ClientFilter!): DeleteClientPayload
	addDataExtractionRequest(input: [AddDataExtractionRequestInput!]!, upsert: Boolean): AddDataExtractionRequestPayload
	updateDataExtractionRequest(input: UpdateDataExtractionRequestInput!): UpdateDataExtractionRequestPayload
	deleteDataExtractionRequest(filter: DataExtractionRequestFilter!): DeleteDataExtractionRequestPayload
	addAmlCheck(input: [AddAmlCheckInput!]!, upsert: Boolean): AddAmlCheckPayload
	updateAmlCheck(input: UpdateAmlCheckInput!): UpdateAmlCheckPayload
	deleteAmlCheck(filter: AmlCheckFilter!): DeleteAmlCheckPayload
	addSubFund(input: [AddSubFundInput!]!, upsert: Boolean): AddSubFundPayload
	updateSubFund(input: UpdateSubFundInput!): UpdateSubFundPayload
	deleteSubFund(filter: SubFundFilter!): DeleteSubFundPayload
	addSubFundCommitmentAmount(input: [AddSubFundCommitmentAmountInput!]!): AddSubFundCommitmentAmountPayload
	updateSubFundCommitmentAmount(input: UpdateSubFundCommitmentAmountInput!): UpdateSubFundCommitmentAmountPayload
	deleteSubFundCommitmentAmount(filter: SubFundCommitmentAmountFilter!): DeleteSubFundCommitmentAmountPayload
	addClientFormCompareData(input: [AddClientFormCompareDataInput!]!): AddClientFormCompareDataPayload
	updateClientFormCompareData(input: UpdateClientFormCompareDataInput!): UpdateClientFormCompareDataPayload
	deleteClientFormCompareData(filter: ClientFormCompareDataFilter!): DeleteClientFormCompareDataPayload
	addInvestorGroup(input: [AddInvestorGroupInput!]!, upsert: Boolean): AddInvestorGroupPayload
	updateInvestorGroup(input: UpdateInvestorGroupInput!): UpdateInvestorGroupPayload
	deleteInvestorGroup(filter: InvestorGroupFilter!): DeleteInvestorGroupPayload
	addAdvisorEntity(input: [AddAdvisorEntityInput!]!, upsert: Boolean): AddAdvisorEntityPayload
	updateAdvisorEntity(input: UpdateAdvisorEntityInput!): UpdateAdvisorEntityPayload
	deleteAdvisorEntity(filter: AdvisorEntityFilter!): DeleteAdvisorEntityPayload
	addAdvisorTag(input: [AddAdvisorTagInput!]!, upsert: Boolean): AddAdvisorTagPayload
	updateAdvisorTag(input: UpdateAdvisorTagInput!): UpdateAdvisorTagPayload
	deleteAdvisorTag(filter: AdvisorTagFilter!): DeleteAdvisorTagPayload
}

